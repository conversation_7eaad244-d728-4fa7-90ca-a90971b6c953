"""
HII区分子云距离估计项目主程序（单一文件夹版本）

用于估计HII区域宿主分子云的距离，通过分析不同区域（空腔、PDR、外部）
恒星的消光-距离关系。

此版本将所有输出保存在同一个文件夹中，文件名中包含源名称以确保唯一性。
所有图像标注使用英文以避免乱码问题。
"""

import os
import sys
import argparse
import yaml
from datetime import datetime
import numpy as np
from astropy.table import Table
from astropy.coordinates import SkyCoord
import astropy.units as u
from astropy.io import fits
import matplotlib.pyplot as plt

# 添加src目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import setup_logger
from src.utils.helpers import load_config, ensure_directory
from src.data_manager import (
    load_hii_region_catalog, load_gaia_data, load_wise_fits,
    load_wise_multiband, cross_match_catalogs, save_processed_data
)
from src.region_mapper import (
    process_wise_image, define_pdr_mask, define_cavity_mask,
    define_external_mask, get_region_tags, save_region_masks
)
from src.star_selector import (
    apply_quality_cuts, flag_ysos_wise, filter_star_sample
)
from src.extinction_estimator import compute_av_per_star
from src.distance_analyzer import (
    analyze_all_regions, estimate_cloud_distance, save_analysis_results
)
from src.visualizer import (
    plot_wise_with_regions_and_stars,
    plot_extinction_distance,
    plot_wise_rgb
)


# 设置日志记录器
logger = setup_logger(name='main_single_folder')

def parse_arguments():
    """
    解析命令行参数

    Returns:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='估计HII区域宿主分子云的距离')

    parser.add_argument('--source', type=str, required=True,
                       help='HII区域源名称')

    parser.add_argument('--config', type=str, default='config/default_config.yaml',
                       help='配置文件路径')

    parser.add_argument('--output-dir', type=str,
                       help='输出目录，如果不指定则使用配置文件中的设置')

    parser.add_argument('--ra', type=float,
                       help='赤经（度），如果不指定则从源表中查找')

    parser.add_argument('--dec', type=float,
                       help='赤纬（度），如果不指定则从源表中查找')

    parser.add_argument('--radius', type=float,
                       help='有效半径（度），如果不指定则从源表中查找')

    parser.add_argument('--skip-steps', type=str, nargs='+',
                       choices=['data_loading', 'region_mapping', 'star_selection',
                               'extinction_calculation', 'distance_analysis'],
                       help='跳过指定的处理步骤')

    return parser.parse_args()

def load_source_info(source_name, args, config):
    """
    加载源信息

    Args:
        source_name: 源名称
        args: 命令行参数
        config: 配置字典

    Returns:
        dict: 源信息
    """
    logger.info(f"加载源{source_name}的信息")

    # 如果命令行参数中提供了坐标和半径，直接使用
    if args.ra is not None and args.dec is not None and args.radius is not None:
        source_info = {
            'name': source_name,
            'ra': args.ra,
            'dec': args.dec,
            'r_eff': args.radius
        }
        logger.info(f"使用命令行参数: RA={source_info['ra']:.6f}, "
                   f"Dec={source_info['dec']:.6f}, R_eff={source_info['r_eff']:.6f}度")
        return source_info

    # 否则从源表中查找
    try:
        catalog = load_hii_region_catalog(config['data_paths']['hii_region_catalog'])

        # 查找源
        source_row = None
        for i, row in catalog.iterrows():
            if source_name.strip() == row['source_name'].strip():
                source_row = row
                break

        if source_row is None:
            logger.error(f"在源表中未找到{source_name}")
            raise ValueError(f"在源表中未找到{source_name}")

        # 从源名中解析银道坐标并转换为赤道坐标
        try:
            from src.utils.helpers import parse_galactic_coords_from_name, galactic_to_icrs

            # 解析银道坐标
            l, b = parse_galactic_coords_from_name(source_name)
            logger.info(f"从源名解析银道坐标: l={l:.6f}, b={b:.6f}")

            # 转换为赤道坐标
            ra_from_name, dec_from_name = galactic_to_icrs(l, b)
            logger.info(f"银道坐标转换为赤道坐标: RA={ra_from_name:.6f}, Dec={dec_from_name:.6f} (ICRS)")

            # 使用从源名解析的坐标
            ra = ra_from_name
            dec = dec_from_name

            # 获取有效半径（从角秒转换为度）
            r_eff = source_row['N'] / 3600.0  # 角秒转度

            source_info = {
                'name': source_name,
                'ra': ra,
                'dec': dec,
                'r_eff': r_eff
            }

            logger.info(f"从源表加载信息: RA={source_info['ra']:.6f}, "
                       f"Dec={source_info['dec']:.6f}, R_eff={source_info['r_eff']:.6f}度")

            return source_info

        except Exception as e:
            logger.error(f"解析源名中的坐标失败: {str(e)}")
            raise

    except Exception as e:
        logger.error(f"加载源信息失败: {str(e)}")
        raise

def process_hii_region(args):
    """
    处理HII区域

    Args:
        args: 命令行参数

    Returns:
        dict: 处理结果
    """
    # 加载配置
    config = load_config(args.config)

    # 设置输出目录
    if args.output_dir:
        output_dir = args.output_dir
    else:
        output_dir = config['output_paths']['results']

    ensure_directory(output_dir)
    logger.info(f"输出目录: {output_dir}")

    # 创建子目录
    processed_dir = os.path.join(output_dir, 'processed')
    vis_dir = os.path.join(output_dir, 'visualizations')
    reports_dir = os.path.join(output_dir, 'reports')

    ensure_directory(processed_dir)
    ensure_directory(vis_dir)
    ensure_directory(reports_dir)

    # 跳过的步骤
    skip_steps = args.skip_steps or []

    # 加载源信息
    source_info = load_source_info(args.source, args, config)

    # 创建中心坐标对象
    center_coord = SkyCoord(ra=source_info['ra']*u.degree,
                           dec=source_info['dec']*u.degree,
                           frame='icrs')

    # 步骤1：加载WISE数据
    if 'data_loading' not in skip_steps:
        logger.info("步骤1：加载WISE数据")

        # 加载WISE FITS图像
        wise_image, wise_header, wise_wcs = load_wise_fits(source_info['name'])

        logger.info("WISE数据加载完成")
    else:
        logger.info("跳过数据加载步骤")

        # 加载WISE FITS图像
        wise_image, wise_header, wise_wcs = load_wise_fits(source_info['name'])

    # 步骤2：区域映射
    if 'region_mapping' not in skip_steps:
        logger.info("步骤2：区域映射")

        # 处理WISE图像
        processed_wise = process_wise_image(wise_image, wise_header)

        # 定义PDR掩模
        # 从配置文件加载PDR参数
        pdr_params = config.get('region_mapping', {}).get('pdr', {})

        # 确定使用哪种PDR检测方法
        use_contour_method = pdr_params.get('use_contour_method', False)
        use_ratio_method = pdr_params.get('use_ratio_method', True)

        if use_ratio_method:
            logger.info("使用W3/W4波段比值方法定义PDR区域")
            pdr_mask = define_pdr_mask(
                processed_wise, wise_wcs, center_coord, source_info['r_eff'],
                threshold_factor=pdr_params.get('threshold_factor', 0.3),
                max_radius_factor=pdr_params.get('max_radius_factor', 5.0),
                search_radius_factor=pdr_params.get('search_radius_factor', 3.0),
                use_ratio_method=True,
                source_name=source_info['name'],
                output_dir=output_dir,
                save_ratio_plot=pdr_params.get('save_ratio_plot', True),
                config_path=args.config
            )
        elif use_contour_method:
            logger.info("使用等高线方法定义PDR区域")
            pdr_mask = define_pdr_mask(
                processed_wise, wise_wcs, center_coord, source_info['r_eff'],
                threshold_factor=pdr_params.get('threshold_factor', 0.3),
                max_radius_factor=pdr_params.get('max_radius_factor', 5.0),
                search_radius_factor=pdr_params.get('search_radius_factor', 3.0),
                use_contour_method=True,
                n_contours=pdr_params.get('n_contours', 10),
                contour_start_nsigma=pdr_params.get('contour_start_nsigma', 3.0),
                source_name=source_info['name'],
                output_dir=output_dir,
                save_contour_plot=True
            )
        else:
            logger.info("使用阈值方法定义PDR区域")
            pdr_mask = define_pdr_mask(
                processed_wise, wise_wcs, center_coord, source_info['r_eff'],
                threshold_factor=pdr_params.get('threshold_factor', 0.3),
                max_radius_factor=pdr_params.get('max_radius_factor', 5.0),
                search_radius_factor=pdr_params.get('search_radius_factor', 3.0)
            )

        # 定义空腔掩模
        cavity_mask = define_cavity_mask(
            pdr_mask, wise_wcs, center_coord, source_info['r_eff']
        )

        # 定义外部区域掩模
        external_mask = define_external_mask(
            pdr_mask, cavity_mask, wise_wcs, center_coord, source_info['r_eff']
        )

        # 保存区域掩模
        masks = {
            'cavity': cavity_mask,
            'pdr': pdr_mask,
            'external': external_mask
        }

        # 使用源名称作为文件名前缀
        save_region_masks(
            masks, source_info['name'], processed_dir
        )

        # 加载Gaia数据
        try:
            gaia_data = load_gaia_data(
                source_info['name'],
                source_info['ra'],
                source_info['dec'],
                source_info['r_eff'] * 5  # 搜索半径为R_eff的5倍
            )

            # 为恒星分配区域标签
            star_coords = SkyCoord(ra=gaia_data['ra']*u.degree,
                                  dec=gaia_data['dec']*u.degree)

            # 创建区域掩模字典
            region_masks = {
                'cavity': cavity_mask,
                'pdr': pdr_mask,
                'external': external_mask
            }

            # 使用get_region_tags函数获取区域标签
            region_tags = get_region_tags(star_coords, region_masks, wise_wcs)

            # 添加区域标签列
            gaia_data['region'] = region_tags

            # 保存带区域标签的Gaia数据
            gaia_file = save_processed_data(
                gaia_data,
                f"{source_info['name']}_gaia_regions.fits",
                processed_dir
            )

            logger.info("区域映射完成")
        except Exception as e:
            logger.error(f"加载Gaia数据失败: {str(e)}")
            logger.warning("将使用空的Gaia数据继续处理")

            # 创建一个空的Gaia数据表
            from astropy.table import Table, Column
            gaia_data = Table()
            # 添加必要的列
            gaia_data['ra'] = []
            gaia_data['dec'] = []
            gaia_data['parallax'] = []
            gaia_data['parallax_error'] = []
            gaia_data['pmra'] = []
            gaia_data['pmdec'] = []
            gaia_data['ruwe'] = []
            gaia_data['phot_g_mean_mag'] = []
            gaia_data['region'] = []

        logger.info("区域映射完成")
    else:
        logger.info("跳过区域映射步骤")

        # 从已处理的文件加载数据
        masks_file = os.path.join(processed_dir, f"{source_info['name']}_region_masks.fits")
        gaia_file = os.path.join(processed_dir, f"{source_info['name']}_gaia_regions.fits")

        if os.path.exists(masks_file):
            with fits.open(masks_file) as hdul:
                cavity_mask = hdul['CAVITY'].data.astype(bool)
                pdr_mask = hdul['PDR'].data.astype(bool)
                external_mask = hdul['EXTERNAL'].data.astype(bool)
        else:
            logger.warning(f"找不到区域掩模文件: {masks_file}，创建空掩模")
            # 创建空掩模
            cavity_mask = np.zeros_like(wise_image, dtype=bool)
            pdr_mask = np.zeros_like(wise_image, dtype=bool)
            external_mask = np.zeros_like(wise_image, dtype=bool)

        if os.path.exists(gaia_file):
            gaia_data = Table.read(gaia_file)
        else:
            logger.warning(f"找不到Gaia数据文件: {gaia_file}，创建空表")
            # 创建空表
            gaia_data = Table()
            gaia_data['ra'] = []
            gaia_data['dec'] = []
            gaia_data['parallax'] = []
            gaia_data['parallax_error'] = []
            gaia_data['pmra'] = []
            gaia_data['pmdec'] = []
            gaia_data['ruwe'] = []
            gaia_data['phot_g_mean_mag'] = []
            gaia_data['region'] = []

    # 步骤3：恒星选择
    if 'star_selection' not in skip_steps:
        logger.info("步骤3：恒星选择")

        # 应用质量筛选
        filtered_data, quality_flags = apply_quality_cuts(gaia_data)

        # 保存质量筛选后的数据
        filtered_data_file = save_processed_data(
            filtered_data,
            f"{source_info['name']}_filtered_data.fits",
            processed_dir
        )
        logger.info(f"保存质量筛选后的数据到: {filtered_data_file}")

        # 识别YSO
        yso_flags = flag_ysos_wise(filtered_data)

        # 筛选恒星样本
        star_sample = filter_star_sample(
            filtered_data, quality_flags=None, yso_flags=yso_flags
        )

        # 保存筛选后的恒星样本
        star_sample_file = save_processed_data(
            star_sample,
            f"{source_info['name']}_star_sample.fits",
            processed_dir
        )

        logger.info("恒星选择完成")
    else:
        logger.info("跳过恒星选择步骤")

        # 从已处理的文件加载数据
        star_sample_file = os.path.join(processed_dir, f"{source_info['name']}_star_sample.fits")

        if os.path.exists(star_sample_file):
            from astropy.table import Table
            star_sample = Table.read(star_sample_file)
        else:
            logger.warning(f"找不到筛选后的恒星样本文件: {star_sample_file}，创建空表")
            from astropy.table import Table
            star_sample = Table()
            star_sample['ra'] = []
            star_sample['dec'] = []

    # 步骤4：消光计算
    if 'extinction_calculation' not in skip_steps:
        logger.info("步骤4：消光计算")

        # 计算消光
        stars_with_av = compute_av_per_star(star_sample)

        # 保存带消光值的恒星样本
        av_file = save_processed_data(
            stars_with_av,
            f"{source_info['name']}_stars_with_av.fits",
            processed_dir
        )

        logger.info("消光计算完成")
    else:
        logger.info("跳过消光计算步骤")

        # 从已处理的文件加载数据
        av_file = os.path.join(processed_dir, f"{source_info['name']}_stars_with_av.fits")

        if os.path.exists(av_file):
            from astropy.table import Table
            stars_with_av = Table.read(av_file)
        else:
            logger.warning(f"找不到带消光值的恒星样本文件: {av_file}，创建空表")
            from astropy.table import Table
            stars_with_av = Table()
            stars_with_av['ra'] = []
            stars_with_av['dec'] = []
            stars_with_av['a_v'] = []
            stars_with_av['distance'] = []
            stars_with_av['region'] = []

    # 步骤5：距离分析
    if 'distance_analysis' not in skip_steps:
        logger.info("步骤5：距离分析")

        # 分析所有区域
        region_results = analyze_all_regions(stars_with_av, args.config, output_dir)

        # 估计分子云距离
        distance_estimate = estimate_cloud_distance(region_results)

        # 整合结果
        results = {
            'source_name': source_info['name'],
            'source_info': source_info,
            'region_results': region_results,
            'cloud_distance': distance_estimate.get('cloud_distance'),
            'distance_error': distance_estimate.get('distance_error'),
            'confidence': distance_estimate.get('confidence'),
            'evidence': distance_estimate.get('evidence')
        }

        # 保存分析结果 - 使用源名称作为文件名前缀
        results_file = os.path.join(reports_dir, f"{source_info['name']}_results.txt")
        with open(results_file, 'w', encoding='utf-8') as f:
            f.write(f"{source_info['name']} 分析结果\n")
            f.write("="*50 + "\n\n")

            # 源信息
            f.write("源信息:\n")
            f.write(f"  名称: {source_info['name']}\n")
            f.write(f"  坐标: RA={source_info['ra']:.6f}, Dec={source_info['dec']:.6f} (ICRS)\n")
            f.write(f"  有效半径: {source_info['r_eff']*3600:.1f} 角秒 ({source_info['r_eff']:.6f} 度)\n\n")

            # 距离估计结果
            if results['cloud_distance'] is not None:
                f.write("分子云距离估计:\n")
                f.write(f"  距离: {results['cloud_distance']:.0f} ± {results['distance_error']:.0f} pc\n")
                f.write(f"  置信度: {results['confidence']}\n\n")

                f.write("证据:\n")
                for e in results['evidence']:
                    f.write(f"- {e}\n")
            else:
                f.write("未能估计分子云距离\n")

            # 各区域的跳变信息
            f.write("\n各区域跳变信息:\n")
            for region in ['cavity', 'pdr', 'external']:
                if region in results['region_results']:
                    region_result = results['region_results'][region]
                    f.write(f"\n{region.upper()} 区域:\n")
                    f.write(f"- 有效恒星数: {region_result['valid_count']}\n")

                    if region_result['jumps']:
                        f.write(f"- 检测到的跳变:\n")
                        for i, jump in enumerate(region_result['jumps']):
                            f.write(f"  {i+1}. 距离: {jump['distance']:.0f} pc, "
                                  f"高度: {jump['height']:.2f} mag, "
                                  f"显著性: {jump['significance']:.1f}\n")
                    else:
                        f.write("- 未检测到显著跳变\n")

        logger.info("距离分析完成")
    else:
        logger.info("跳过距离分析步骤")

        # 这里可以加载已有的分析结果
        results = {
            'source_name': source_info['name'],
            'source_info': source_info,
            'message': "跳过了距离分析步骤，没有距离估计结果"
        }

    # 步骤6：可视化和报告生成
    logger.info("步骤6：可视化和报告生成")

    # 如果跳过了前面的步骤，需要创建一个空的stars_with_av对象
    if 'stars_with_av' not in locals() or stars_with_av is None:
        logger.info("创建空的stars_with_av对象用于可视化")
        stars_with_av = Table()
        # 添加必要的列
        stars_with_av['ra'] = []
        stars_with_av['dec'] = []
        stars_with_av['a_v'] = []
        stars_with_av['distance'] = []
        stars_with_av['region'] = []

    try:
        # 1. 以12μm数据为背景，画出PDR区的轮廓，以及Gaia星的分布
        wise_vis_path = os.path.join(vis_dir, f"{source_info['name']}_wise_regions_stars.png")

        # 加载WISE图像和区域掩模
        wise_image, wise_header, wise_wcs = load_wise_fits(source_info['name'])

        # 加载区域掩模
        masks_file = os.path.join(processed_dir, f"{source_info['name']}_region_masks.fits")
        if os.path.exists(masks_file):
            with fits.open(masks_file) as hdul:
                cavity_mask = hdul['CAVITY'].data.astype(bool)
                pdr_mask = hdul['PDR'].data.astype(bool)
                external_mask = hdul['EXTERNAL'].data.astype(bool)

                masks = {
                    'cavity': cavity_mask,
                    'pdr': pdr_mask,
                    'external': external_mask
                }

                # 创建中心坐标对象
                center_coord = SkyCoord(ra=source_info['ra']*u.degree,
                                      dec=source_info['dec']*u.degree)

                # 绘制WISE图像、区域轮廓和恒星位置
                plot_wise_with_regions_and_stars(
                    wise_image, wise_header, masks, stars_with_av,
                    wise_vis_path,
                    title=f"{source_info['name']} - WISE 12μm with Region Contours and Stars",
                    r_eff=source_info['r_eff'],
                    center_coord=center_coord
                )

                logger.info(f"成功保存WISE图像和区域掩模到: {wise_vis_path}")

                # 1.1 创建WISE三波段RGB图像
                try:
                    # 加载多波段WISE数据
                    wise_rgb_path = os.path.join(vis_dir, f"{source_info['name']}_wise_rgb.png")
                    logger.info(f"加载WISE多波段数据用于RGB图像生成")

                    wise_data, wise_rgb_wcs = load_wise_multiband(
                        source_info['name'], bands=('3.4', '12', '22'))

                    # 检查是否成功加载了所有波段
                    if 'w1' in wise_data and 'w3' in wise_data and 'w4' in wise_data:
                        # 绘制RGB图像
                        plot_wise_rgb(
                            wise_data, wise_rgb_wcs, wise_rgb_path,
                            title=f"{source_info['name']}",  # 只使用源名作为标题
                            r_eff=source_info['r_eff'],
                            center_coord=center_coord,
                            masks=masks,
                            star_table=stars_with_av
                        )
                        logger.info(f"成功保存WISE RGB图像到: {wise_rgb_path}")
                    else:
                        logger.warning("未能加载所有需要的WISE波段数据，跳过RGB图像生成")
                except Exception as e:
                    logger.error(f"生成WISE RGB图像失败: {str(e)}")
        else:
            logger.warning(f"找不到区域掩模文件: {masks_file}")

        # 2. 三个区域（空腔、PDR、外部）的消光-距离散点图
        if len(stars_with_av) > 0 and 'a_v' in stars_with_av.colnames and 'distance' in stars_with_av.colnames:
            ext_dist_path = os.path.join(vis_dir, f"{source_info['name']}_extinction_distance.png")

            # 绘制消光-距离散点图
            plot_extinction_distance(
                stars_with_av, ext_dist_path,
                title=f"{source_info['name']} - Extinction-Distance Relation"  # 使用英文标题
            )

            logger.info(f"成功保存消光-距离散点图到: {ext_dist_path}")
        else:
            logger.warning("没有足够的数据绘制消光-距离散点图")

    except Exception as e:
        logger.error(f"可视化失败: {str(e)}")
        logger.warning("继续处理，但可视化结果可能不完整")

    try:
        # 生成处理报告
        # 收集原始Gaia数据
        original_gaia_data = gaia_data

        # 收集质量筛选后的数据
        filtered_data_for_report = None
        filtered_data_file = os.path.join(processed_dir, f"{source_info['name']}_filtered_data.fits")
        if os.path.exists(filtered_data_file):
            from astropy.table import Table
            filtered_data_for_report = Table.read(filtered_data_file)

        # 创建详细的处理统计信息
        processing_stats = {
            'gaia_stats': {
                'initial_count': len(original_gaia_data) if original_gaia_data is not None else 0,
                'after_quality_cuts': len(filtered_data_for_report) if filtered_data_for_report is not None else 0,
                'final_count': len(star_sample) if star_sample is not None else 0
            },
            'extinction_stats': {
                'valid_av_count': np.sum(~np.isnan(stars_with_av['a_v'])) if stars_with_av is not None and 'a_v' in stars_with_av.colnames else 0
            },
            'distance_stats': {
                'final_distance': results.get('cloud_distance'),
                'distance_error': results.get('distance_error'),
                'confidence': results.get('confidence'),
                'evidence': results.get('evidence', [])
            }
        }

        # 生成详细的文本报告
        report_path = os.path.join(reports_dir, f"{source_info['name']}_report.txt")
        with open(report_path, 'w', encoding='utf-8-sig') as f:
            f.write(f"{source_info['name']} Processing Report\n")  # 使用英文标题
            f.write("="*50 + "\n\n")

            # 源信息
            f.write("Source Information:\n")  # 使用英文标题
            f.write(f"  Name: {source_info['name']}\n")
            f.write(f"  Coordinates: RA={source_info['ra']:.6f}, Dec={source_info['dec']:.6f} (ICRS)\n")
            f.write(f"  Effective Radius: {source_info['r_eff']*3600:.1f} arcsec ({source_info['r_eff']:.6f} deg)\n\n")

            # 处理参数
            f.write("Processing Parameters:\n")  # 使用英文标题
            # 从配置文件加载参数
            config = load_config(args.config)

            # PDR区域定义参数
            pdr_params = config.get('region_mapping', {}).get('pdr', {})
            use_contour_method = pdr_params.get('use_contour_method', False)
            use_ratio_method = pdr_params.get('use_ratio_method', True)

            f.write("  PDR Region Definition:\n")
            if use_ratio_method:
                f.write("    Method: W3/W4 band ratio\n")
                f.write(f"    Threshold Factor: {pdr_params.get('threshold_factor', 0.3)}\n")
                f.write(f"    Max Radius Factor: {pdr_params.get('max_radius_factor', 5.0)}\n")
                f.write(f"    Search Radius Factor: {pdr_params.get('search_radius_factor', 3.0)}\n")
            elif use_contour_method:
                f.write("    Method: Contour\n")
                f.write(f"    Number of Contours: {pdr_params.get('n_contours', 10)}\n")
                f.write(f"    Contour Start N-sigma: {pdr_params.get('contour_start_nsigma', 3.0)}\n")
            else:
                f.write("    Method: Threshold\n")
                f.write(f"    Threshold Factor: {pdr_params.get('threshold_factor', 0.3)}\n")

            # Gaia质量筛选参数
            gaia_quality = config.get('processing', {}).get('gaia_quality', {})
            f.write("\n  Gaia Quality Filtering:\n")
            f.write(f"    Parallax SNR Min: {gaia_quality.get('parallax_snr_min', 5.0)}\n")
            f.write(f"    RUWE Max: {gaia_quality.get('ruwe_max', 1.4)}\n\n")

            # 消光计算参数
            ext_params = config.get('extinction', {})
            f.write("  Extinction Calculation:\n")
            f.write(f"    Method: {ext_params.get('method', 'nir')}\n")
            f.write(f"    Rv Value: {ext_params.get('rv', 3.1)}\n\n")

            # 处理统计
            f.write("Processing Statistics:\n")

            # Gaia数据统计
            gaia_stats = processing_stats['gaia_stats']
            f.write("  Gaia Data:\n")
            f.write(f"    Initial Star Count: {gaia_stats.get('initial_count', 0)}\n")
            f.write(f"    After Quality Cuts: {gaia_stats.get('after_quality_cuts', 0)}\n")
            f.write(f"    Final Star Sample: {gaia_stats.get('final_count', 0)}\n\n")

            # 消光统计
            ext_stats = processing_stats['extinction_stats']
            f.write("  Extinction:\n")
            f.write(f"    Stars with Valid Av: {ext_stats.get('valid_av_count', 0)}\n\n")

            # 距离分析结果
            dist_stats = processing_stats['distance_stats']
            f.write("  Distance Analysis:\n")
            if dist_stats.get('final_distance') is not None:
                f.write(f"    Cloud Distance: {dist_stats.get('final_distance', 0):.0f} ± {dist_stats.get('distance_error', 0):.0f} pc\n")
                f.write(f"    Confidence: {dist_stats.get('confidence', 'unknown')}\n\n")

                f.write("    Evidence:\n")
                for evidence in dist_stats.get('evidence', []):
                    f.write(f"      - {evidence}\n")
            else:
                f.write("    No valid distance estimate\n")

        logger.info(f"成功保存处理报告到: {report_path}")

    except Exception as e:
        logger.error(f"生成报告失败: {str(e)}")
        logger.warning("继续处理，但报告可能不完整")

    logger.info(f"处理{source_info['name']}完成")
    return results

def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_arguments()

    try:
        # 处理HII区域
        results = process_hii_region(args)

        # 输出结果摘要
        if 'cloud_distance' in results and results['cloud_distance'] is not None:
            print(f"\n结果摘要:")
            print(f"源: {results['source_name']}")
            print(f"分子云距离估计: {results['cloud_distance']:.0f} ± {results['distance_error']:.0f} pc")
            print(f"置信度: {results['confidence']}")
            print("\n证据:")
            for e in results['evidence']:
                print(f"- {e}")
        else:
            print(f"\n未能估计{results['source_name']}的分子云距离")

        return 0

    except Exception as e:
        logger.error(f"处理失败: {str(e)}", exc_info=True)
        print(f"错误: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
