处理时间: 2025-05-20 09:52:12
耗时: 17.66秒

标准输出:

结果摘要:
源: G006.445-00.254
分子云距离估计: 2111 ± 50 pc
置信度: low

证据:
- Using most significant PDR jump at 2111pc (ΔA_V=16.19, significance=2.8)


标准错误:
2025-05-20 09:51:58,073 - main_single_folder - INFO - 输出目录: results/batch_single_folder
2025-05-20 09:51:58,073 - main_single_folder - INFO - 加载源G006.445-00.254的信息
2025-05-20 09:51:58,074 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
2025-05-20 09:51:58,079 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-05-20 09:51:58,081 - main_single_folder - INFO - 从源名解析银道坐标: l=6.445000, b=-0.254000
2025-05-20 09:51:58,086 - main_single_folder - INFO - 银道坐标转换为赤道坐标: RA=270.298741, Dec=-23.512335 (ICRS)
2025-05-20 09:51:58,087 - main_single_folder - INFO - 从源表加载信息: RA=270.298741, Dec=-23.512335, R_eff=0.004444度
2025-05-20 09:51:58,087 - main_single_folder - INFO - 步骤1：加载WISE数据
2025-05-20 09:51:58,092 - data_manager - INFO - 为源G006.445-00.254加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.445-00.254
2025-05-20 09:51:58,093 - data_manager - INFO - 目录中的所有文件: ['G006.445-00.254_ATLASGAL_870um.fits', 'G006.445-00.254_IRIS_100.fits', 'G006.445-00.254_MIPSGAL_24um.fits', 'G006.445-00.254_NVSS.fits', 'G006.445-00.254_WISE_12.fits', 'G006.445-00.254_WISE_22.fits', 'G006.445-00.254_WISE_3.4.fits', 'G006.445-00.254_WISE_4.6.fits']
2025-05-20 09:51:58,093 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:51:58,093 - data_manager - INFO - 第一次匹配结果: ['G006.445-00.254_WISE_12.fits']
2025-05-20 09:51:58,093 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.445-00.254\G006.445-00.254_WISE_12.fits
2025-05-20 09:51:58,156 - data_manager - INFO - FITS数据统计: 最小值=1532.3812255859375, 最大值=7529.916015625, 均值=2062.9326171875, 中位数=2037.5260009765625
2025-05-20 09:51:58,156 - data_manager - INFO - 有效数据点数量: 33856/33856 (100.00%)
2025-05-20 09:51:58,164 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-05-20 09:51:58,164 - main_single_folder - INFO - WISE数据加载完成
2025-05-20 09:51:58,164 - main_single_folder - INFO - 步骤2：区域映射
2025-05-20 09:51:58,166 - main_single_folder - INFO - 使用W3/W4波段比值方法定义PDR区域
2025-05-20 09:51:58,166 - region_mapper - INFO - 使用W3/W4波段比值方法定义PDR掩模
2025-05-20 09:51:58,166 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:51:58,166 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:51:58,167 - region_mapper.ratio - INFO - 加载源G006.445-00.254的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:51:58,167 - region_mapper.ratio - INFO - 加载源G006.445-00.254的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:51:58,167 - data_manager - INFO - 为源G006.445-00.254加载多波段WISE数据: ('12', '22')
2025-05-20 09:51:58,171 - data_manager - INFO - 为源G006.445-00.254加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.445-00.254
2025-05-20 09:51:58,172 - data_manager - INFO - 目录中的所有文件: ['G006.445-00.254_ATLASGAL_870um.fits', 'G006.445-00.254_IRIS_100.fits', 'G006.445-00.254_MIPSGAL_24um.fits', 'G006.445-00.254_NVSS.fits', 'G006.445-00.254_WISE_12.fits', 'G006.445-00.254_WISE_22.fits', 'G006.445-00.254_WISE_3.4.fits', 'G006.445-00.254_WISE_4.6.fits']
2025-05-20 09:51:58,172 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:51:58,172 - data_manager - INFO - 第一次匹配结果: ['G006.445-00.254_WISE_12.fits']
2025-05-20 09:51:58,172 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.445-00.254\G006.445-00.254_WISE_12.fits
2025-05-20 09:51:58,175 - data_manager - INFO - FITS数据统计: 最小值=1532.3812255859375, 最大值=7529.916015625, 均值=2062.9326171875, 中位数=2037.5260009765625
2025-05-20 09:51:58,175 - data_manager - INFO - 有效数据点数量: 33856/33856 (100.00%)
2025-05-20 09:51:58,247 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-05-20 09:51:58,248 - data_manager - INFO - 使用12μm波段的WCS作为参考
2025-05-20 09:51:58,248 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (184, 184)
2025-05-20 09:51:58,253 - data_manager - INFO - 为源G006.445-00.254加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.445-00.254
2025-05-20 09:51:58,253 - data_manager - INFO - 目录中的所有文件: ['G006.445-00.254_ATLASGAL_870um.fits', 'G006.445-00.254_IRIS_100.fits', 'G006.445-00.254_MIPSGAL_24um.fits', 'G006.445-00.254_NVSS.fits', 'G006.445-00.254_WISE_12.fits', 'G006.445-00.254_WISE_22.fits', 'G006.445-00.254_WISE_3.4.fits', 'G006.445-00.254_WISE_4.6.fits']
2025-05-20 09:51:58,254 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:51:58,254 - data_manager - INFO - 第一次匹配结果: ['G006.445-00.254_WISE_22.fits']
2025-05-20 09:51:58,254 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.445-00.254\G006.445-00.254_WISE_22.fits
2025-05-20 09:51:58,270 - data_manager - INFO - FITS数据统计: 最小值=367.48583984375, 最大值=1662.29541015625, 均值=395.4743957519531, 中位数=391.24969482421875
2025-05-20 09:51:58,270 - data_manager - INFO - 有效数据点数量: 10000/10000 (100.00%)
2025-05-20 09:51:58,277 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (100, 100)
2025-05-20 09:51:58,277 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (100, 100)
2025-05-20 09:51:58,277 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w3', 'w4']
2025-05-20 09:51:58,278 - region_mapper.ratio - INFO - 调整W4波段数据从(100, 100)到(184, 184)
2025-05-20 09:51:58,278 - region_mapper.ratio - INFO - 调整W4波段数据从(100, 100)到(184, 184)
2025-05-20 09:51:58,286 - region_mapper.ratio - INFO - W3/W4比值范围: 4.52-6.33，均值: 5.22
2025-05-20 09:51:58,286 - region_mapper.ratio - INFO - W3/W4比值范围: 4.52-6.33，均值: 5.22
2025-05-20 09:51:58,292 - region_mapper.ratio - INFO - 像素尺度: 506.17 像素/角秒
2025-05-20 09:51:58,292 - region_mapper.ratio - INFO - 像素尺度: 506.17 像素/角秒
2025-05-20 09:51:58,293 - region_mapper.ratio - INFO - 有效半径: 0.0 像素
2025-05-20 09:51:58,293 - region_mapper.ratio - INFO - 有效半径: 0.0 像素
2025-05-20 09:51:58,293 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:51:58,293 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:51:59,026 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.708
2025-05-20 09:51:59,026 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.708
2025-05-20 09:51:59,107 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.601
2025-05-20 09:51:59,107 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.601
2025-05-20 09:51:59,187 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.584
2025-05-20 09:51:59,187 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.584
2025-05-20 09:51:59,187 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 09:51:59,187 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 09:51:59,255 - region_mapper.ratio - INFO - 聚类 0: 平均比值=5.32±0.08, 大小=264, 平均距离=6.8, 紧凑性=6.7
2025-05-20 09:51:59,255 - region_mapper.ratio - INFO - 聚类 0: 平均比值=5.32±0.08, 大小=264, 平均距离=6.8, 紧凑性=6.7
2025-05-20 09:51:59,255 - region_mapper.ratio - INFO - 聚类 1: 平均比值=5.71±0.15, 大小=49, 平均距离=6.0, 紧凑性=4.0
2025-05-20 09:51:59,255 - region_mapper.ratio - INFO - 聚类 1: 平均比值=5.71±0.15, 大小=49, 平均距离=6.0, 紧凑性=4.0
2025-05-20 09:51:59,256 - region_mapper.ratio - INFO - 选择聚类 0 作为PDR区域
2025-05-20 09:51:59,256 - region_mapper.ratio - INFO - 选择聚类 0 作为PDR区域
2025-05-20 09:51:59,261 - region_mapper.ratio - INFO - PDR掩模覆盖308个像素
2025-05-20 09:51:59,261 - region_mapper.ratio - INFO - PDR掩模覆盖308个像素
2025-05-20 09:52:01,910 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G006.445-00.254_w3w4_ratio.png
2025-05-20 09:52:01,910 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G006.445-00.254_w3w4_ratio.png
2025-05-20 09:52:01,911 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖308个像素
2025-05-20 09:52:01,911 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖308个像素
2025-05-20 09:52:01,933 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G006.445-00.254_region_masks.fits
2025-05-20 09:52:01,933 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G006.445-00.254_region_masks.fits
2025-05-20 09:52:01,939 - data_manager - INFO - 为源G006.445-00.254加载Gaia数据，中心坐标: RA=270.298741, Dec=-23.512335, 半径=0.0222度
2025-05-20 09:52:01,942 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances-Data/gaia_data\gaia_G006.445-00.254_10arcmin_min10.csv
2025-05-20 09:52:02,299 - data_manager - INFO - 成功加载Gaia数据，共17708条记录
2025-05-20 09:52:02,314 - data_manager - INFO - 成功加载Gaia数据，共338个源在搜索半径内
2025-05-20 09:52:02,323 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.445-00.254_gaia_regions.fits
2025-05-20 09:52:02,435 - data_manager - WARNING - 检测到可能导致保存问题的列: ['region']
2025-05-20 09:52:02,453 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:52:02,549 - data_manager - INFO - 成功保存数据
2025-05-20 09:52:02,550 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:52:02,550 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:52:02,551 - main_single_folder - INFO - 步骤3：恒星选择
2025-05-20 09:52:02,555 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-05-20 09:52:02,558 - star_selector - INFO - 视差信噪比筛选: 保留39/338个源
2025-05-20 09:52:02,558 - star_selector - INFO - RUWE筛选: 保留325/338个源
2025-05-20 09:52:02,566 - star_selector - INFO - 质量筛选详细信息:
2025-05-20 09:52:02,566 - star_selector - INFO -   视差信噪比阈值: 5.0
2025-05-20 09:52:02,566 - star_selector - INFO -   RUWE阈值: 1.4
2025-05-20 09:52:02,568 - star_selector - INFO -   视差信噪比筛选通过率: 13.5%
2025-05-20 09:52:02,569 - star_selector - INFO -   RUWE筛选通过率: 112.5%
2025-05-20 09:52:02,569 - star_selector - INFO -   总体通过率: 10.9%
2025-05-20 09:52:02,569 - star_selector - INFO - 质量筛选完成，保留37/338个源
2025-05-20 09:52:02,569 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.445-00.254_filtered_data.fits
2025-05-20 09:52:02,578 - data_manager - WARNING - 检测到可能导致保存问题的列: ['region']
2025-05-20 09:52:02,596 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:52:02,689 - data_manager - INFO - 成功保存数据
2025-05-20 09:52:02,690 - main_single_folder - INFO - 保存质量筛选后的数据到: results/batch_single_folder\processed\G006.445-00.254_filtered_data.fits
2025-05-20 09:52:02,695 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-05-20 09:52:02,695 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-05-20 09:52:02,695 - star_selector - INFO - 筛选恒星样本，原始样本大小: 37
2025-05-20 09:52:02,695 - star_selector - INFO - 排除YSO后: 37/37个源
2025-05-20 09:52:02,704 - star_selector - INFO - 区域 cavity: 0个源
2025-05-20 09:52:02,704 - star_selector - INFO - 区域 pdr: 24个源
2025-05-20 09:52:02,704 - star_selector - INFO - 区域 external: 0个源
2025-05-20 09:52:02,704 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 37
2025-05-20 09:52:02,705 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.445-00.254_star_sample.fits
2025-05-20 09:52:02,714 - data_manager - WARNING - 检测到可能导致保存问题的列: ['region']
2025-05-20 09:52:02,731 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:52:02,823 - data_manager - INFO - 成功保存数据
2025-05-20 09:52:02,823 - main_single_folder - INFO - 恒星选择完成
2025-05-20 09:52:02,823 - main_single_folder - INFO - 步骤4：消光计算
2025-05-20 09:52:02,828 - extinction_estimator - INFO - 为37个恒星计算A_V，参数: {'rv': 3.1}
2025-05-20 09:52:02,845 - extinction_estimator - INFO - 使用2MASS H-K颜色计算A_V
2025-05-20 09:52:02,845 - extinction_estimator - INFO - 估计本征H-K颜色，方法: select_giants
2025-05-20 09:52:02,846 - extinction_estimator - INFO - 使用红巨星的典型H-K颜色: 0.15
2025-05-20 09:52:02,846 - extinction_estimator - INFO - 基于H-K颜色过量计算A_V，R_V=3.1
2025-05-20 09:52:02,847 - extinction_estimator - INFO - 计算得到36个A_V值，范围: 0.00-18.76
2025-05-20 09:52:02,847 - extinction_estimator - INFO - 使用Gaia BP-RP颜色计算A_V
2025-05-20 09:52:02,848 - extinction_estimator - INFO - 估计本征BP-RP颜色，方法: fixed_value
2025-05-20 09:52:02,848 - extinction_estimator - INFO - 使用固定的BP-RP颜色: 0.8
2025-05-20 09:52:02,848 - extinction_estimator - INFO - 基于Gaia BP-RP颜色过量计算A_V，R_V=3.1
2025-05-20 09:52:02,849 - extinction_estimator - INFO - 计算得到37个A_V值，范围: 0.35-6.22
2025-05-20 09:52:02,850 - extinction_estimator - INFO - 方法 2MASS_HK: 36个源
2025-05-20 09:52:02,850 - extinction_estimator - INFO - 方法 Gaia_BPRP: 1个源
2025-05-20 09:52:02,851 - extinction_estimator - INFO - A_V计算完成，统计信息:
2025-05-20 09:52:02,851 - extinction_estimator - INFO -   范围: 0.00-18.76 mag
2025-05-20 09:52:02,851 - extinction_estimator - INFO -   均值: 5.12 mag
2025-05-20 09:52:02,851 - extinction_estimator - INFO -   中位数: 4.48 mag
2025-05-20 09:52:02,852 - extinction_estimator - INFO -   标准差: 5.03 mag
2025-05-20 09:52:02,852 - extinction_estimator - INFO - 消光计算方法统计:
2025-05-20 09:52:02,852 - extinction_estimator - INFO -   2MASS_HK: 36颗恒星 (97.3%)
2025-05-20 09:52:02,852 - extinction_estimator - INFO -   Gaia_BPRP: 1颗恒星 (2.7%)
2025-05-20 09:52:02,853 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.445-00.254_stars_with_av.fits
2025-05-20 09:52:02,861 - data_manager - WARNING - 检测到可能导致保存问题的列: ['region', 'av_method']
2025-05-20 09:52:02,878 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:52:02,973 - data_manager - INFO - 成功保存数据
2025-05-20 09:52:02,973 - main_single_folder - INFO - 消光计算完成
2025-05-20 09:52:02,974 - main_single_folder - INFO - 步骤5：距离分析
2025-05-20 09:52:02,974 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-05-20 09:52:02,974 - distance_analyzer - INFO - 将分析1个区域: pdr
2025-05-20 09:52:02,979 - distance_analyzer - INFO - 分析pdr区域的消光-距离关系
2025-05-20 09:52:02,980 - distance_analyzer - INFO - 准备距离-消光数据，区域: pdr
2025-05-20 09:52:02,980 - distance_analyzer - INFO - 区域pdr中有24个有效源
2025-05-20 09:52:02,980 - distance_analyzer - INFO - pdr区域统计信息:
2025-05-20 09:52:02,980 - distance_analyzer - INFO -   总恒星数: 37
2025-05-20 09:52:02,980 - distance_analyzer - INFO -   区域内恒星数: 24 (64.9% 的总数)
2025-05-20 09:52:02,981 - distance_analyzer - INFO -   有效数据点: 24 (100.0% 的区域内恒星)
2025-05-20 09:52:02,981 - distance_analyzer - INFO - 计算消光的滑动统计量，bin大小=50pc，最小恒星数=5
2025-05-20 09:52:02,984 - distance_analyzer - WARNING - 没有bin包含足够的恒星（>=5）
2025-05-20 09:52:02,984 - distance_analyzer - INFO - 检测消光跳变，最小跳变高度=0.5mag，最小显著性=2.0
2025-05-20 09:52:02,985 - distance_analyzer - INFO - 跳变检测统计:
2025-05-20 09:52:02,985 - distance_analyzer - INFO -   差分标准差: 5.8514
2025-05-20 09:52:02,985 - distance_analyzer - INFO -   最小跳变高度阈值: 0.5000
2025-05-20 09:52:02,985 - distance_analyzer - INFO -   最小显著性阈值: 2.00
2025-05-20 09:52:02,985 - distance_analyzer - INFO -   检测到的跳变数量: 1
2025-05-20 09:52:02,985 - distance_analyzer - INFO - 跳变详细信息:
2025-05-20 09:52:02,985 - distance_analyzer - INFO -   跳变 1: 距离=2111.3pc, 高度=16.19mag, 显著性=2.8, 前消光=2.57mag, 后消光=18.76mag
2025-05-20 09:52:02,986 - distance_analyzer - INFO -   最显著的跳变: 距离=2111.3pc, 高度=16.19mag, 显著性=2.8
2025-05-20 09:52:02,986 - distance_analyzer - INFO - 共检测到1个消光跳变
2025-05-20 09:52:04,120 - distance_analyzer - INFO - 保存消光-距离图到: results/batch_single_folder\extinction_distance_pdr.png
2025-05-20 09:52:04,121 - distance_analyzer - INFO - pdr区域分析完成，检测到1个跳变
2025-05-20 09:52:04,121 - distance_analyzer - INFO - 估计分子云距离
2025-05-20 09:52:04,121 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity, external
2025-05-20 09:52:04,121 - distance_analyzer - INFO - 估计的分子云距离: 2111 ± 50 pc, 置信度: low
2025-05-20 09:52:04,121 - distance_analyzer - INFO - 证据: Using most significant PDR jump at 2111pc (ΔA_V=16.19, significance=2.8)
2025-05-20 09:52:04,122 - main_single_folder - INFO - 距离分析完成
2025-05-20 09:52:04,122 - main_single_folder - INFO - 步骤6：可视化和报告生成
2025-05-20 09:52:04,127 - data_manager - INFO - 为源G006.445-00.254加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.445-00.254
2025-05-20 09:52:04,127 - data_manager - INFO - 目录中的所有文件: ['G006.445-00.254_ATLASGAL_870um.fits', 'G006.445-00.254_IRIS_100.fits', 'G006.445-00.254_MIPSGAL_24um.fits', 'G006.445-00.254_NVSS.fits', 'G006.445-00.254_WISE_12.fits', 'G006.445-00.254_WISE_22.fits', 'G006.445-00.254_WISE_3.4.fits', 'G006.445-00.254_WISE_4.6.fits']
2025-05-20 09:52:04,128 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:52:04,128 - data_manager - INFO - 第一次匹配结果: ['G006.445-00.254_WISE_12.fits']
2025-05-20 09:52:04,128 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.445-00.254\G006.445-00.254_WISE_12.fits
2025-05-20 09:52:04,131 - data_manager - INFO - FITS数据统计: 最小值=1532.3812255859375, 最大值=7529.916015625, 均值=2062.9326171875, 中位数=2037.5260009765625
2025-05-20 09:52:04,131 - data_manager - INFO - 有效数据点数量: 33856/33856 (100.00%)
2025-05-20 09:52:04,138 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-05-20 09:52:04,143 - visualizer - INFO - 绘制WISE图像和区域掩模，输出到: results/batch_single_folder\visualizations\G006.445-00.254_wise_regions_stars.png
2025-05-20 09:52:04,262 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:52:06,141 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:52:06,149 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:52:07,957 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G006.445-00.254_wise_regions_stars.png
2025-05-20 09:52:07,958 - main_single_folder - INFO - 成功保存WISE图像和区域掩模到: results/batch_single_folder\visualizations\G006.445-00.254_wise_regions_stars.png
2025-05-20 09:52:07,958 - main_single_folder - INFO - 加载WISE多波段数据用于RGB图像生成
2025-05-20 09:52:07,958 - data_manager - INFO - 为源G006.445-00.254加载多波段WISE数据: ('3.4', '12', '22')
2025-05-20 09:52:07,963 - data_manager - INFO - 为源G006.445-00.254加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.445-00.254
2025-05-20 09:52:07,963 - data_manager - INFO - 目录中的所有文件: ['G006.445-00.254_ATLASGAL_870um.fits', 'G006.445-00.254_IRIS_100.fits', 'G006.445-00.254_MIPSGAL_24um.fits', 'G006.445-00.254_NVSS.fits', 'G006.445-00.254_WISE_12.fits', 'G006.445-00.254_WISE_22.fits', 'G006.445-00.254_WISE_3.4.fits', 'G006.445-00.254_WISE_4.6.fits']
2025-05-20 09:52:07,963 - data_manager - INFO - 查找WISE 3.4μm波段的FITS文件
2025-05-20 09:52:07,963 - data_manager - INFO - 第一次匹配结果: ['G006.445-00.254_WISE_3.4.fits']
2025-05-20 09:52:07,964 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.445-00.254\G006.445-00.254_WISE_3.4.fits
2025-05-20 09:52:08,020 - data_manager - INFO - FITS数据统计: 最小值=36.545616149902344, 最大值=2429.599609375, 均值=249.72222900390625, 中位数=177.85360717773438
2025-05-20 09:52:08,020 - data_manager - INFO - 有效数据点数量: 38416/38416 (100.00%)
2025-05-20 09:52:08,027 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (196, 196)
2025-05-20 09:52:08,027 - data_manager - INFO - 使用3.4μm波段的WCS作为参考
2025-05-20 09:52:08,028 - data_manager - INFO - 成功加载WISE 3.4μm波段数据，尺寸: (196, 196)
2025-05-20 09:52:08,033 - data_manager - INFO - 为源G006.445-00.254加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.445-00.254
2025-05-20 09:52:08,033 - data_manager - INFO - 目录中的所有文件: ['G006.445-00.254_ATLASGAL_870um.fits', 'G006.445-00.254_IRIS_100.fits', 'G006.445-00.254_MIPSGAL_24um.fits', 'G006.445-00.254_NVSS.fits', 'G006.445-00.254_WISE_12.fits', 'G006.445-00.254_WISE_22.fits', 'G006.445-00.254_WISE_3.4.fits', 'G006.445-00.254_WISE_4.6.fits']
2025-05-20 09:52:08,033 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:52:08,033 - data_manager - INFO - 第一次匹配结果: ['G006.445-00.254_WISE_12.fits']
2025-05-20 09:52:08,034 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.445-00.254\G006.445-00.254_WISE_12.fits
2025-05-20 09:52:08,036 - data_manager - INFO - FITS数据统计: 最小值=1532.3812255859375, 最大值=7529.916015625, 均值=2062.9326171875, 中位数=2037.5260009765625
2025-05-20 09:52:08,036 - data_manager - INFO - 有效数据点数量: 33856/33856 (100.00%)
2025-05-20 09:52:08,044 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-05-20 09:52:08,044 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (184, 184)
2025-05-20 09:52:08,049 - data_manager - INFO - 为源G006.445-00.254加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.445-00.254
2025-05-20 09:52:08,049 - data_manager - INFO - 目录中的所有文件: ['G006.445-00.254_ATLASGAL_870um.fits', 'G006.445-00.254_IRIS_100.fits', 'G006.445-00.254_MIPSGAL_24um.fits', 'G006.445-00.254_NVSS.fits', 'G006.445-00.254_WISE_12.fits', 'G006.445-00.254_WISE_22.fits', 'G006.445-00.254_WISE_3.4.fits', 'G006.445-00.254_WISE_4.6.fits']
2025-05-20 09:52:08,050 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:52:08,050 - data_manager - INFO - 第一次匹配结果: ['G006.445-00.254_WISE_22.fits']
2025-05-20 09:52:08,050 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.445-00.254\G006.445-00.254_WISE_22.fits
2025-05-20 09:52:08,052 - data_manager - INFO - FITS数据统计: 最小值=367.48583984375, 最大值=1662.29541015625, 均值=395.4743957519531, 中位数=391.24969482421875
2025-05-20 09:52:08,052 - data_manager - INFO - 有效数据点数量: 10000/10000 (100.00%)
2025-05-20 09:52:08,060 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (100, 100)
2025-05-20 09:52:08,060 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (100, 100)
2025-05-20 09:52:08,060 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w1', 'w3', 'w4']
2025-05-20 09:52:08,060 - visualizer - INFO - 创建WISE三波段RGB图像，输出到: results/batch_single_folder\visualizations\G006.445-00.254_wise_rgb.png
2025-05-20 09:52:08,187 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:52:08,187 - visualizer - INFO - 波段 3.4μm 的尺寸: (196, 196)
2025-05-20 09:52:08,187 - visualizer - INFO - 波段 12μm 的尺寸: (184, 184)
2025-05-20 09:52:08,187 - visualizer - INFO - 波段 22μm 的尺寸: (100, 100)
2025-05-20 09:52:08,187 - visualizer - INFO - Using 12μm band size as target: (184, 184)
2025-05-20 09:52:08,187 - visualizer - INFO - Using target image size: (184, 184), original sizes: {'w1': (196, 196), 'w3': (184, 184), 'w4': (100, 100)}
2025-05-20 09:52:08,193 - visualizer - INFO - 调整w4波段数据从(100, 100)到(184, 184)
2025-05-20 09:52:08,193 - visualizer - INFO - 红色通道(22μm)数据范围: 367.48583984375-1627.8521728515625
2025-05-20 09:52:08,194 - visualizer - INFO - 绿色通道(12μm)数据范围: 1532.3812255859375-7529.916015625
2025-05-20 09:52:08,200 - visualizer - INFO - 调整w1波段数据从(196, 196)到(184, 184)
2025-05-20 09:52:08,200 - visualizer - INFO - 蓝色通道(3.4μm)数据范围: 41.17228317260742-2392.1220703125
2025-05-20 09:52:08,202 - visualizer - INFO - 使用Lupton RGB方法，stretch=1617.33, Q=10
2025-05-20 09:52:08,202 - visualizer - INFO - 各波段99.5%百分位数: R=551.98, G=3234.66, B=1475.85
2025-05-20 09:52:08,207 - visualizer - INFO - RGB数据形状: (184, 184, 3), 类型: float32
2025-05-20 09:52:08,207 - visualizer - INFO - Red通道数据范围：0.06666667014360428-0.239215686917305，均值：0.1188
2025-05-20 09:52:08,208 - visualizer - INFO - Green通道数据范围：0.4470588266849518-1.0，均值：0.6257
2025-05-20 09:52:08,208 - visualizer - INFO - Blue通道数据范围：0.0117647061124444-0.4470588266849518，均值：0.0711
2025-05-20 09:52:09,603 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:52:09,609 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:52:09,616 - visualizer - INFO - 按照要求不显示Gaia星
2025-05-20 09:52:09,617 - visualizer - INFO - 设置坐标刻度间隔: RA=10.000度, Dec=10.000度
2025-05-20 09:52:10,507 - visualizer - INFO - 成功保存RGB图像到: results/batch_single_folder\visualizations\G006.445-00.254_wise_rgb.png
2025-05-20 09:52:10,508 - main_single_folder - INFO - 成功保存WISE RGB图像到: results/batch_single_folder\visualizations\G006.445-00.254_wise_rgb.png
2025-05-20 09:52:10,508 - visualizer - INFO - 绘制消光-距离散点图，输出到: results/batch_single_folder\visualizations\G006.445-00.254_extinction_distance.png
2025-05-20 09:52:11,898 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G006.445-00.254_extinction_distance.png
2025-05-20 09:52:11,898 - main_single_folder - INFO - 成功保存消光-距离散点图到: results/batch_single_folder\visualizations\G006.445-00.254_extinction_distance.png
2025-05-20 09:52:11,968 - main_single_folder - INFO - 成功保存处理报告到: results/batch_single_folder\reports\G006.445-00.254_report.txt
2025-05-20 09:52:11,968 - main_single_folder - INFO - 处理G006.445-00.254完成
