处理时间: 2025-05-20 09:51:35
耗时: 19.02秒

标准输出:

结果摘要:
源: G006.160-00.608
分子云距离估计: 1182 ± 50 pc
置信度: low

证据:
- Using most significant PDR jump at 1182pc (ΔA_V=24.46, significance=2.3)


标准错误:
2025-05-20 09:51:20,034 - main_single_folder - INFO - 输出目录: results/batch_single_folder
2025-05-20 09:51:20,035 - main_single_folder - INFO - 加载源G006.160-00.608的信息
2025-05-20 09:51:20,035 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
2025-05-20 09:51:20,040 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-05-20 09:51:20,042 - main_single_folder - INFO - 从源名解析银道坐标: l=6.160000, b=-0.608000
2025-05-20 09:51:20,048 - main_single_folder - INFO - 银道坐标转换为赤道坐标: RA=270.480747, Dec=-23.935150 (ICRS)
2025-05-20 09:51:20,048 - main_single_folder - INFO - 从源表加载信息: RA=270.480747, Dec=-23.935150, R_eff=0.018611度
2025-05-20 09:51:20,049 - main_single_folder - INFO - 步骤1：加载WISE数据
2025-05-20 09:51:20,053 - data_manager - INFO - 为源G006.160-00.608加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.160-00.608
2025-05-20 09:51:20,072 - data_manager - INFO - 目录中的所有文件: ['G006.160-00.608_ATLASGAL_870um.fits', 'G006.160-00.608_IRIS_100.fits', 'G006.160-00.608_MIPSGAL_24um.fits', 'G006.160-00.608_NVSS.fits', 'G006.160-00.608_WISE_12.fits', 'G006.160-00.608_WISE_22.fits', 'G006.160-00.608_WISE_3.4.fits', 'G006.160-00.608_WISE_4.6.fits']
2025-05-20 09:51:20,072 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:51:20,072 - data_manager - INFO - 第一次匹配结果: ['G006.160-00.608_WISE_12.fits']
2025-05-20 09:51:20,072 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.160-00.608\G006.160-00.608_WISE_12.fits
2025-05-20 09:51:20,119 - data_manager - INFO - FITS数据统计: 最小值=1396.326416015625, 最大值=8065.0732421875, 均值=2102.52734375, 中位数=2034.44921875
2025-05-20 09:51:20,119 - data_manager - INFO - 有效数据点数量: 33856/33856 (100.00%)
2025-05-20 09:51:20,126 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-05-20 09:51:20,127 - main_single_folder - INFO - WISE数据加载完成
2025-05-20 09:51:20,127 - main_single_folder - INFO - 步骤2：区域映射
2025-05-20 09:51:20,128 - main_single_folder - INFO - 使用W3/W4波段比值方法定义PDR区域
2025-05-20 09:51:20,128 - region_mapper - INFO - 使用W3/W4波段比值方法定义PDR掩模
2025-05-20 09:51:20,129 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:51:20,129 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:51:20,129 - region_mapper.ratio - INFO - 加载源G006.160-00.608的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:51:20,129 - region_mapper.ratio - INFO - 加载源G006.160-00.608的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:51:20,129 - data_manager - INFO - 为源G006.160-00.608加载多波段WISE数据: ('12', '22')
2025-05-20 09:51:20,134 - data_manager - INFO - 为源G006.160-00.608加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.160-00.608
2025-05-20 09:51:20,134 - data_manager - INFO - 目录中的所有文件: ['G006.160-00.608_ATLASGAL_870um.fits', 'G006.160-00.608_IRIS_100.fits', 'G006.160-00.608_MIPSGAL_24um.fits', 'G006.160-00.608_NVSS.fits', 'G006.160-00.608_WISE_12.fits', 'G006.160-00.608_WISE_22.fits', 'G006.160-00.608_WISE_3.4.fits', 'G006.160-00.608_WISE_4.6.fits']
2025-05-20 09:51:20,134 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:51:20,134 - data_manager - INFO - 第一次匹配结果: ['G006.160-00.608_WISE_12.fits']
2025-05-20 09:51:20,134 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.160-00.608\G006.160-00.608_WISE_12.fits
2025-05-20 09:51:20,137 - data_manager - INFO - FITS数据统计: 最小值=1396.326416015625, 最大值=8065.0732421875, 均值=2102.52734375, 中位数=2034.44921875
2025-05-20 09:51:20,137 - data_manager - INFO - 有效数据点数量: 33856/33856 (100.00%)
2025-05-20 09:51:20,210 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-05-20 09:51:20,211 - data_manager - INFO - 使用12μm波段的WCS作为参考
2025-05-20 09:51:20,211 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (184, 184)
2025-05-20 09:51:20,216 - data_manager - INFO - 为源G006.160-00.608加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.160-00.608
2025-05-20 09:51:20,216 - data_manager - INFO - 目录中的所有文件: ['G006.160-00.608_ATLASGAL_870um.fits', 'G006.160-00.608_IRIS_100.fits', 'G006.160-00.608_MIPSGAL_24um.fits', 'G006.160-00.608_NVSS.fits', 'G006.160-00.608_WISE_12.fits', 'G006.160-00.608_WISE_22.fits', 'G006.160-00.608_WISE_3.4.fits', 'G006.160-00.608_WISE_4.6.fits']
2025-05-20 09:51:20,216 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:51:20,217 - data_manager - INFO - 第一次匹配结果: ['G006.160-00.608_WISE_22.fits']
2025-05-20 09:51:20,217 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.160-00.608\G006.160-00.608_WISE_22.fits
2025-05-20 09:51:20,251 - data_manager - INFO - FITS数据统计: 最小值=346.9205322265625, 最大值=1491.30029296875, 均值=402.7835998535156, 中位数=387.4496154785156
2025-05-20 09:51:20,251 - data_manager - INFO - 有效数据点数量: 10000/10000 (100.00%)
2025-05-20 09:51:20,258 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (100, 100)
2025-05-20 09:51:20,258 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (100, 100)
2025-05-20 09:51:20,258 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w3', 'w4']
2025-05-20 09:51:20,259 - region_mapper.ratio - INFO - 调整W4波段数据从(100, 100)到(184, 184)
2025-05-20 09:51:20,259 - region_mapper.ratio - INFO - 调整W4波段数据从(100, 100)到(184, 184)
2025-05-20 09:51:20,267 - region_mapper.ratio - INFO - W3/W4比值范围: 4.00-7.05，均值: 5.21
2025-05-20 09:51:20,267 - region_mapper.ratio - INFO - W3/W4比值范围: 4.00-7.05，均值: 5.21
2025-05-20 09:51:20,274 - region_mapper.ratio - INFO - 像素尺度: 504.53 像素/角秒
2025-05-20 09:51:20,274 - region_mapper.ratio - INFO - 像素尺度: 504.53 像素/角秒
2025-05-20 09:51:20,274 - region_mapper.ratio - INFO - 有效半径: 0.1 像素
2025-05-20 09:51:20,274 - region_mapper.ratio - INFO - 有效半径: 0.1 像素
2025-05-20 09:51:20,274 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:51:20,274 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:51:20,998 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.679
2025-05-20 09:51:20,998 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.679
2025-05-20 09:51:21,074 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.678
2025-05-20 09:51:21,074 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.678
2025-05-20 09:51:21,153 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.696
2025-05-20 09:51:21,153 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.696
2025-05-20 09:51:21,153 - region_mapper.ratio - INFO - 使用最佳聚类数: 4
2025-05-20 09:51:21,153 - region_mapper.ratio - INFO - 使用最佳聚类数: 4
2025-05-20 09:51:21,229 - region_mapper.ratio - INFO - 聚类 0: 平均比值=5.24±0.18, 大小=56, 平均距离=8.3, 紧凑性=8.3
2025-05-20 09:51:21,229 - region_mapper.ratio - INFO - 聚类 0: 平均比值=5.24±0.18, 大小=56, 平均距离=8.3, 紧凑性=8.3
2025-05-20 09:51:21,229 - region_mapper.ratio - INFO - 聚类 1: 平均比值=4.04±0.07, 大小=118, 平均距离=4.6, 紧凑性=4.3
2025-05-20 09:51:21,229 - region_mapper.ratio - INFO - 聚类 1: 平均比值=4.04±0.07, 大小=118, 平均距离=4.6, 紧凑性=4.3
2025-05-20 09:51:21,230 - region_mapper.ratio - INFO - 聚类 2: 平均比值=4.62±0.17, 大小=73, 平均距离=6.9, 紧凑性=6.8
2025-05-20 09:51:21,230 - region_mapper.ratio - INFO - 聚类 2: 平均比值=4.62±0.17, 大小=73, 平均距离=6.9, 紧凑性=6.8
2025-05-20 09:51:21,230 - region_mapper.ratio - INFO - 聚类 3: 平均比值=5.99±0.19, 大小=67, 平均距离=8.7, 紧凑性=5.8
2025-05-20 09:51:21,230 - region_mapper.ratio - INFO - 聚类 3: 平均比值=5.99±0.19, 大小=67, 平均距离=8.7, 紧凑性=5.8
2025-05-20 09:51:21,231 - region_mapper.ratio - INFO - 选择聚类 2 作为PDR区域
2025-05-20 09:51:21,231 - region_mapper.ratio - INFO - 选择聚类 2 作为PDR区域
2025-05-20 09:51:21,236 - region_mapper.ratio - INFO - PDR掩模覆盖191个像素
2025-05-20 09:51:21,236 - region_mapper.ratio - INFO - PDR掩模覆盖191个像素
2025-05-20 09:51:23,890 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G006.160-00.608_w3w4_ratio.png
2025-05-20 09:51:23,890 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G006.160-00.608_w3w4_ratio.png
2025-05-20 09:51:23,891 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖191个像素
2025-05-20 09:51:23,891 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖191个像素
2025-05-20 09:51:23,915 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G006.160-00.608_region_masks.fits
2025-05-20 09:51:23,915 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G006.160-00.608_region_masks.fits
2025-05-20 09:51:23,920 - data_manager - INFO - 为源G006.160-00.608加载Gaia数据，中心坐标: RA=270.480747, Dec=-23.935150, 半径=0.0931度
2025-05-20 09:51:23,922 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances-Data/gaia_data\gaia_G006.160-00.608_10arcmin_min10.csv
2025-05-20 09:51:24,198 - data_manager - INFO - 成功加载Gaia数据，共11719条记录
2025-05-20 09:51:24,215 - data_manager - INFO - 成功加载Gaia数据，共3454个源在搜索半径内
2025-05-20 09:51:24,228 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.160-00.608_gaia_regions.fits
2025-05-20 09:51:25,207 - data_manager - WARNING - 检测到可能导致保存问题的列: ['region']
2025-05-20 09:51:25,229 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:51:25,366 - data_manager - INFO - 成功保存数据
2025-05-20 09:51:25,367 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:51:25,367 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:51:25,367 - main_single_folder - INFO - 步骤3：恒星选择
2025-05-20 09:51:25,372 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-05-20 09:51:25,375 - star_selector - INFO - 视差信噪比筛选: 保留569/3454个源
2025-05-20 09:51:25,376 - star_selector - INFO - RUWE筛选: 保留3384/3454个源
2025-05-20 09:51:25,387 - star_selector - INFO - 质量筛选详细信息:
2025-05-20 09:51:25,387 - star_selector - INFO -   视差信噪比阈值: 5.0
2025-05-20 09:51:25,387 - star_selector - INFO -   RUWE阈值: 1.4
2025-05-20 09:51:25,390 - star_selector - INFO -   视差信噪比筛选通过率: 19.3%
2025-05-20 09:51:25,391 - star_selector - INFO -   RUWE筛选通过率: 114.6%
2025-05-20 09:51:25,391 - star_selector - INFO -   总体通过率: 15.8%
2025-05-20 09:51:25,391 - star_selector - INFO - 质量筛选完成，保留545/3454个源
2025-05-20 09:51:25,391 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.160-00.608_filtered_data.fits
2025-05-20 09:51:25,513 - data_manager - WARNING - 检测到可能导致保存问题的列: ['region']
2025-05-20 09:51:25,531 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:51:25,627 - data_manager - INFO - 成功保存数据
2025-05-20 09:51:25,627 - main_single_folder - INFO - 保存质量筛选后的数据到: results/batch_single_folder\processed\G006.160-00.608_filtered_data.fits
2025-05-20 09:51:25,632 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-05-20 09:51:25,632 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-05-20 09:51:25,632 - star_selector - INFO - 筛选恒星样本，原始样本大小: 545
2025-05-20 09:51:25,633 - star_selector - INFO - 排除YSO后: 545/545个源
2025-05-20 09:51:25,641 - star_selector - INFO - 区域 cavity: 0个源
2025-05-20 09:51:25,642 - star_selector - INFO - 区域 pdr: 20个源
2025-05-20 09:51:25,642 - star_selector - INFO - 区域 external: 0个源
2025-05-20 09:51:25,642 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 545
2025-05-20 09:51:25,642 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.160-00.608_star_sample.fits
2025-05-20 09:51:25,761 - data_manager - WARNING - 检测到可能导致保存问题的列: ['region']
2025-05-20 09:51:25,778 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:51:25,873 - data_manager - INFO - 成功保存数据
2025-05-20 09:51:25,873 - main_single_folder - INFO - 恒星选择完成
2025-05-20 09:51:25,874 - main_single_folder - INFO - 步骤4：消光计算
2025-05-20 09:51:25,879 - extinction_estimator - INFO - 为545个恒星计算A_V，参数: {'rv': 3.1}
2025-05-20 09:51:25,898 - extinction_estimator - INFO - 使用2MASS H-K颜色计算A_V
2025-05-20 09:51:25,898 - extinction_estimator - INFO - 估计本征H-K颜色，方法: select_giants
2025-05-20 09:51:25,898 - extinction_estimator - INFO - 使用红巨星的典型H-K颜色: 0.15
2025-05-20 09:51:25,898 - extinction_estimator - INFO - 基于H-K颜色过量计算A_V，R_V=3.1
2025-05-20 09:51:25,900 - extinction_estimator - INFO - 计算得到489个A_V值，范围: 0.00-66.38
2025-05-20 09:51:25,900 - extinction_estimator - INFO - 使用Gaia BP-RP颜色计算A_V
2025-05-20 09:51:25,900 - extinction_estimator - INFO - 估计本征BP-RP颜色，方法: fixed_value
2025-05-20 09:51:25,901 - extinction_estimator - INFO - 使用固定的BP-RP颜色: 0.8
2025-05-20 09:51:25,901 - extinction_estimator - INFO - 基于Gaia BP-RP颜色过量计算A_V，R_V=3.1
2025-05-20 09:51:25,902 - extinction_estimator - INFO - 计算得到537个A_V值，范围: 0.00-10.57
2025-05-20 09:51:25,903 - extinction_estimator - INFO - 方法 2MASS_HK: 489个源
2025-05-20 09:51:25,903 - extinction_estimator - INFO - 方法 Gaia_BPRP: 50个源
2025-05-20 09:51:25,903 - extinction_estimator - INFO - 方法 unknown: 6个源
2025-05-20 09:51:25,904 - extinction_estimator - INFO - A_V计算完成，统计信息:
2025-05-20 09:51:25,904 - extinction_estimator - INFO -   范围: 0.00-66.38 mag
2025-05-20 09:51:25,904 - extinction_estimator - INFO -   均值: 5.34 mag
2025-05-20 09:51:25,904 - extinction_estimator - INFO -   中位数: 2.00 mag
2025-05-20 09:51:25,904 - extinction_estimator - INFO -   标准差: 7.81 mag
2025-05-20 09:51:25,905 - extinction_estimator - INFO - 消光计算方法统计:
2025-05-20 09:51:25,905 - extinction_estimator - INFO -   2MASS_HK: 489颗恒星 (89.7%)
2025-05-20 09:51:25,905 - extinction_estimator - INFO -   Gaia_BPRP: 50颗恒星 (9.2%)
2025-05-20 09:51:25,906 - extinction_estimator - INFO -   unknown: 6颗恒星 (1.1%)
2025-05-20 09:51:25,906 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.160-00.608_stars_with_av.fits
2025-05-20 09:51:26,030 - data_manager - WARNING - 检测到可能导致保存问题的列: ['region', 'av_method']
2025-05-20 09:51:26,047 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:51:26,149 - data_manager - INFO - 成功保存数据
2025-05-20 09:51:26,150 - main_single_folder - INFO - 消光计算完成
2025-05-20 09:51:26,150 - main_single_folder - INFO - 步骤5：距离分析
2025-05-20 09:51:26,150 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-05-20 09:51:26,151 - distance_analyzer - INFO - 将分析1个区域: pdr
2025-05-20 09:51:26,156 - distance_analyzer - INFO - 分析pdr区域的消光-距离关系
2025-05-20 09:51:26,157 - distance_analyzer - INFO - 准备距离-消光数据，区域: pdr
2025-05-20 09:51:26,157 - distance_analyzer - INFO - 区域pdr中有20个有效源
2025-05-20 09:51:26,157 - distance_analyzer - INFO - pdr区域统计信息:
2025-05-20 09:51:26,157 - distance_analyzer - INFO -   总恒星数: 545
2025-05-20 09:51:26,158 - distance_analyzer - INFO -   区域内恒星数: 20 (3.7% 的总数)
2025-05-20 09:51:26,158 - distance_analyzer - INFO -   有效数据点: 20 (100.0% 的区域内恒星)
2025-05-20 09:51:26,158 - distance_analyzer - INFO - 计算消光的滑动统计量，bin大小=50pc，最小恒星数=5
2025-05-20 09:51:26,161 - distance_analyzer - WARNING - 没有bin包含足够的恒星（>=5）
2025-05-20 09:51:26,161 - distance_analyzer - INFO - 检测消光跳变，最小跳变高度=0.5mag，最小显著性=2.0
2025-05-20 09:51:26,162 - distance_analyzer - INFO - 跳变检测统计:
2025-05-20 09:51:26,162 - distance_analyzer - INFO -   差分标准差: 10.7410
2025-05-20 09:51:26,162 - distance_analyzer - INFO -   最小跳变高度阈值: 0.5000
2025-05-20 09:51:26,162 - distance_analyzer - INFO -   最小显著性阈值: 2.00
2025-05-20 09:51:26,162 - distance_analyzer - INFO -   检测到的跳变数量: 1
2025-05-20 09:51:26,163 - distance_analyzer - INFO - 跳变详细信息:
2025-05-20 09:51:26,163 - distance_analyzer - INFO -   跳变 1: 距离=1182.2pc, 高度=24.46mag, 显著性=2.3, 前消光=0.03mag, 后消光=24.49mag
2025-05-20 09:51:26,163 - distance_analyzer - INFO -   最显著的跳变: 距离=1182.2pc, 高度=24.46mag, 显著性=2.3
2025-05-20 09:51:26,163 - distance_analyzer - INFO - 共检测到1个消光跳变
2025-05-20 09:51:27,264 - distance_analyzer - INFO - 保存消光-距离图到: results/batch_single_folder\extinction_distance_pdr.png
2025-05-20 09:51:27,265 - distance_analyzer - INFO - pdr区域分析完成，检测到1个跳变
2025-05-20 09:51:27,265 - distance_analyzer - INFO - 估计分子云距离
2025-05-20 09:51:27,265 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity, external
2025-05-20 09:51:27,265 - distance_analyzer - INFO - 估计的分子云距离: 1182 ± 50 pc, 置信度: low
2025-05-20 09:51:27,265 - distance_analyzer - INFO - 证据: Using most significant PDR jump at 1182pc (ΔA_V=24.46, significance=2.3)
2025-05-20 09:51:27,266 - main_single_folder - INFO - 距离分析完成
2025-05-20 09:51:27,266 - main_single_folder - INFO - 步骤6：可视化和报告生成
2025-05-20 09:51:27,271 - data_manager - INFO - 为源G006.160-00.608加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.160-00.608
2025-05-20 09:51:27,271 - data_manager - INFO - 目录中的所有文件: ['G006.160-00.608_ATLASGAL_870um.fits', 'G006.160-00.608_IRIS_100.fits', 'G006.160-00.608_MIPSGAL_24um.fits', 'G006.160-00.608_NVSS.fits', 'G006.160-00.608_WISE_12.fits', 'G006.160-00.608_WISE_22.fits', 'G006.160-00.608_WISE_3.4.fits', 'G006.160-00.608_WISE_4.6.fits']
2025-05-20 09:51:27,271 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:51:27,272 - data_manager - INFO - 第一次匹配结果: ['G006.160-00.608_WISE_12.fits']
2025-05-20 09:51:27,272 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.160-00.608\G006.160-00.608_WISE_12.fits
2025-05-20 09:51:27,275 - data_manager - INFO - FITS数据统计: 最小值=1396.326416015625, 最大值=8065.0732421875, 均值=2102.52734375, 中位数=2034.44921875
2025-05-20 09:51:27,275 - data_manager - INFO - 有效数据点数量: 33856/33856 (100.00%)
2025-05-20 09:51:27,282 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-05-20 09:51:27,287 - visualizer - INFO - 绘制WISE图像和区域掩模，输出到: results/batch_single_folder\visualizations\G006.160-00.608_wise_regions_stars.png
2025-05-20 09:51:27,405 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:51:29,322 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:51:29,330 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:51:31,150 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G006.160-00.608_wise_regions_stars.png
2025-05-20 09:51:31,150 - main_single_folder - INFO - 成功保存WISE图像和区域掩模到: results/batch_single_folder\visualizations\G006.160-00.608_wise_regions_stars.png
2025-05-20 09:51:31,151 - main_single_folder - INFO - 加载WISE多波段数据用于RGB图像生成
2025-05-20 09:51:31,151 - data_manager - INFO - 为源G006.160-00.608加载多波段WISE数据: ('3.4', '12', '22')
2025-05-20 09:51:31,155 - data_manager - INFO - 为源G006.160-00.608加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.160-00.608
2025-05-20 09:51:31,156 - data_manager - INFO - 目录中的所有文件: ['G006.160-00.608_ATLASGAL_870um.fits', 'G006.160-00.608_IRIS_100.fits', 'G006.160-00.608_MIPSGAL_24um.fits', 'G006.160-00.608_NVSS.fits', 'G006.160-00.608_WISE_12.fits', 'G006.160-00.608_WISE_22.fits', 'G006.160-00.608_WISE_3.4.fits', 'G006.160-00.608_WISE_4.6.fits']
2025-05-20 09:51:31,156 - data_manager - INFO - 查找WISE 3.4μm波段的FITS文件
2025-05-20 09:51:31,156 - data_manager - INFO - 第一次匹配结果: ['G006.160-00.608_WISE_3.4.fits']
2025-05-20 09:51:31,156 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.160-00.608\G006.160-00.608_WISE_3.4.fits
2025-05-20 09:51:31,195 - data_manager - INFO - FITS数据统计: 最小值=32.832855224609375, 最大值=2778.21044921875, 均值=217.27426147460938, 中位数=159.8007354736328
2025-05-20 09:51:31,195 - data_manager - INFO - 有效数据点数量: 38416/38416 (100.00%)
2025-05-20 09:51:31,202 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (196, 196)
2025-05-20 09:51:31,202 - data_manager - INFO - 使用3.4μm波段的WCS作为参考
2025-05-20 09:51:31,202 - data_manager - INFO - 成功加载WISE 3.4μm波段数据，尺寸: (196, 196)
2025-05-20 09:51:31,208 - data_manager - INFO - 为源G006.160-00.608加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.160-00.608
2025-05-20 09:51:31,208 - data_manager - INFO - 目录中的所有文件: ['G006.160-00.608_ATLASGAL_870um.fits', 'G006.160-00.608_IRIS_100.fits', 'G006.160-00.608_MIPSGAL_24um.fits', 'G006.160-00.608_NVSS.fits', 'G006.160-00.608_WISE_12.fits', 'G006.160-00.608_WISE_22.fits', 'G006.160-00.608_WISE_3.4.fits', 'G006.160-00.608_WISE_4.6.fits']
2025-05-20 09:51:31,208 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:51:31,208 - data_manager - INFO - 第一次匹配结果: ['G006.160-00.608_WISE_12.fits']
2025-05-20 09:51:31,208 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.160-00.608\G006.160-00.608_WISE_12.fits
2025-05-20 09:51:31,211 - data_manager - INFO - FITS数据统计: 最小值=1396.326416015625, 最大值=8065.0732421875, 均值=2102.52734375, 中位数=2034.44921875
2025-05-20 09:51:31,211 - data_manager - INFO - 有效数据点数量: 33856/33856 (100.00%)
2025-05-20 09:51:31,218 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-05-20 09:51:31,219 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (184, 184)
2025-05-20 09:51:31,224 - data_manager - INFO - 为源G006.160-00.608加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.160-00.608
2025-05-20 09:51:31,224 - data_manager - INFO - 目录中的所有文件: ['G006.160-00.608_ATLASGAL_870um.fits', 'G006.160-00.608_IRIS_100.fits', 'G006.160-00.608_MIPSGAL_24um.fits', 'G006.160-00.608_NVSS.fits', 'G006.160-00.608_WISE_12.fits', 'G006.160-00.608_WISE_22.fits', 'G006.160-00.608_WISE_3.4.fits', 'G006.160-00.608_WISE_4.6.fits']
2025-05-20 09:51:31,224 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:51:31,224 - data_manager - INFO - 第一次匹配结果: ['G006.160-00.608_WISE_22.fits']
2025-05-20 09:51:31,225 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.160-00.608\G006.160-00.608_WISE_22.fits
2025-05-20 09:51:31,227 - data_manager - INFO - FITS数据统计: 最小值=346.9205322265625, 最大值=1491.30029296875, 均值=402.7835998535156, 中位数=387.4496154785156
2025-05-20 09:51:31,227 - data_manager - INFO - 有效数据点数量: 10000/10000 (100.00%)
2025-05-20 09:51:31,234 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (100, 100)
2025-05-20 09:51:31,234 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (100, 100)
2025-05-20 09:51:31,235 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w1', 'w3', 'w4']
2025-05-20 09:51:31,235 - visualizer - INFO - 创建WISE三波段RGB图像，输出到: results/batch_single_folder\visualizations\G006.160-00.608_wise_rgb.png
2025-05-20 09:51:31,363 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:51:31,363 - visualizer - INFO - 波段 3.4μm 的尺寸: (196, 196)
2025-05-20 09:51:31,363 - visualizer - INFO - 波段 12μm 的尺寸: (184, 184)
2025-05-20 09:51:31,363 - visualizer - INFO - 波段 22μm 的尺寸: (100, 100)
2025-05-20 09:51:31,363 - visualizer - INFO - Using 12μm band size as target: (184, 184)
2025-05-20 09:51:31,364 - visualizer - INFO - Using target image size: (184, 184), original sizes: {'w1': (196, 196), 'w3': (184, 184), 'w4': (100, 100)}
2025-05-20 09:51:31,369 - visualizer - INFO - 调整w4波段数据从(100, 100)到(184, 184)
2025-05-20 09:51:31,369 - visualizer - INFO - 红色通道(22μm)数据范围: 346.93670654296875-1491.30029296875
2025-05-20 09:51:31,370 - visualizer - INFO - 绿色通道(12μm)数据范围: 1396.326416015625-8065.0732421875
2025-05-20 09:51:31,377 - visualizer - INFO - 调整w1波段数据从(196, 196)到(184, 184)
2025-05-20 09:51:31,377 - visualizer - INFO - 蓝色通道(3.4μm)数据范围: 32.832855224609375-2571.968505859375
2025-05-20 09:51:31,379 - visualizer - INFO - 使用Lupton RGB方法，stretch=2448.26, Q=10
2025-05-20 09:51:31,379 - visualizer - INFO - 各波段99.5%百分位数: R=785.95, G=4896.52, B=1324.89
2025-05-20 09:51:31,384 - visualizer - INFO - RGB数据形状: (184, 184, 3), 类型: float32
2025-05-20 09:51:31,384 - visualizer - INFO - Red通道数据范围：0.05882352963089943-0.2078431397676468，均值：0.0999
2025-05-20 09:51:31,385 - visualizer - INFO - Green通道数据范围：0.3490196168422699-0.9372549057006836，均值：0.5253
2025-05-20 09:51:31,385 - visualizer - INFO - Blue通道数据范围：0.007843137718737125-0.3960784375667572，均值：0.0514
2025-05-20 09:51:32,792 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:51:32,798 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:51:32,805 - visualizer - INFO - 按照要求不显示Gaia星
2025-05-20 09:51:32,806 - visualizer - INFO - 设置坐标刻度间隔: RA=10.000度, Dec=10.000度
2025-05-20 09:51:33,702 - visualizer - INFO - 成功保存RGB图像到: results/batch_single_folder\visualizations\G006.160-00.608_wise_rgb.png
2025-05-20 09:51:33,702 - main_single_folder - INFO - 成功保存WISE RGB图像到: results/batch_single_folder\visualizations\G006.160-00.608_wise_rgb.png
2025-05-20 09:51:33,702 - visualizer - INFO - 绘制消光-距离散点图，输出到: results/batch_single_folder\visualizations\G006.160-00.608_extinction_distance.png
2025-05-20 09:51:35,121 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G006.160-00.608_extinction_distance.png
2025-05-20 09:51:35,122 - main_single_folder - INFO - 成功保存消光-距离散点图到: results/batch_single_folder\visualizations\G006.160-00.608_extinction_distance.png
2025-05-20 09:51:35,194 - main_single_folder - INFO - 成功保存处理报告到: results/batch_single_folder\reports\G006.160-00.608_report.txt
2025-05-20 09:51:35,194 - main_single_folder - INFO - 处理G006.160-00.608完成
