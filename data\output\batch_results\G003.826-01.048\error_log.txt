处理时间: 2025-04-24 04:33:54
耗时: 4.01秒

返回码: 1

标准输出:
错误: 在源表中未找到G003.826-01.048


标准错误:
2025-04-24 04:33:54,162 - main - INFO - 输出目录: data/output/batch_results\G003.826-01.048
2025-04-24 04:33:54,163 - main - INFO - 加载源G003.826-01.048的信息
2025-04-24 04:33:54,163 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
H:\Augment\Parallax distances\src\data_manager.py:42: FutureWarning: The 'delim_whitespace' keyword in pd.read_csv is deprecated and will be removed in a future version. Use ``sep='\s+'`` instead
  df = pd.read_csv(catalog_path, delim_whitespace=True, comment='#')
2025-04-24 04:33:54,169 - data_manager - INFO - 成功加载HII区域源表，共458条记录
2025-04-24 04:33:54,194 - main - ERROR - 在源表中未找到G003.826-01.048
2025-04-24 04:33:54,194 - main - ERROR - 加载源信息失败: 在源表中未找到G003.826-01.048
2025-04-24 04:33:54,195 - main - ERROR - 处理失败: 在源表中未找到G003.826-01.048
Traceback (most recent call last):
  File "H:\Augment\Parallax distances\main.py", line 394, in main
    results = process_hii_region(args)
              ^^^^^^^^^^^^^^^^^^^^^^^^
  File "H:\Augment\Parallax distances\main.py", line 170, in process_hii_region
    source_info = load_source_info(args.source, args, config)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "H:\Augment\Parallax distances\main.py", line 117, in load_source_info
    raise ValueError(f"在源表中未找到{source_name}")
ValueError: 在源表中未找到G003.826-01.048
