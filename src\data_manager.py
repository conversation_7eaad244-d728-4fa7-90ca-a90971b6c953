"""
数据管理模块

负责加载、处理和管理项目中使用的各种天文数据。
"""

import os
import numpy as np
import pandas as pd
from astropy.table import Table, vstack, join
from astropy.io import fits
from astropy.coordinates import SkyCoord
import astropy.units as u
from astropy.wcs import WCS

from src.utils.logger import setup_logger
from src.utils.helpers import load_config, ensure_directory, angular_separation

# 设置日志记录器
logger = setup_logger(name='data_manager')

def load_hii_region_catalog(catalog_path=None, config_path='config/default_config.yaml'):
    """
    加载HII区域源表

    Args:
        catalog_path: 源表路径，如果为None则从配置文件读取
        config_path: 配置文件路径

    Returns:
        pandas.DataFrame: HII区域源表
    """
    if catalog_path is None:
        config = load_config(config_path)
        catalog_path = config['data_paths']['hii_region_catalog']

    logger.info(f"加载HII区域源表: {catalog_path}")

    try:
        # 读取数据，根据实际文件格式设置列名
        # 源表格式为：源名称 分类 有效半径(角秒) 距离(kpc) 上误差 下误差
        column_names = ['source_name', 'type', 'N', 'distance', 'distance_err_plus', 'distance_err_minus']
        df = pd.read_csv(catalog_path, delim_whitespace=True, comment='#', header=None, names=column_names)
        logger.info(f"成功加载HII区域源表，共{len(df)}条记录")
        return df
    except Exception as e:
        logger.error(f"加载HII区域源表失败: {str(e)}")
        raise

def load_gaia_data(source_name, ra, dec, radius, gaia_path=None, config_path='config/default_config.yaml'):
    """
    加载Gaia数据

    Args:
        source_name: 源名称
        ra: 赤经（度）
        dec: 赤纬（度）
        radius: 搜索半径（度）
        gaia_path: Gaia数据路径，如果为None则从配置文件读取
        config_path: 配置文件路径

    Returns:
        astropy.table.Table: Gaia数据表
    """
    if gaia_path is None:
        config = load_config(config_path)
        gaia_path = config['data_paths']['gaia_data']

    logger.info(f"为源{source_name}加载Gaia数据，中心坐标: RA={ra:.6f}, Dec={dec:.6f}, 半径={radius:.4f}度")

    try:
        # 根据提供的信息，Gaia数据存储在H:\Cursor\Parallax distances\gaia_data目录下
        # 文件命名格式为：gaia_[源名]_*.csv，例如：gaia_G351.651+00.510_20arcmin_5R.csv

        # 查找匹配的Gaia数据文件
        gaia_file_pattern = os.path.join(gaia_path, f"gaia_{source_name}_*.csv")
        import glob
        matching_files = glob.glob(gaia_file_pattern)

        if matching_files:
            # 如果找到匹配的文件，使用第一个
            gaia_file = matching_files[0]
            logger.info(f"找到Gaia数据文件: {gaia_file}")

            # 读取CSV文件
            import pandas as pd
            df = pd.read_csv(gaia_file)

            # 转换为astropy Table
            gaia_data = Table.from_pandas(df)

            # 确保列名符合我们的预期
            column_mapping = {
                'RA_ICRS': 'ra',
                'DE_ICRS': 'dec',
                'Plx': 'parallax',
                'e_Plx': 'parallax_error',
                'pmRA': 'pmra',
                'pmDE': 'pmde',
                'RUWE': 'ruwe',
                'Gmag': 'phot_g_mean_mag',
                'BP-RP': 'bp_rp',
                'bj_distance_geo': 'distance',
                'twomass_Jmag': 'j_m',
                'twomass_Hmag': 'h_m',
                'twomass_Kmag': 'k_m',
                'j_h': 'j_h',
                'h_ks': 'h_k'
            }

            # 重命名列
            for old_name, new_name in column_mapping.items():
                if old_name in gaia_data.colnames and new_name not in gaia_data.colnames:
                    gaia_data.rename_column(old_name, new_name)

            # 添加缺失的列
            required_columns = ['ra', 'dec', 'parallax', 'parallax_error', 'pmra', 'pmde',
                               'ruwe', 'phot_g_mean_mag', 'bp_rp', 'distance',
                               'j_m', 'h_m', 'k_m', 'j_h', 'h_k']

            for col in required_columns:
                if col not in gaia_data.colnames:
                    gaia_data[col] = np.nan

            # 添加区域列
            if 'region' not in gaia_data.colnames:
                gaia_data['region'] = 'unknown'

            logger.info(f"成功加载Gaia数据，共{len(gaia_data)}条记录")
        else:
            # 如果没有找到匹配的文件，创建一个空的表
            logger.warning(f"未找到匹配的Gaia数据文件: {gaia_file_pattern}")
            logger.info("创建空的Gaia数据表")

            # 创建一个空的表，包含必要的列
            from astropy.table import Table as AstropyTable
            from astropy.table import Column
            gaia_data = AstropyTable()

            # 创建一个包含一行数据的表，以确保列存在
            # 添加必要的列，基于提供的Gaia数据列信息
            gaia_data['source_id'] = Column([0], dtype='int64')
            gaia_data['ra'] = Column([0.0], dtype='float64')  # RA_ICRS
            gaia_data['dec'] = Column([0.0], dtype='float64')  # DE_ICRS
            gaia_data['parallax'] = Column([np.nan], dtype='float64')  # Plx
            gaia_data['parallax_error'] = Column([np.nan], dtype='float64')  # e_Plx
            gaia_data['pmra'] = Column([np.nan], dtype='float64')
            gaia_data['pmde'] = Column([np.nan], dtype='float64')  # pmDE
            gaia_data['ruwe'] = Column([np.nan], dtype='float64')  # RUWE
            gaia_data['phot_g_mean_mag'] = Column([np.nan], dtype='float64')  # Gmag
            gaia_data['bp_rp'] = Column([np.nan], dtype='float64')  # BP-RP
            gaia_data['distance'] = Column([np.nan], dtype='float64')  # bj_distance_geo

            # 添加2MASS相关列
            gaia_data['j_m'] = Column([np.nan], dtype='float64')  # twomass_Jmag
            gaia_data['h_m'] = Column([np.nan], dtype='float64')  # twomass_Hmag
            gaia_data['k_m'] = Column([np.nan], dtype='float64')  # twomass_Kmag
            gaia_data['j_h'] = Column([np.nan], dtype='float64')  # j_h
            gaia_data['h_k'] = Column([np.nan], dtype='float64')  # h_ks

            # 添加区域列
            gaia_data['region'] = Column(['unknown'], dtype='U10')

            # 删除这一行，以创建一个空表但保留列结构
            gaia_data.remove_row(0)

            logger.warning("使用空的Gaia数据表继续处理")

        # 筛选在搜索半径内的源
        coords = SkyCoord(ra=gaia_data['ra']*u.degree, dec=gaia_data['dec']*u.degree)
        center = SkyCoord(ra=ra*u.degree, dec=dec*u.degree)
        separations = coords.separation(center).degree
        mask = separations <= radius
        filtered_data = gaia_data[mask]

        logger.info(f"成功加载Gaia数据，共{len(filtered_data)}个源在搜索半径内")
        return filtered_data

    except Exception as e:
        logger.error(f"加载Gaia数据失败: {str(e)}")
        raise

def load_wise_fits(source_name, wise_base_path=None, config_path='config/default_config.yaml', band='12'):
    """
    加载WISE FITS图像

    Args:
        source_name: 源名称
        wise_base_path: WISE数据基础路径，如果为None则从配置文件读取
        config_path: 配置文件路径
        band: WISE波段，可以是'3.4', '4.6', '12', '22'

    Returns:
        tuple: (image_data, header, wcs)
    """
    if wise_base_path is None:
        config = load_config(config_path)
        wise_base_path = config['data_paths']['wise_fits_base']

    # 构建WISE FITS文件路径
    wise_dir = os.path.join(wise_base_path, source_name)

    logger.info(f"为源{source_name}加载WISE FITS图像，路径: {wise_dir}")

    try:
        # 列出目录中的所有文件，用于调试
        all_files = os.listdir(wise_dir)
        logger.info(f"目录中的所有文件: {all_files}")

        # 查找指定波段的WISE FITS文件
        band_str = str(band)  # 确保band是字符串
        logger.info(f"查找WISE {band_str}μm波段的FITS文件")

        # 查找WISE FITS文件，使用正确的文件命名格式
        fits_files = [f for f in all_files
                     if f.endswith('.fits') and f'_wise_{band_str}' in f.lower()]
        logger.info(f"第一次匹配结果: {fits_files}")

        # 如果没有找到，尝试更宽松的匹配
        if not fits_files:
            fits_files = [f for f in all_files
                         if f.endswith('.fits') and f'wise_{band_str}' in f.lower()]
            logger.info(f"第二次匹配结果: {fits_files}")

        # 如果仍然没有找到，尝试最宽松的匹配
        if not fits_files:
            fits_files = [f for f in all_files
                         if f.endswith('.fits') and band_str in f.lower()]
            logger.info(f"第三次匹配结果: {fits_files}")

        if not fits_files:
            logger.error(f"在{wise_dir}中未找到WISE {band_str}μm波段的FITS文件")
            raise FileNotFoundError(f"未找到WISE {band_str}μm波段的FITS文件")

        # 使用第一个匹配的文件
        fits_file = os.path.join(wise_dir, fits_files[0])
        logger.info(f"使用WISE FITS文件: {fits_file}")

        # 读取FITS文件
        with fits.open(fits_file) as hdul:
            # 获取主数据和头信息
            image_data = hdul[0].data
            header = hdul[0].header

            # 检查数据是否有效
            if image_data is None:
                logger.error(f"FITS文件{fits_file}中的数据为None")
                raise ValueError(f"FITS文件{fits_file}中的数据为None")

            # 检查数据是否包含NaN或无穷大
            if np.all(~np.isfinite(image_data)):
                logger.error(f"FITS文件{fits_file}中的所有数据都是NaN或无穷大")
                raise ValueError(f"FITS文件{fits_file}中的所有数据都是NaN或无穷大")

            # 记录数据统计信息
            valid_data = image_data[np.isfinite(image_data)]
            if len(valid_data) > 0:
                logger.info(f"FITS数据统计: 最小值={np.min(valid_data)}, 最大值={np.max(valid_data)}, 均值={np.mean(valid_data)}, 中位数={np.median(valid_data)}")
                logger.info(f"有效数据点数量: {len(valid_data)}/{image_data.size} ({len(valid_data)/image_data.size*100:.2f}%)")
            else:
                logger.warning(f"FITS文件{fits_file}中没有有效数据")

            # 创建WCS对象
            wcs = WCS(header)

            logger.info(f"成功加载WISE FITS图像，尺寸: {image_data.shape}")
            return image_data, header, wcs

    except Exception as e:
        logger.error(f"加载WISE FITS图像失败: {str(e)}")
        raise

def load_wise_multiband(source_name, wise_base_path=None, config_path='config/default_config.yaml',
                    bands=('3.4', '12', '22')):
    """
    加载多波段WISE FITS图像

    Args:
        source_name: 源名称
        wise_base_path: WISE数据基础路径，如果为None则从配置文件读取
        config_path: 配置文件路径
        bands: 要加载的WISE波段列表，默认为('3.4', '12', '22')，对应W1, W3, W4

    Returns:
        tuple: (wise_data, wcs)，其中wise_data是包含各波段数据的字典，wcs是世界坐标系对象
    """
    logger.info(f"为源{source_name}加载多波段WISE数据: {bands}")

    try:
        wise_data = {}
        wcs = None

        # 加载每个波段的数据
        for band in bands:
            try:
                image_data, header, band_wcs = load_wise_fits(
                    source_name, wise_base_path, config_path, band=band)

                # 使用波段名称作为键存储数据
                band_key = f'w{1 if band == "3.4" else 2 if band == "4.6" else 3 if band == "12" else 4}'
                wise_data[band_key] = image_data

                # 使用第一个成功加载的波段的WCS作为返回值
                if wcs is None:
                    wcs = band_wcs
                    logger.info(f"使用{band}μm波段的WCS作为参考")

                logger.info(f"成功加载WISE {band}μm波段数据，尺寸: {image_data.shape}")

            except Exception as e:
                logger.warning(f"加载WISE {band}μm波段数据失败: {str(e)}")
                wise_data[f'w{1 if band == "3.4" else 2 if band == "4.6" else 3 if band == "12" else 4}'] = None

        # 检查是否至少加载了一个波段
        if wcs is None:
            raise ValueError("未能成功加载任何WISE波段数据")

        logger.info(f"成功加载多波段WISE数据，波段: {list(wise_data.keys())}")
        return wise_data, wcs

    except Exception as e:
        logger.error(f"加载多波段WISE数据失败: {str(e)}")
        raise

def cross_match_catalogs(catalog1, catalog2, max_separation=1.0, ra1_key='ra', dec1_key='dec',
                        ra2_key='ra', dec2_key='dec', frame1='icrs', frame2='icrs'):
    """
    交叉匹配两个星表

    Args:
        catalog1: 第一个星表 (astropy.table.Table)
        catalog2: 第二个星表 (astropy.table.Table)
        max_separation: 最大匹配距离（角秒）
        ra1_key: catalog1中的赤经列名
        dec1_key: catalog1中的赤纬列名
        ra2_key: catalog2中的赤经列名
        dec2_key: catalog2中的赤纬列名
        frame1: catalog1的坐标系统 ('icrs', 'fk5', 'galactic', etc.)
        frame2: catalog2的坐标系统 ('icrs', 'fk5', 'galactic', etc.)

    Returns:
        astropy.table.Table: 匹配后的表
    """
    logger.info(f"交叉匹配星表，最大匹配距离: {max_separation}角秒")
    logger.info(f"坐标系统: catalog1={frame1}, catalog2={frame2}")

    try:
        # 创建坐标对象，并确保统一为ICRS坐标系
        coords1 = SkyCoord(ra=catalog1[ra1_key]*u.degree, dec=catalog1[dec1_key]*u.degree, frame=frame1).icrs
        coords2 = SkyCoord(ra=catalog2[ra2_key]*u.degree, dec=catalog2[dec2_key]*u.degree, frame=frame2).icrs

        # 执行匹配
        idx, d2d, _ = coords1.match_to_catalog_sky(coords2)

        # 筛选匹配距离小于阈值的源
        mask = d2d < max_separation * u.arcsec

        # 创建匹配索引
        matched_idx1 = np.arange(len(catalog1))[mask]
        matched_idx2 = idx[mask]

        # 提取匹配的源
        matched_cat1 = catalog1[matched_idx1]
        matched_cat2 = catalog2[matched_idx2]

        # 重命名第二个表的列，避免冲突
        for colname in matched_cat2.colnames:
            if colname in matched_cat1.colnames and colname not in [ra2_key, dec2_key]:
                matched_cat2.rename_column(colname, f"{colname}_2")

        # 合并表
        result = Table()
        for colname in matched_cat1.colnames:
            result[colname] = matched_cat1[colname]

        for colname in matched_cat2.colnames:
            if colname not in result.colnames:
                result[colname] = matched_cat2[colname]

        # 添加匹配距离列
        result['match_distance'] = d2d[mask].arcsec

        logger.info(f"成功匹配{len(result)}个源")
        return result

    except Exception as e:
        logger.error(f"交叉匹配星表失败: {str(e)}")
        raise

def save_processed_data(data, filename, output_dir=None, config_path='config/default_config.yaml'):
    """
    保存处理后的数据

    Args:
        data: 要保存的数据 (astropy.table.Table 或 pandas.DataFrame)
        filename: 文件名
        output_dir: 输出目录，如果为None则从配置文件读取
        config_path: 配置文件路径

    Returns:
        str: 保存的文件路径
    """
    if output_dir is None:
        config = load_config(config_path)
        output_dir = config['output_paths']['processed_data']

    ensure_directory(output_dir)
    output_path = os.path.join(output_dir, filename)

    logger.info(f"保存处理后的数据到: {output_path}")

    try:
        if isinstance(data, Table):
            # 检查是否有混合类型的列，这些列在保存为FITS时会出问题
            problematic_columns = []
            for col_name in data.colnames:
                col = data[col_name]
                # 检查是否是字符串列且包含混合类型
                if col.dtype.kind == 'U' and len(set(len(str(x)) for x in col if x is not None and x != '')) > 1:
                    problematic_columns.append(col_name)

            if problematic_columns:
                logger.warning(f"检测到可能导致保存问题的列: {problematic_columns}")
                # 创建一个新表，去掉有问题的列
                new_data = Table()
                for col_name in data.colnames:
                    if col_name not in problematic_columns:
                        new_data[col_name] = data[col_name]
                data = new_data
                logger.info(f"已移除有问题的列，继续保存")

            data.write(output_path, format='fits', overwrite=True)
        elif isinstance(data, pd.DataFrame):
            data.to_csv(output_path, index=False)
        else:
            raise TypeError("数据类型必须是astropy.table.Table或pandas.DataFrame")

        logger.info(f"成功保存数据")
        return output_path

    except Exception as e:
        logger.error(f"保存数据失败: {str(e)}")
        raise
