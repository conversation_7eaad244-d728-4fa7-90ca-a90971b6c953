"""
区域映射可视化模块

提供区域映射的可视化功能。
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.patches import Circle
from astropy.wcs import WCS
from astropy.visualization import simple_norm

from src.utils.logger import setup_logger
from src.utils.helpers import ensure_directory

# 设置日志记录器
logger = setup_logger(name='region_mapper.visualization')

def plot_regions(wise_data, region_masks, wcs, center_coord, r_eff, 
                source_name, output_dir, show_stars=False, star_coords=None):
    """
    绘制区域图像

    Args:
        wise_data: WISE图像数据
        region_masks: 区域掩模字典
        wcs: 世界坐标系
        center_coord: 中心坐标 (SkyCoord对象)
        r_eff: 有效半径（度）
        source_name: 源名称
        output_dir: 输出目录
        show_stars: 是否显示恒星，默认为False
        star_coords: 恒星坐标 (SkyCoord对象)，默认为None
    """
    try:
        # 创建可视化目录
        vis_dir = os.path.join(output_dir, 'visualizations')
        ensure_directory(vis_dir)
        
        # 设置图像路径
        regions_plot_path = os.path.join(vis_dir, f"{source_name}_regions.png")
        
        # 创建图像
        plt.figure(figsize=(10, 8))
        
        # 设置背景图像
        norm = simple_norm(wise_data, 'log', clip=False)
        plt.imshow(wise_data, origin='lower', cmap='gray', norm=norm)
        
        # 获取中心坐标在图像中的像素位置
        center_x, center_y = wcs.world_to_pixel(center_coord)
        
        # 计算像素尺度
        test_offset = 1.0 / 3600.0  # 1角秒
        test_coord = center_coord.directional_offset_by(0, test_offset * 3600)  # 1角秒
        test_x, test_y = wcs.world_to_pixel(test_coord)
        pixel_scale = np.sqrt((test_x - center_x)**2 + (test_y - center_y)**2)  # 像素/角秒
        
        # 计算有效半径（像素）
        r_eff_pixels = r_eff * 3600 / pixel_scale
        
        # 创建自定义颜色映射
        colors = [(0, 0, 1, 0.3), (0, 1, 0, 0.3), (1, 0, 0, 0.3)]  # 蓝、绿、红，半透明
        cmap = LinearSegmentedColormap.from_list('regions', colors, N=3)
        
        # 创建区域掩模图像
        region_image = np.zeros(wise_data.shape, dtype=int)
        if 'cavity' in region_masks:
            region_image[region_masks['cavity']] = 1
        if 'pdr' in region_masks:
            region_image[region_masks['pdr']] = 2
        if 'external' in region_masks:
            region_image[region_masks['external']] = 3
        
        # 绘制区域掩模
        plt.imshow(region_image, origin='lower', cmap=cmap, alpha=0.5)
        
        # 绘制HII区有效半径
        circle = Circle((center_x, center_y), r_eff_pixels, fill=False, color='red', linestyle='--', linewidth=2)
        plt.gca().add_patch(circle)
        
        # 绘制中心点
        plt.plot(center_x, center_y, 'r+', markersize=10)
        
        # 如果需要显示恒星
        if show_stars and star_coords is not None:
            # 获取恒星在图像中的像素位置
            star_x, star_y = wcs.world_to_pixel(star_coords)
            
            # 绘制恒星
            plt.plot(star_x, star_y, 'o', color='white', markersize=2, alpha=0.5)
        
        # 添加坐标轴
        plt.xlabel('RA')
        plt.ylabel('Dec')
        
        # 添加图例
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='blue', alpha=0.3, label='Cavity'),
            Patch(facecolor='green', alpha=0.3, label='PDR'),
            Patch(facecolor='red', alpha=0.3, label='External')
        ]
        plt.legend(handles=legend_elements, loc='upper right')
        
        # 添加标题
        plt.title(f"{source_name} Regions")
        
        # 保存图像
        plt.savefig(regions_plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"成功保存区域图像到: {regions_plot_path}")
        
    except Exception as e:
        logger.error(f"绘制区域图像失败: {str(e)}")

def plot_region_histogram(region_masks, source_name, output_dir):
    """
    绘制区域像素数量直方图

    Args:
        region_masks: 区域掩模字典
        source_name: 源名称
        output_dir: 输出目录
    """
    try:
        # 创建可视化目录
        vis_dir = os.path.join(output_dir, 'visualizations')
        ensure_directory(vis_dir)
        
        # 设置图像路径
        histogram_plot_path = os.path.join(vis_dir, f"{source_name}_region_histogram.png")
        
        # 计算各区域像素数量
        region_pixels = {}
        for region, mask in region_masks.items():
            region_pixels[region] = np.sum(mask)
        
        # 创建图像
        plt.figure(figsize=(8, 6))
        
        # 绘制直方图
        regions = list(region_pixels.keys())
        pixels = [region_pixels[region] for region in regions]
        colors = ['blue', 'green', 'red']  # 空腔、PDR、外部
        
        plt.bar(regions, pixels, color=colors[:len(regions)])
        
        # 添加数值标签
        for i, v in enumerate(pixels):
            plt.text(i, v + 0.1, str(v), ha='center')
        
        # 添加标题和标签
        plt.title(f"{source_name} Region Pixel Counts")
        plt.ylabel('Pixel Count')
        
        # 保存图像
        plt.savefig(histogram_plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"成功保存区域像素数量直方图到: {histogram_plot_path}")
        
    except Exception as e:
        logger.error(f"绘制区域像素数量直方图失败: {str(e)}")

def plot_region_star_counts(region_tags, source_name, output_dir):
    """
    绘制各区域恒星数量直方图

    Args:
        region_tags: 区域标签数组
        source_name: 源名称
        output_dir: 输出目录
    """
    try:
        # 创建可视化目录
        vis_dir = os.path.join(output_dir, 'visualizations')
        ensure_directory(vis_dir)
        
        # 设置图像路径
        star_counts_plot_path = os.path.join(vis_dir, f"{source_name}_region_star_counts.png")
        
        # 计算各区域恒星数量
        region_counts = {}
        for region in np.unique(region_tags):
            if region != 'unknown':
                region_counts[region] = np.sum(region_tags == region)
        
        # 创建图像
        plt.figure(figsize=(8, 6))
        
        # 绘制直方图
        regions = list(region_counts.keys())
        counts = [region_counts[region] for region in regions]
        colors = ['blue', 'green', 'red']  # 空腔、PDR、外部
        
        plt.bar(regions, counts, color=colors[:len(regions)])
        
        # 添加数值标签
        for i, v in enumerate(counts):
            plt.text(i, v + 0.1, str(v), ha='center')
        
        # 添加标题和标签
        plt.title(f"{source_name} Region Star Counts")
        plt.ylabel('Star Count')
        
        # 保存图像
        plt.savefig(star_counts_plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"成功保存各区域恒星数量直方图到: {star_counts_plot_path}")
        
    except Exception as e:
        logger.error(f"绘制各区域恒星数量直方图失败: {str(e)}")
