"""
等高线法PDR检测模块

使用等高线分析法检测PDR区域。
"""

import os
import numpy as np
from scipy import ndimage as ndi
from skimage import measure, morphology
from astropy.coordinates import SkyCoord
import astropy.units as u
from astropy.stats import sigma_clipped_stats
import matplotlib.pyplot as plt

from src.utils.logger import setup_logger
from src.utils.helpers import ensure_directory

# 设置日志记录器
logger = setup_logger(name='region_mapper.contour')

def define_pdr_mask_contour(processed_image, wcs, center_coord, r_eff,
                          search_radius_factor=3.0, n_contours=10, contour_start_nsigma=3.0,
                          source_name=None, output_dir=None, save_contour_plot=False):
    """
    使用等高线分析法定义PDR区域掩模

    Args:
        processed_image: 处理后的WISE图像
        wcs: 世界坐标系
        center_coord: 中心坐标 (SkyCoord对象)
        r_eff: 有效半径（度）
        search_radius_factor: PDR搜索半径因子（相对于r_eff），默认为3.0
        n_contours: 生成的等高线数量，默认为10
        contour_start_nsigma: 最低等高线水平（相对于背景标准差的倍数），默认为3.0
        source_name: 源名称，用于保存等高线图像
        output_dir: 输出目录，用于保存等高线图像
        save_contour_plot: 是否保存等高线图像，默认为False

    Returns:
        numpy.ndarray: PDR掩模（布尔数组）
    """
    try:
        # 获取中心坐标在图像中的像素位置
        center_x, center_y = wcs.world_to_pixel(center_coord)

        # 计算像素尺度
        test_offset = 1.0 / 3600.0  # 1角秒
        # 根据坐标系统类型创建测试坐标
        if center_coord.frame.name == 'galactic':
            test_coord = SkyCoord(l=center_coord.l + test_offset * u.degree,
                                 b=center_coord.b,
                                 frame='galactic')
        else:
            test_coord = SkyCoord(ra=center_coord.ra + test_offset * u.degree,
                                 dec=center_coord.dec,
                                 frame=center_coord.frame)
        test_x, test_y = wcs.world_to_pixel(test_coord)
        pixel_scale = np.sqrt((test_x - center_x)**2 + (test_y - center_y)**2) * 3600  # 像素/角秒

        # 计算PDR搜索半径（像素）
        r_eff_pixels = r_eff * 3600 / pixel_scale
        pdr_search_radius_pixels = r_eff * search_radius_factor * 3600 / pixel_scale

        # 创建距离图像
        y, x = np.ogrid[:processed_image.shape[0], :processed_image.shape[1]]
        distance_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)

        # 限制PDR搜索区域在指定半径内
        pdr_search_mask = distance_from_center <= pdr_search_radius_pixels

        # 使用sigma-clipping统计方法计算背景噪声水平
        mean, median, std = sigma_clipped_stats(processed_image, sigma=3.0)
        logger.info(f"背景统计: 均值={mean:.4f}, 中值={median:.4f}, 标准差={std:.4f}")

        # 计算等高线水平
        # 使用对数间距，确保低亮度区域有更多等高线
        min_level = median + contour_start_nsigma * std
        max_level = np.max(processed_image)

        if max_level <= min_level:
            logger.warning(f"最大亮度({max_level:.4f})小于等于最低等高线水平({min_level:.4f})，使用备用方法")
            # 使用备用方法：简单阈值
            threshold = median + 2 * std
            pdr_mask = (processed_image >= threshold) & pdr_search_mask
            pdr_mask = morphology.binary_closing(pdr_mask, morphology.disk(5))
            pdr_mask = ndi.binary_fill_holes(pdr_mask)
            logger.info(f"备用方法PDR掩模创建完成，覆盖{np.sum(pdr_mask)}个像素")
            return pdr_mask

        # 使用对数间距生成等高线水平
        levels = np.logspace(np.log10(min_level), np.log10(max_level), n_contours)
        logger.info(f"生成{len(levels)}个等高线水平，范围=[{min_level:.4f}, {max_level:.4f}]")

        # 查找所有等高线
        all_contours = []
        for level in levels:
            contours = measure.find_contours(processed_image, level)
            for contour in contours:
                # 计算轮廓的质心
                centroid_y, centroid_x = np.mean(contour, axis=0)

                # 计算轮廓到中心的距离
                dist_to_center = np.sqrt((centroid_x - center_x)**2 + (centroid_y - center_y)**2)

                # 计算轮廓的平均半径
                avg_radius = np.mean(np.sqrt((contour[:, 1] - centroid_x)**2 + (contour[:, 0] - centroid_y)**2))

                # 只保留在搜索区域内且长度足够的轮廓
                if dist_to_center <= pdr_search_radius_pixels and len(contour) >= 10:
                    all_contours.append({
                        'points': contour,
                        'level': level,
                        'length': len(contour),
                        'centroid': (centroid_y, centroid_x),
                        'dist_to_center': dist_to_center,
                        'avg_radius': avg_radius
                    })

        logger.info(f"找到{len(all_contours)}个等高线")

        if len(all_contours) < 2:
            logger.warning(f"等高线数量太少({len(all_contours)} < 2)，使用备用方法")
            # 使用备用方法：简单阈值
            threshold = median + 2 * std
            pdr_mask = (processed_image >= threshold) & pdr_search_mask
            pdr_mask = morphology.binary_closing(pdr_mask, morphology.disk(5))
            pdr_mask = ndi.binary_fill_holes(pdr_mask)
            logger.info(f"备用方法PDR掩模创建完成，覆盖{np.sum(pdr_mask)}个像素")
            return pdr_mask

        # 按照到中心的距离排序
        all_contours.sort(key=lambda c: c['dist_to_center'])

        # 尝试识别PDR内外边界
        # PDR内边界：通常是距离中心较近的长轮廓
        # PDR外边界：通常是距离中心较远的长轮廓

        # 按长度筛选轮廓，只保留长度在前50%的轮廓
        length_threshold = np.percentile([c['length'] for c in all_contours], 50)
        long_contours = [c for c in all_contours if c['length'] >= length_threshold]

        if len(long_contours) < 2:
            logger.warning(f"长轮廓数量太少({len(long_contours)} < 2)，使用所有轮廓")
            long_contours = all_contours

        # 按照到中心的距离排序
        long_contours.sort(key=lambda c: c['dist_to_center'])

        # 尝试识别内外边界
        inner_boundary = None
        outer_boundary = None

        # 如果轮廓数量足够，选择距离中心最近和最远的长轮廓作为内外边界
        if len(long_contours) >= 2:
            # 内边界：距离中心最近的长轮廓
            inner_boundary = long_contours[0]

            # 外边界：距离中心最远的长轮廓
            outer_boundary = long_contours[-1]

            logger.info(f"识别到内边界: 距离中心={inner_boundary['dist_to_center']:.1f}像素, 长度={inner_boundary['length']}像素")
            logger.info(f"识别到外边界: 距离中心={outer_boundary['dist_to_center']:.1f}像素, 长度={outer_boundary['length']}像素")
        else:
            logger.warning(f"轮廓数量不足，无法识别内外边界")
            # 使用备用方法：简单阈值
            threshold = median + 2 * std
            pdr_mask = (processed_image >= threshold) & pdr_search_mask
            pdr_mask = morphology.binary_closing(pdr_mask, morphology.disk(5))
            pdr_mask = ndi.binary_fill_holes(pdr_mask)
            logger.info(f"备用方法PDR掩模创建完成，覆盖{np.sum(pdr_mask)}个像素")
            return pdr_mask

        # 创建PDR掩模
        pdr_mask = np.zeros_like(processed_image, dtype=bool)

        # 使用内外边界创建PDR掩模
        # 将内外边界轮廓转换为掩模
        inner_mask = np.zeros_like(processed_image, dtype=bool)
        outer_mask = np.zeros_like(processed_image, dtype=bool)

        # 创建内边界掩模
        inner_contour = inner_boundary['points'].astype(int)
        inner_contour_y, inner_contour_x = inner_contour[:, 0], inner_contour[:, 1]
        valid_indices = (inner_contour_y >= 0) & (inner_contour_y < inner_mask.shape[0]) & \
                        (inner_contour_x >= 0) & (inner_contour_x < inner_mask.shape[1])
        inner_contour_y, inner_contour_x = inner_contour_y[valid_indices], inner_contour_x[valid_indices]
        inner_mask[inner_contour_y, inner_contour_x] = True
        inner_mask = ndi.binary_fill_holes(inner_mask)

        # 创建外边界掩模
        outer_contour = outer_boundary['points'].astype(int)
        outer_contour_y, outer_contour_x = outer_contour[:, 0], outer_contour[:, 1]
        valid_indices = (outer_contour_y >= 0) & (outer_contour_y < outer_mask.shape[0]) & \
                        (outer_contour_x >= 0) & (outer_contour_x < outer_mask.shape[1])
        outer_contour_y, outer_contour_x = outer_contour_y[valid_indices], outer_contour_x[valid_indices]
        outer_mask[outer_contour_y, outer_contour_x] = True
        outer_mask = ndi.binary_fill_holes(outer_mask)

        # PDR区域是外边界内且内边界外的区域
        pdr_mask = outer_mask & ~inner_mask & pdr_search_mask

        # 应用形态学操作，填充孔洞并平滑边界
        pdr_mask = morphology.binary_closing(pdr_mask, morphology.disk(3))
        pdr_mask = ndi.binary_fill_holes(pdr_mask)

        # 如果PDR区域太小，使用备用方法
        min_pdr_size = 100  # 最小像素数
        if np.sum(pdr_mask) < min_pdr_size:
            logger.warning(f"PDR区域太小 ({np.sum(pdr_mask)} < {min_pdr_size})，使用备用方法")
            # 使用备用方法：简单阈值
            threshold = median + 2 * std
            pdr_mask = (processed_image >= threshold) & pdr_search_mask
            pdr_mask = morphology.binary_closing(pdr_mask, morphology.disk(5))
            pdr_mask = ndi.binary_fill_holes(pdr_mask)
            logger.info(f"备用方法PDR掩模创建完成，覆盖{np.sum(pdr_mask)}个像素")
            return pdr_mask

        # 如果需要保存等高线图像
        if save_contour_plot and output_dir and source_name:
            save_contour_plot_image(
                processed_image, all_contours, inner_boundary, outer_boundary, pdr_mask,
                center_x, center_y, r_eff_pixels, pdr_search_radius_pixels,
                source_name, output_dir
            )

        logger.info(f"等高线法PDR掩模创建完成，覆盖{np.sum(pdr_mask)}个像素")
        return pdr_mask

    except Exception as e:
        logger.error(f"使用等高线方法定义PDR掩模失败: {str(e)}")
        # 出错时返回空掩模
        return np.zeros_like(processed_image, dtype=bool)

def save_contour_plot_image(processed_image, all_contours, inner_boundary, outer_boundary, pdr_mask,
                          center_x, center_y, r_eff_pixels, pdr_search_radius_pixels,
                          source_name, output_dir):
    """
    保存等高线图像

    Args:
        processed_image: 处理后的WISE图像
        all_contours: 所有等高线
        inner_boundary: 内边界轮廓
        outer_boundary: 外边界轮廓
        pdr_mask: PDR掩模
        center_x: 中心X坐标
        center_y: 中心Y坐标
        r_eff_pixels: 有效半径（像素）
        pdr_search_radius_pixels: PDR搜索半径（像素）
        source_name: 源名称
        output_dir: 输出目录
    """
    try:
        # 创建可视化目录
        vis_dir = os.path.join(output_dir, 'visualizations')
        ensure_directory(vis_dir)

        # 设置图像路径
        contour_plot_path = os.path.join(vis_dir, f"{source_name}_contours.png")

        # 创建图像
        plt.figure(figsize=(12, 10))

        # 绘制原始图像和所有等高线
        plt.subplot(221)
        plt.title("WISE Image with All Contours")
        plt.imshow(processed_image, origin='lower', cmap='viridis')
        for contour in all_contours:
            plt.plot(contour['points'][:, 1], contour['points'][:, 0], 'r-', linewidth=0.5)
        plt.plot(center_x, center_y, 'r+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False, color='r', linestyle='--')
        plt.gca().add_patch(circle)
        circle = plt.Circle((center_x, center_y), pdr_search_radius_pixels, fill=False, color='white')
        plt.gca().add_patch(circle)

        # 绘制内外边界
        plt.subplot(222)
        plt.title("Inner and Outer Boundaries")
        plt.imshow(processed_image, origin='lower', cmap='viridis')
        if inner_boundary:
            plt.plot(inner_boundary['points'][:, 1], inner_boundary['points'][:, 0], 'r-', linewidth=2, label='Inner')
        if outer_boundary:
            plt.plot(outer_boundary['points'][:, 1], outer_boundary['points'][:, 0], 'g-', linewidth=2, label='Outer')
        plt.plot(center_x, center_y, 'r+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False, color='r', linestyle='--')
        plt.gca().add_patch(circle)
        plt.legend()

        # 绘制PDR掩模
        plt.subplot(223)
        plt.title("PDR Mask")
        plt.imshow(pdr_mask, origin='lower', cmap='gray')
        plt.plot(center_x, center_y, 'r+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False, color='r', linestyle='--')
        plt.gca().add_patch(circle)

        # 绘制PDR区域叠加在原始图像上
        plt.subplot(224)
        plt.title("PDR Region on WISE Image")
        plt.imshow(processed_image, origin='lower', cmap='viridis')
        plt.imshow(pdr_mask, origin='lower', cmap='gray', alpha=0.3)
        plt.plot(center_x, center_y, 'r+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False, color='r', linestyle='--')
        plt.gca().add_patch(circle)

        # 调整布局并保存
        plt.tight_layout()
        plt.savefig(contour_plot_path, dpi=300)
        plt.close()

        logger.info(f"成功保存等高线图像到: {contour_plot_path}")

    except Exception as e:
        logger.error(f"保存等高线图像失败: {str(e)}")
