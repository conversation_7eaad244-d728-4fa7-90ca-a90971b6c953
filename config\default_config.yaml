# 默认配置文件
# 数据路径
data_paths:
  hii_region_catalog: "H:/Augment/Parallax distances/Parallax-based distances.dat"
  gaia_data: "H:/Cursor/Parallax distances-Data/gaia_data"
  wise_fits_base: "U:/Data/Bubbles/Wise bubbles"

# 输出路径
output_paths:
  processed_data: "data/processed"
  results: "data/output"
  logs: "logs"

# 数据处理参数
processing:
  # Gaia质量筛选参数
  gaia_quality:
    parallax_snr_min: 5.0  # 最小视差信噪比
    ruwe_max: 1.4         # 最大RUWE值

  # 区域定义参数
  region_definition:
    pdr_threshold: 0.5    # PDR区域的WISE 12μm通量阈值（相对于峰值）
    cavity_radius_factor: 1.0  # 空腔区域的半径因子（相对于R_eff）
    pdr_outer_radius_factor: 5.0  # PDR外边界的半径因子
    external_radius_factor: 5.0  # 外部区域的半径因子

  # PDR区域映射参数
  region_mapping:
    pdr:
      # 阈值方法参数
      threshold_factor: 0.3  # 阈值因子（相对于百分位数或峰值）
      max_radius_factor: 5.0  # 最大半径因子（相对于r_eff）
      search_radius_factor: 5.0  # PDR搜索半径因子（相对于r_eff）
      morphology_disk_size: 5  # 形态学操作磁盘大小

      # 等高线方法参数
      use_contour_method: false  # 是否使用等高线方法
      n_contours: 10  # 生成的等高线数量
      contour_start_nsigma: 1.0  # 最低等高线水平（相对于背景标准差的倍数）

      # W3/W4波段比值方法参数
      use_ratio_method: true  # 是否使用W3/W4波段比值方法
      save_ratio_plot: true  # 是否保存比值图像

      # 改进的W3/W4波段比值方法参数
      use_improved_method: true  # 是否使用改进的方法
      w4_threshold_factor: 0.7  # W4阈值因子（用于空腔检测）
      ratio_upper_limit: 0.5  # W3/W4比值上限（用于空腔检测）
      min_cavity_size_factor: 0.2  # 最小空腔大小因子
      cavity_weight: 0.7  # W4波段在空腔检测中的权重
      ratio_weight: 0.3  # W3/W4比值在空腔检测中的权重
      morphology_disk_size: 5  # 形态学操作磁盘大小
      save_improved_plot: true  # 是否保存改进方法的图像

      # 标准天文流程PDR检测方法参数
      use_standard_astro_method: false  # 是否使用标准天文流程方法
      background_box_size: 50  # 背景估计网格大小
      background_filter_size: 3  # 背景滤波核大小
      radial_analysis_factor: 5.0  # 径向分析区域因子（相对于r_eff）
      histogram_bins: 100  # 直方图分析的bin数
      morphology_remove_small_objects: 100  # 移除小对象的最小像素数
      save_standard_plot: true  # 是否保存标准方法的图像

      # 背景区域选择参数
      min_background_radius_factor: 2.0  # 最小背景区域距离因子（相对于r_eff）
      background_percentile_threshold: 25.0  # 背景像素值百分位数阈值
      uniformity_threshold: 0.4  # 均匀性阈值（标准差/均值）

  # YSO识别参数
  yso_identification:
    w1w2_min: 0.8  # W1-W2颜色下限

  # 消光计算参数
  extinction:
    rv: 3.1  # 消光律R_V值

  # 距离分析参数
  distance_analysis:
    bin_size: 50  # 距离分析的bin大小（单位：pc）
    min_stars_per_bin: 5  # 每个bin的最小恒星数

# 日志配置
logging:
  level: "INFO"  # 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_rotation: 5  # 保留的日志文件数量
