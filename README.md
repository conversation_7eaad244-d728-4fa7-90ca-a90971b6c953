# HII区分子云距离估计项目

## 项目概述

本项目旨在通过分析Gaia恒星的消光-距离关系，估计HII区域宿主分子云的距离。该方法基于以下核心假设：宿主分子云会在其距离处导致背景恒星消光的明显"跳变"。通过比较HII区域中心空腔、PDR（光解离区）和外部区域的消光跳变特征，可以区分目标云和前景云的贡献。

## 方法原理

- **前景云（D_fg < D_cloud）**：预期在所有三个区域（空腔、PDR、外部）造成相似的消光跳变ΔA_V(fg)。
- **目标云（D_cloud）**：预期在PDR和外部区域造成较大的消光跳变ΔA_V(PDR)和ΔA_V(External)，但在空腔区域的跳变ΔA_V(Cavity)明显较小。这种差异化特征是识别目标云的关键。

## 目录结构

```
Parallax distances/
│
├── data/                  # 存放数据文件
│   ├── raw/               # 原始数据
│   ├── processed/         # 处理后的数据
│   └── output/            # 输出结果
│
├── src/                   # 源代码
│   ├── __init__.py
│   ├── data_manager.py    # 数据获取和处理
│   ├── region_mapper.py   # 区域定义
│   ├── star_selector.py   # 恒星样本选择
│   ├── extinction_estimator.py  # 消光估计
│   ├── distance_analyzer.py     # 距离分析
│   └── utils/             # 工具函数
│       ├── __init__.py
│       ├── plotting.py     # 绘图工具
│       ├── helpers.py      # 辅助函数
│       └── logger.py       # 日志配置
│
├── notebooks/             # Jupyter笔记本
│   ├── exploratory/       # 探索性分析
│   └── results/           # 结果展示
│
├── config/                # 配置文件
│   └── default_config.yaml  # 默认配置
│
├── logs/                  # 日志文件夹
│
├── main.py                # 主程序入口
├── README.md              # 项目说明
└── requirements.txt       # 依赖包
```

## 数据来源

- **HII区源表**：`H:\Augment\Parallax distances\Parallax-based distances.dat`
- **Gaia数据**：`H:\Cursor\Parallax distances\gaia_data`
- **WISE FITS数据**：`U:\Data\Bubbles\Wise bubbles\[源名]`

## 工作流程

1. **数据获取**：加载Gaia DR3、2MASS和WISE数据。
2. **区域定义**：使用WISE 12μm图像定义空腔、PDR和外部区域。
   - **PDR区域**：使用等高线分析法识别PDR壳层的内外边界，或使用阈值法基于亮度阈值识别PDR区域。
   - **空腔区域**：HII区有效半径内且不在PDR区域内的区域。
   - **外部区域**：在最大搜索半径内（默认为5R）且不在PDR区域和空腔区域内的区域。
3. **恒星样本准备**：应用质量筛选，识别并移除YSO。
4. **消光计算**：使用近红外或Gaia颜色计算每颗恒星的消光值。
5. **消光-距离分析**：分析各区域的消光-距离关系，识别跳变。
6. **距离估计**：基于跳变特征估计分子云距离。

## 使用方法

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行分析

```bash
python main.py --source [源名称] --config [配置文件路径]
```

可选参数：
- `--ra`：赤经（度）
- `--dec`：赤纬（度）
- `--radius`：有效半径（度）
- `--output-dir`：输出目录
- `--skip-steps`：跳过指定的处理步骤

### 示例

```bash
python main.py --source G000.4+00.4 --output-dir results/G000.4
```

## 输出结果

- **消光-距离图**：各区域的消光-距离散点图和中位数曲线。
- **区域比较图**：比较不同区域的消光跳变。
- **结果报告**：包含估计的分子云距离、不确定度、置信度和证据。

## 注意事项

- 本项目遵循`cursor_rules.md`中定义的Python天文数据处理规范。
- 处理过程中的日志记录在`logs`目录中，便于追踪和调试。
- 对于缺少WISE点源数据的情况，可能需要额外下载。

## 更新日志

### 2025-05-21 更新

1. **改进的HII区空腔和PDR区域检测**：
   - 实现了新的HII区空腔检测方法，使用W4波段(22μm)高强度特征和W3/W4比值低值特征
   - 改进了PDR区域检测，先识别空腔，然后在其周围寻找PDR区域
   - 使用DBSCAN聚类算法自动识别PDR区域，更好地适应不规则PDR结构
   - 基于到空腔边缘的距离计算PDR得分，优先选择靠近空腔的高W3/W4比值区域
   - 应用形态学操作连接相邻PDR簇，形成连续的PDR区域
   - 使用孔洞填充算法确保PDR区域的连续性
   - 提供多级备用策略，确保在各种情况下都能生成合理的空腔和PDR掩模
   - 改进可视化输出，显示空腔、PDR区域和组合视图
   - 模块化实现，创建了`improved_ratio_method.py`文件，便于维护和扩展

2. **配置系统更新**：
   - 添加改进方法的参数设置，包括W4阈值因子、W3/W4比值上限、空腔权重等
   - 支持在传统方法和改进方法之间灵活切换
   - 默认启用改进方法，提供更准确的HII区空腔和PDR区域识别

### 2025-05-20 更新

1. **坐标系统问题修复**：
   - 修复了`ratio_method.py`中的坐标系统处理问题，确保正确处理银道坐标系
   - 改进了像素尺度计算逻辑，根据坐标系统类型选择正确的坐标偏移方式
   - 增加了对银道坐标系的明确支持，使用`l`和`b`属性而非`ra`和`dec`
   - 确保在不同坐标系统下都能正确计算像素尺度，提高PDR区域检测的准确性
   - 测试了多个源，验证了修复的有效性

### 2025-04-29 更新

1. **代码模块化重构**：
   - 对`region_mapper.py`进行模块化重构，将其拆分为多个专注于特定功能的模块
   - 创建了`src/region_mapper/`目录，包含以下模块：
     - `__init__.py`：主入口点，导出所有公共函数
     - `base.py`：基础函数和通用工具
     - `threshold_method.py`：阈值法PDR检测
     - `contour_method.py`：等高线法PDR检测
     - `ratio_method.py`：波段比值法PDR检测
     - `visualization.py`：可视化功能
   - 保留原始`region_mapper.py`文件作为兼容性包装器，确保现有代码不会中断
   - 改进了代码组织，使其更易于维护和扩展
   - 每个模块都有专门的日志记录器，便于调试和跟踪

2. **依赖项更新**：
   - 添加了`scikit-learn`依赖，用于实现聚类功能
   - 更新了`requirements.txt`文件，确保所有依赖项都正确列出

### 2025-04-28 更新

1. **PDR区域检测算法高级改进**：
   - 完全重写W3/W4波段比值方法，使用聚类技术自动识别PDR区域
   - 移除了手动设置的比值上下限，使算法能够自适应不同源的特性
   - 实现了基于KMeans聚类的PDR区域自动识别，结合比值和空间位置信息
   - 使用轮廓系数(Silhouette Score)自动确定最佳聚类数(2-4)
   - 分析各聚类的特征（平均比值、标准差、大小、距离中心距离、紧凑性）
   - 基于聚类特征智能识别PDR区域，适应不同源的特性
   - 提供多级备用策略，确保在各种情况下都能生成合理的PDR掩模
   - 改进可视化输出，显示聚类分析结果和PDR区域识别过程

2. **配置系统简化**：
   - 简化W3/W4波段比值方法的参数设置，移除了手动比值范围设置
   - 默认启用聚类方法，提供更智能的PDR区域识别
   - 改进报告生成，详细记录聚类方法的参数和结果

### 2025-04-27 更新

1. **PDR区域检测算法新方法**：
   - 实现了基于WISE W3(12μm)/W4(22μm)波段比值的PDR区域检测方法
   - 利用PDR区域在中红外波段的特定光谱特征，通过波段比值识别PDR区域
   - 自动加载和处理W3和W4波段数据，确保尺寸一致性
   - 应用高斯平滑减少噪声影响，提高比值计算的稳定性
   - 使用百分位数限制极值，避免异常值影响比值分析
   - 基于比值范围创建PDR掩模，更准确地识别PDR区域的物理特性
   - 添加自适应调整机制，当PDR区域太小时自动扩大比值范围
   - 提供备用策略，当比值方法失效时回退到基于W3波段亮度的阈值方法
   - 生成详细的比值分析可视化，包括比值分布直方图和PDR区域轮廓

2. **配置系统改进**：
   - 在配置文件中添加W3/W4波段比值方法的参数设置
   - 支持在三种PDR检测方法（比值法、等高线法、阈值法）之间灵活切换
   - 默认启用比值法，提供更准确的PDR区域识别
   - 改进报告生成，显示比值法的参数信息和结果

### 2025-04-26 更新

1. **PDR区域检测算法改进**：
   - 实现了基于等高线分析的PDR区域检测方法，提高了PDR壳层识别的准确性
   - 使用sigma-clipping统计方法计算背景噪声水平，更加稳健地处理异常值
   - 生成多个等高线水平，从背景噪声水平到峰值亮度，使用对数间距确保低亮度区域有更多等高线
   - 筛选等高线，识别PDR内外边界，基于长度、质心位置和平均半径等特征
   - 通过内外边界等高线对创建PDR掩模，更准确地表示PDR壳层结构
   - 添加多种备用策略，处理无法找到清晰边界的情况
   - 在配置文件中添加新参数，支持在等高线方法和原有阈值方法之间切换

2. **配置系统改进**：
   - 重组配置文件结构，添加专门的PDR区域映射参数部分
   - 添加等高线方法的参数配置，包括等高线数量、最低等高线水平等
   - 改进报告生成，根据使用的方法显示相应的参数信息

### 2025-04-25 更新（三）

1. **WISE RGB图像显示改进**：
   - 修复了坐标轴显示问题，确保坐标轴和刻度标签清晰可见
   - 增加坐标轴线宽和颜色对比度，提高可读性
   - 优化坐标刻度间隔，增加刻度数量以提供更详细的坐标信息
   - 简化图像标题，只保留源名称，减少视觉干扰
   - 确保坐标轴边框可见，增强图像的专业性
   - 添加坐标轴框架，使坐标系统更加明确

### 2025-04-25 更新（二）

1. **WISE RGB图像生成优化**：
   - 使用Astropy的make_lupton_rgb函数实现专业的天文RGB图像合成
   - 基于Lupton et al. (2004)论文中的方法，使用Q=10和动态stretch参数
   - 使用99.5%百分位数计算最佳stretch值，确保图像对比度合适
   - 确保三个波段最亮的区域显示为白色，符合天文图像标准
   - 将RGB波段标注移至图像内部，提高可读性
   - 坐标轴区域使用白色背景和黑色文字，图像区域保持黑色背景

### 2025-04-25 更新（一）

1. **WISE RGB图像处理改进**：
   - 改进了多波段图像的尺寸处理逻辑，使用中位数尺寸而非最小尺寸
   - 采用双三次插值（bicubic interpolation）进行图像重采样，提高图像质量
   - 优化了坐标轴刻度标记，减少到3-5个值，使图像更加清晰易读
   - 自动计算合适的刻度间隔，确保刻度值为"漂亮"的数字（如0.1、0.2、0.5等）
   - 保持图像的原始宽高比，避免图像变形

### 2025-04-24 更新（五）

1. **WISE三波段RGB图像**：
   - 添加了使用WISE 3.4μm、12μm和22μm三个波段数据创建RGB彩色图像的功能
   - 使用22μm作为红色通道(R)、12μm作为绿色通道(G)、3.4μm作为蓝色通道(B)
   - 对每个波段的数据进行预处理，去除极值和无效值，调整对比度
   - 在RGB图像上叠加PDR区域轮廓和HII区域圆，提供更直观的多波段视图
   - 添加了波段信息标注，便于理解图像中不同颜色的含义

### 2025-04-24 更新（四）

1. **报告编码修复**：
   - 修复了中文报告的编码问题，使用`utf-8-sig`编码保存报告文件
   - 确保中文字符在Windows系统上正确显示

### 2025-04-24 更新（三）

1. **报告本地化**：
   - 将处理报告内容从英文翻译为中文，符合Cursor规则要求
   - 翻译了所有报告标题、参数名称和描述文本
   - 添加了区域名称的中文映射（cavity→空腔，external→外部）
   - 改进了错误消息和日志摘要的本地化

### 2025-04-24 更新（二）

1. **坐标系统改进**：
   - 添加了从HII区源名中解析银道坐标的功能
   - 将银道坐标转换为赤道坐标（ICRS），确保坐标系统一致性
   - 比较源名坐标和FITS文件坐标，如有较大差异则发出警告
   - 优先使用从源名解析的坐标，提高坐标准确性

### 2025-04-24 更新（一）

1. **改进PDR区域检测**：
   - 添加了`estimate_background`函数，从整个FITS数据获取背景值
   - 通过分块分析和百分位数统计，排除极值影响，获得更准确的背景估计
   - 背景估计使用第10百分位数，选取数值较低的区域

2. **限制PDR搜索范围**：
   - 修改`define_pdr_mask`函数，添加`search_radius_factor`参数（默认为3.0）
   - 限制PDR区域搜索范围在HII区域有效半径的3倍内
   - 在搜索前先减去估计的背景值，提高PDR区域检测的准确性

3. **可视化改进**：
   - 改进HII区域圆的显示，使用黄色虚线使其更加明显
   - 坐标轴使用度作为单位，标签更加清晰可读
   - 修复了坐标系统转换问题，确保WISE图像（FK5坐标系）和Gaia数据（ICRS坐标系）之间的坐标系统一致性

4. **报告生成改进**：
   - 添加背景估计和PDR搜索半径的信息
   - 详细记录PDR区域检测的参数和结果
