处理时间: 2025-05-20 09:59:18
耗时: 160.70秒

标准输出:

未能估计G007.015-00.271的分子云距离


标准错误:
2025-05-20 09:56:41,023 - main_single_folder - INFO - 输出目录: results/batch_single_folder
2025-05-20 09:56:41,023 - main_single_folder - INFO - 加载源G007.015-00.271的信息
2025-05-20 09:56:41,024 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
2025-05-20 09:56:41,029 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-05-20 09:56:41,031 - main_single_folder - INFO - 从源名解析银道坐标: l=7.015000, b=-0.271000
2025-05-20 09:56:41,037 - main_single_folder - INFO - 银道坐标转换为赤道坐标: RA=270.621581, Dec=-23.025280 (ICRS)
2025-05-20 09:56:41,037 - main_single_folder - INFO - 从源表加载信息: RA=270.621581, Dec=-23.025280, R_eff=0.152778度
2025-05-20 09:56:41,038 - main_single_folder - INFO - 步骤1：加载WISE数据
2025-05-20 09:56:41,043 - data_manager - INFO - 为源G007.015-00.271加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G007.015-00.271
2025-05-20 09:56:41,053 - data_manager - INFO - 目录中的所有文件: ['G007.015-00.271_ATLASGAL_870um.fits', 'G007.015-00.271_IRIS_100.fits', 'G007.015-00.271_MIPSGAL_24um.fits', 'G007.015-00.271_NVSS.fits', 'G007.015-00.271_WISE_12.fits', 'G007.015-00.271_WISE_22.fits', 'G007.015-00.271_WISE_3.4.fits', 'G007.015-00.271_WISE_4.6.fits']
2025-05-20 09:56:41,053 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:56:41,053 - data_manager - INFO - 第一次匹配结果: ['G007.015-00.271_WISE_12.fits']
2025-05-20 09:56:41,053 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G007.015-00.271\G007.015-00.271_WISE_12.fits
2025-05-20 09:56:41,130 - data_manager - INFO - FITS数据统计: 最小值=772.3701171875, 最大值=8623.435546875, 均值=1592.2484130859375, 中位数=1532.66650390625
2025-05-20 09:56:41,131 - data_manager - INFO - 有效数据点数量: 715682/715716 (100.00%)
2025-05-20 09:56:41,139 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (846, 846)
2025-05-20 09:56:41,139 - main_single_folder - INFO - WISE数据加载完成
2025-05-20 09:56:41,140 - main_single_folder - INFO - 步骤2：区域映射
2025-05-20 09:56:41,162 - main_single_folder - INFO - 使用W3/W4波段比值方法定义PDR区域
2025-05-20 09:56:41,163 - region_mapper - INFO - 使用W3/W4波段比值方法定义PDR掩模
2025-05-20 09:56:41,163 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:56:41,163 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:56:41,163 - region_mapper.ratio - INFO - 加载源G007.015-00.271的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:56:41,163 - region_mapper.ratio - INFO - 加载源G007.015-00.271的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:56:41,163 - data_manager - INFO - 为源G007.015-00.271加载多波段WISE数据: ('12', '22')
2025-05-20 09:56:41,168 - data_manager - INFO - 为源G007.015-00.271加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G007.015-00.271
2025-05-20 09:56:41,169 - data_manager - INFO - 目录中的所有文件: ['G007.015-00.271_ATLASGAL_870um.fits', 'G007.015-00.271_IRIS_100.fits', 'G007.015-00.271_MIPSGAL_24um.fits', 'G007.015-00.271_NVSS.fits', 'G007.015-00.271_WISE_12.fits', 'G007.015-00.271_WISE_22.fits', 'G007.015-00.271_WISE_3.4.fits', 'G007.015-00.271_WISE_4.6.fits']
2025-05-20 09:56:41,169 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:56:41,169 - data_manager - INFO - 第一次匹配结果: ['G007.015-00.271_WISE_12.fits']
2025-05-20 09:56:41,169 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G007.015-00.271\G007.015-00.271_WISE_12.fits
2025-05-20 09:56:41,186 - data_manager - INFO - FITS数据统计: 最小值=772.3701171875, 最大值=8623.435546875, 均值=1592.2484130859375, 中位数=1532.66650390625
2025-05-20 09:56:41,186 - data_manager - INFO - 有效数据点数量: 715682/715716 (100.00%)
2025-05-20 09:56:41,265 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (846, 846)
2025-05-20 09:56:41,266 - data_manager - INFO - 使用12μm波段的WCS作为参考
2025-05-20 09:56:41,266 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (846, 846)
2025-05-20 09:56:41,272 - data_manager - INFO - 为源G007.015-00.271加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G007.015-00.271
2025-05-20 09:56:41,272 - data_manager - INFO - 目录中的所有文件: ['G007.015-00.271_ATLASGAL_870um.fits', 'G007.015-00.271_IRIS_100.fits', 'G007.015-00.271_MIPSGAL_24um.fits', 'G007.015-00.271_NVSS.fits', 'G007.015-00.271_WISE_12.fits', 'G007.015-00.271_WISE_22.fits', 'G007.015-00.271_WISE_3.4.fits', 'G007.015-00.271_WISE_4.6.fits']
2025-05-20 09:56:41,272 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:56:41,272 - data_manager - INFO - 第一次匹配结果: ['G007.015-00.271_WISE_22.fits']
2025-05-20 09:56:41,273 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G007.015-00.271\G007.015-00.271_WISE_22.fits
2025-05-20 09:56:41,355 - data_manager - INFO - FITS数据统计: 最小值=342.60736083984375, 最大值=1646.81396484375, 均值=370.3656005859375, 中位数=363.6838073730469
2025-05-20 09:56:41,355 - data_manager - INFO - 有效数据点数量: 209764/209764 (100.00%)
2025-05-20 09:56:41,363 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (458, 458)
2025-05-20 09:56:41,363 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (458, 458)
2025-05-20 09:56:41,364 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w3', 'w4']
2025-05-20 09:56:41,374 - region_mapper.ratio - INFO - 调整W4波段数据从(458, 458)到(846, 846)
2025-05-20 09:56:41,374 - region_mapper.ratio - INFO - 调整W4波段数据从(458, 458)到(846, 846)
2025-05-20 09:56:41,515 - region_mapper.ratio - INFO - W3/W4比值范围: 3.22-6.00，均值: 4.27
2025-05-20 09:56:41,515 - region_mapper.ratio - INFO - W3/W4比值范围: 3.22-6.00，均值: 4.27
2025-05-20 09:56:41,525 - region_mapper.ratio - INFO - 像素尺度: 509.63 像素/角秒
2025-05-20 09:56:41,525 - region_mapper.ratio - INFO - 像素尺度: 509.63 像素/角秒
2025-05-20 09:56:41,525 - region_mapper.ratio - INFO - 有效半径: 1.1 像素
2025-05-20 09:56:41,525 - region_mapper.ratio - INFO - 有效半径: 1.1 像素
2025-05-20 09:56:41,525 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:56:41,525 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:56:42,295 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.756
2025-05-20 09:56:42,295 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.756
2025-05-20 09:56:42,371 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.619
2025-05-20 09:56:42,371 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.619
2025-05-20 09:56:42,448 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.630
2025-05-20 09:56:42,448 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.630
2025-05-20 09:56:42,449 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 09:56:42,449 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 09:56:42,515 - region_mapper.ratio - INFO - 聚类 0: 平均比值=5.22±0.14, 大小=217, 平均距离=6.2, 紧凑性=5.7
2025-05-20 09:56:42,515 - region_mapper.ratio - INFO - 聚类 0: 平均比值=5.22±0.14, 大小=217, 平均距离=6.2, 紧凑性=5.7
2025-05-20 09:56:42,516 - region_mapper.ratio - INFO - 聚类 1: 平均比值=5.91±0.13, 大小=94, 平均距离=7.7, 紧凑性=4.8
2025-05-20 09:56:42,516 - region_mapper.ratio - INFO - 聚类 1: 平均比值=5.91±0.13, 大小=94, 平均距离=7.7, 紧凑性=4.8
2025-05-20 09:56:42,516 - region_mapper.ratio - INFO - 选择聚类 1 作为PDR区域
2025-05-20 09:56:42,516 - region_mapper.ratio - INFO - 选择聚类 1 作为PDR区域
2025-05-20 09:56:42,599 - region_mapper.ratio - INFO - PDR掩模覆盖95个像素
2025-05-20 09:56:42,599 - region_mapper.ratio - INFO - PDR掩模覆盖95个像素
2025-05-20 09:56:42,600 - region_mapper.ratio - WARNING - PDR区域太小 (95 < 100)，使用备用方法
2025-05-20 09:56:42,600 - region_mapper.ratio - WARNING - PDR区域太小 (95 < 100)，使用备用方法
2025-05-20 09:56:42,735 - region_mapper.ratio - INFO - 备用方法后PDR掩模覆盖311个像素
2025-05-20 09:56:42,735 - region_mapper.ratio - INFO - 备用方法后PDR掩模覆盖311个像素
2025-05-20 09:56:47,485 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G007.015-00.271_w3w4_ratio.png
2025-05-20 09:56:47,485 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G007.015-00.271_w3w4_ratio.png
2025-05-20 09:56:47,486 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖311个像素
2025-05-20 09:56:47,486 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖311个像素
2025-05-20 09:56:47,538 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G007.015-00.271_region_masks.fits
2025-05-20 09:56:47,538 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G007.015-00.271_region_masks.fits
2025-05-20 09:56:47,543 - data_manager - INFO - 为源G007.015-00.271加载Gaia数据，中心坐标: RA=270.621581, Dec=-23.025280, 半径=0.7639度
2025-05-20 09:56:47,546 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances-Data/gaia_data\gaia_G007.015-00.271_46arcmin_5R.csv
H:\Augment\Parallax distances\src\data_manager.py:87: DtypeWarning: Columns (52,54) have mixed types. Specify dtype option on import or set low_memory=False.
  df = pd.read_csv(gaia_file)
2025-05-20 09:56:53,756 - data_manager - INFO - 成功加载Gaia数据，共372544条记录
2025-05-20 09:56:54,154 - data_manager - INFO - 成功加载Gaia数据，共372539个源在搜索半径内
2025-05-20 09:56:55,058 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G007.015-00.271_gaia_regions.fits
2025-05-20 09:58:56,253 - data_manager - WARNING - 检测到可能导致保存问题的列: ['TYC2', 'region']
2025-05-20 09:58:56,407 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:59:04,279 - data_manager - ERROR - 保存数据失败: Column 'RAVE5' contains unsupported object types or mixed types: {dtype('<U1'), dtype('<U16')}
2025-05-20 09:59:04,280 - main_single_folder - ERROR - 加载Gaia数据失败: Column 'RAVE5' contains unsupported object types or mixed types: {dtype('<U1'), dtype('<U16')}
2025-05-20 09:59:04,280 - main_single_folder - WARNING - 将使用空的Gaia数据继续处理
2025-05-20 09:59:04,597 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:59:04,597 - main_single_folder - INFO - 步骤3：恒星选择
2025-05-20 09:59:04,602 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-05-20 09:59:04,602 - star_selector - WARNING - 没有有效的视差数据，跳过视差信噪比筛选
2025-05-20 09:59:04,603 - star_selector - WARNING - 没有有效的RUWE数据，跳过RUWE筛选
2025-05-20 09:59:04,603 - star_selector - INFO - 质量筛选详细信息:
2025-05-20 09:59:04,603 - star_selector - INFO -   视差信噪比阈值: 5.0
2025-05-20 09:59:04,603 - star_selector - INFO -   RUWE阈值: 1.4
2025-05-20 09:59:04,604 - star_selector - INFO -   总体通过率: 0.0%
2025-05-20 09:59:04,604 - star_selector - INFO - 质量筛选完成，保留0/0个源
2025-05-20 09:59:04,604 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G007.015-00.271_filtered_data.fits
2025-05-20 09:59:04,617 - data_manager - INFO - 成功保存数据
2025-05-20 09:59:04,617 - main_single_folder - INFO - 保存质量筛选后的数据到: results/batch_single_folder\processed\G007.015-00.271_filtered_data.fits
2025-05-20 09:59:04,621 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-05-20 09:59:04,622 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-05-20 09:59:04,622 - star_selector - INFO - 筛选恒星样本，原始样本大小: 0
2025-05-20 09:59:04,622 - star_selector - INFO - 排除YSO后: 0/0个源
2025-05-20 09:59:04,622 - star_selector - INFO - 区域 cavity: 0个源
2025-05-20 09:59:04,623 - star_selector - INFO - 区域 pdr: 0个源
2025-05-20 09:59:04,623 - star_selector - INFO - 区域 external: 0个源
2025-05-20 09:59:04,623 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 0
2025-05-20 09:59:04,623 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G007.015-00.271_star_sample.fits
2025-05-20 09:59:04,635 - data_manager - INFO - 成功保存数据
2025-05-20 09:59:04,635 - main_single_folder - INFO - 恒星选择完成
2025-05-20 09:59:04,635 - main_single_folder - INFO - 步骤4：消光计算
2025-05-20 09:59:04,640 - extinction_estimator - INFO - 为0个恒星计算A_V，参数: {'rv': 3.1}
2025-05-20 09:59:04,640 - extinction_estimator - WARNING - 星表为空，无法计算A_V
2025-05-20 09:59:04,640 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G007.015-00.271_stars_with_av.fits
2025-05-20 09:59:04,652 - data_manager - INFO - 成功保存数据
2025-05-20 09:59:04,652 - main_single_folder - INFO - 消光计算完成
2025-05-20 09:59:04,652 - main_single_folder - INFO - 步骤5：距离分析
2025-05-20 09:59:04,652 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-05-20 09:59:04,653 - distance_analyzer - INFO - 将分析0个区域: 
2025-05-20 09:59:04,653 - distance_analyzer - INFO - 估计分子云距离
2025-05-20 09:59:04,653 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity, pdr, external
2025-05-20 09:59:04,653 - distance_analyzer - WARNING - 没有检测到任何跳变，无法估计距离
2025-05-20 09:59:04,677 - main_single_folder - INFO - 距离分析完成
2025-05-20 09:59:04,677 - main_single_folder - INFO - 步骤6：可视化和报告生成
2025-05-20 09:59:04,682 - data_manager - INFO - 为源G007.015-00.271加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G007.015-00.271
2025-05-20 09:59:04,682 - data_manager - INFO - 目录中的所有文件: ['G007.015-00.271_ATLASGAL_870um.fits', 'G007.015-00.271_IRIS_100.fits', 'G007.015-00.271_MIPSGAL_24um.fits', 'G007.015-00.271_NVSS.fits', 'G007.015-00.271_WISE_12.fits', 'G007.015-00.271_WISE_22.fits', 'G007.015-00.271_WISE_3.4.fits', 'G007.015-00.271_WISE_4.6.fits']
2025-05-20 09:59:04,682 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:59:04,682 - data_manager - INFO - 第一次匹配结果: ['G007.015-00.271_WISE_12.fits']
2025-05-20 09:59:04,682 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G007.015-00.271\G007.015-00.271_WISE_12.fits
2025-05-20 09:59:04,702 - data_manager - INFO - FITS数据统计: 最小值=772.3701171875, 最大值=8623.435546875, 均值=1592.2484130859375, 中位数=1532.66650390625
2025-05-20 09:59:04,702 - data_manager - INFO - 有效数据点数量: 715682/715716 (100.00%)
2025-05-20 09:59:04,710 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (846, 846)
2025-05-20 09:59:04,733 - visualizer - INFO - 绘制WISE图像和区域掩模，输出到: results/batch_single_folder\visualizations\G007.015-00.271_wise_regions_stars.png
2025-05-20 09:59:04,845 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:59:06,927 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:59:06,936 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:59:10,978 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G007.015-00.271_wise_regions_stars.png
2025-05-20 09:59:10,978 - main_single_folder - INFO - 成功保存WISE图像和区域掩模到: results/batch_single_folder\visualizations\G007.015-00.271_wise_regions_stars.png
2025-05-20 09:59:10,978 - main_single_folder - INFO - 加载WISE多波段数据用于RGB图像生成
2025-05-20 09:59:10,979 - data_manager - INFO - 为源G007.015-00.271加载多波段WISE数据: ('3.4', '12', '22')
2025-05-20 09:59:10,983 - data_manager - INFO - 为源G007.015-00.271加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G007.015-00.271
2025-05-20 09:59:10,984 - data_manager - INFO - 目录中的所有文件: ['G007.015-00.271_ATLASGAL_870um.fits', 'G007.015-00.271_IRIS_100.fits', 'G007.015-00.271_MIPSGAL_24um.fits', 'G007.015-00.271_NVSS.fits', 'G007.015-00.271_WISE_12.fits', 'G007.015-00.271_WISE_22.fits', 'G007.015-00.271_WISE_3.4.fits', 'G007.015-00.271_WISE_4.6.fits']
2025-05-20 09:59:10,984 - data_manager - INFO - 查找WISE 3.4μm波段的FITS文件
2025-05-20 09:59:10,984 - data_manager - INFO - 第一次匹配结果: ['G007.015-00.271_WISE_3.4.fits']
2025-05-20 09:59:10,984 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G007.015-00.271\G007.015-00.271_WISE_3.4.fits
2025-05-20 09:59:11,083 - data_manager - INFO - FITS数据统计: 最小值=1.6251144409179688, 最大值=4836.97900390625, 均值=202.37448120117188, 中位数=140.68963623046875
2025-05-20 09:59:11,084 - data_manager - INFO - 有效数据点数量: 811775/811801 (100.00%)
2025-05-20 09:59:11,092 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (901, 901)
2025-05-20 09:59:11,092 - data_manager - INFO - 使用3.4μm波段的WCS作为参考
2025-05-20 09:59:11,092 - data_manager - INFO - 成功加载WISE 3.4μm波段数据，尺寸: (901, 901)
2025-05-20 09:59:11,098 - data_manager - INFO - 为源G007.015-00.271加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G007.015-00.271
2025-05-20 09:59:11,098 - data_manager - INFO - 目录中的所有文件: ['G007.015-00.271_ATLASGAL_870um.fits', 'G007.015-00.271_IRIS_100.fits', 'G007.015-00.271_MIPSGAL_24um.fits', 'G007.015-00.271_NVSS.fits', 'G007.015-00.271_WISE_12.fits', 'G007.015-00.271_WISE_22.fits', 'G007.015-00.271_WISE_3.4.fits', 'G007.015-00.271_WISE_4.6.fits']
2025-05-20 09:59:11,098 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:59:11,098 - data_manager - INFO - 第一次匹配结果: ['G007.015-00.271_WISE_12.fits']
2025-05-20 09:59:11,099 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G007.015-00.271\G007.015-00.271_WISE_12.fits
2025-05-20 09:59:11,117 - data_manager - INFO - FITS数据统计: 最小值=772.3701171875, 最大值=8623.435546875, 均值=1592.2484130859375, 中位数=1532.66650390625
2025-05-20 09:59:11,117 - data_manager - INFO - 有效数据点数量: 715682/715716 (100.00%)
2025-05-20 09:59:11,126 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (846, 846)
2025-05-20 09:59:11,127 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (846, 846)
2025-05-20 09:59:11,132 - data_manager - INFO - 为源G007.015-00.271加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G007.015-00.271
2025-05-20 09:59:11,133 - data_manager - INFO - 目录中的所有文件: ['G007.015-00.271_ATLASGAL_870um.fits', 'G007.015-00.271_IRIS_100.fits', 'G007.015-00.271_MIPSGAL_24um.fits', 'G007.015-00.271_NVSS.fits', 'G007.015-00.271_WISE_12.fits', 'G007.015-00.271_WISE_22.fits', 'G007.015-00.271_WISE_3.4.fits', 'G007.015-00.271_WISE_4.6.fits']
2025-05-20 09:59:11,133 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:59:11,133 - data_manager - INFO - 第一次匹配结果: ['G007.015-00.271_WISE_22.fits']
2025-05-20 09:59:11,133 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G007.015-00.271\G007.015-00.271_WISE_22.fits
2025-05-20 09:59:11,141 - data_manager - INFO - FITS数据统计: 最小值=342.60736083984375, 最大值=1646.81396484375, 均值=370.3656005859375, 中位数=363.6838073730469
2025-05-20 09:59:11,141 - data_manager - INFO - 有效数据点数量: 209764/209764 (100.00%)
2025-05-20 09:59:11,149 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (458, 458)
2025-05-20 09:59:11,149 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (458, 458)
2025-05-20 09:59:11,149 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w1', 'w3', 'w4']
2025-05-20 09:59:11,149 - visualizer - INFO - 创建WISE三波段RGB图像，输出到: results/batch_single_folder\visualizations\G007.015-00.271_wise_rgb.png
2025-05-20 09:59:11,280 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:59:11,280 - visualizer - INFO - 波段 3.4μm 的尺寸: (901, 901)
2025-05-20 09:59:11,281 - visualizer - INFO - 波段 12μm 的尺寸: (846, 846)
2025-05-20 09:59:11,281 - visualizer - INFO - 波段 22μm 的尺寸: (458, 458)
2025-05-20 09:59:11,281 - visualizer - INFO - Using 12μm band size as target: (846, 846)
2025-05-20 09:59:11,281 - visualizer - INFO - Using target image size: (846, 846), original sizes: {'w1': (901, 901), 'w3': (846, 846), 'w4': (458, 458)}
2025-05-20 09:59:11,373 - visualizer - INFO - 调整w4波段数据从(458, 458)到(846, 846)
2025-05-20 09:59:11,379 - visualizer - INFO - 红色通道(22μm)数据范围: 342.60736083984375-1646.81396484375
2025-05-20 09:59:11,385 - visualizer - INFO - 绿色通道(12μm)数据范围: 772.3701171875-8623.435546875
2025-05-20 09:59:11,523 - visualizer - INFO - 调整w1波段数据从(901, 901)到(846, 846)
2025-05-20 09:59:11,526 - visualizer - INFO - 蓝色通道(3.4μm)数据范围: 1.1428260803222656-4327.92626953125
2025-05-20 09:59:11,555 - visualizer - INFO - 使用Lupton RGB方法，stretch=1584.48, Q=10
2025-05-20 09:59:11,555 - visualizer - INFO - 各波段99.5%百分位数: R=539.87, G=3168.96, B=1391.81
2025-05-20 09:59:11,690 - visualizer - INFO - RGB数据形状: (846, 846, 3), 类型: float32
2025-05-20 09:59:11,695 - visualizer - INFO - Red通道数据范围：0.05882352963089943-0.5490196347236633，均值：0.1285
2025-05-20 09:59:11,698 - visualizer - INFO - Green通道数据范围：0.0-1.0，均值：0.5501
2025-05-20 09:59:11,702 - visualizer - INFO - Blue通道数据范围：0.0-0.8392156958580017，均值：0.0650
2025-05-20 09:59:13,207 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:59:13,213 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:59:13,263 - visualizer - INFO - 按照要求不显示Gaia星
2025-05-20 09:59:13,264 - visualizer - INFO - 设置坐标刻度间隔: RA=100.000度, Dec=100.000度
2025-05-20 09:59:17,420 - visualizer - INFO - 成功保存RGB图像到: results/batch_single_folder\visualizations\G007.015-00.271_wise_rgb.png
2025-05-20 09:59:17,423 - main_single_folder - INFO - 成功保存WISE RGB图像到: results/batch_single_folder\visualizations\G007.015-00.271_wise_rgb.png
2025-05-20 09:59:17,424 - main_single_folder - WARNING - 没有足够的数据绘制消光-距离散点图
2025-05-20 09:59:17,443 - main_single_folder - INFO - 成功保存处理报告到: results/batch_single_folder\reports\G007.015-00.271_report.txt
2025-05-20 09:59:17,443 - main_single_folder - INFO - 处理G007.015-00.271完成
