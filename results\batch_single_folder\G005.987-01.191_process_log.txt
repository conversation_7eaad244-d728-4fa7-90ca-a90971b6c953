处理时间: 2025-05-20 09:45:49
耗时: 95.69秒

标准输出:

未能估计G005.987-01.191的分子云距离


标准错误:
2025-05-20 09:44:17,847 - main_single_folder - INFO - 输出目录: results/batch_single_folder
2025-05-20 09:44:17,847 - main_single_folder - INFO - 加载源G005.987-01.191的信息
2025-05-20 09:44:17,848 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
2025-05-20 09:44:17,853 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-05-20 09:44:17,855 - main_single_folder - INFO - 从源名解析银道坐标: l=5.987000, b=-1.191000
2025-05-20 09:44:17,861 - main_single_folder - INFO - 银道坐标转换为赤道坐标: RA=270.943321, Dec=-24.372945 (ICRS)
2025-05-20 09:44:17,861 - main_single_folder - INFO - 从源表加载信息: RA=270.943321, Dec=-24.372945, R_eff=0.119167度
2025-05-20 09:44:17,862 - main_single_folder - INFO - 步骤1：加载WISE数据
2025-05-20 09:44:17,866 - data_manager - INFO - 为源G005.987-01.191加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G005.987-01.191
2025-05-20 09:44:17,882 - data_manager - INFO - 目录中的所有文件: ['G005.987-01.191_ATLASGAL_870um.fits', 'G005.987-01.191_IRIS_100.fits', 'G005.987-01.191_MIPSGAL_24um.fits', 'G005.987-01.191_NVSS.fits', 'G005.987-01.191_WISE_12.fits', 'G005.987-01.191_WISE_22.fits', 'G005.987-01.191_WISE_3.4.fits', 'G005.987-01.191_WISE_4.6.fits']
2025-05-20 09:44:17,882 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:44:17,882 - data_manager - INFO - 第一次匹配结果: ['G005.987-01.191_WISE_12.fits']
2025-05-20 09:44:17,883 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G005.987-01.191\G005.987-01.191_WISE_12.fits
2025-05-20 09:44:17,956 - data_manager - INFO - FITS数据统计: 最小值=24.77783203125, 最大值=10553.4814453125, 均值=1576.13818359375, 中位数=1399.173583984375
2025-05-20 09:44:17,957 - data_manager - INFO - 有效数据点数量: 434304/435600 (99.70%)
2025-05-20 09:44:17,964 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (660, 660)
2025-05-20 09:44:17,965 - main_single_folder - INFO - WISE数据加载完成
2025-05-20 09:44:17,965 - main_single_folder - INFO - 步骤2：区域映射
2025-05-20 09:44:17,979 - main_single_folder - INFO - 使用W3/W4波段比值方法定义PDR区域
2025-05-20 09:44:17,979 - region_mapper - INFO - 使用W3/W4波段比值方法定义PDR掩模
2025-05-20 09:44:17,979 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:44:17,979 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:44:17,980 - region_mapper.ratio - INFO - 加载源G005.987-01.191的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:44:17,980 - region_mapper.ratio - INFO - 加载源G005.987-01.191的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:44:17,980 - data_manager - INFO - 为源G005.987-01.191加载多波段WISE数据: ('12', '22')
2025-05-20 09:44:17,985 - data_manager - INFO - 为源G005.987-01.191加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G005.987-01.191
2025-05-20 09:44:17,985 - data_manager - INFO - 目录中的所有文件: ['G005.987-01.191_ATLASGAL_870um.fits', 'G005.987-01.191_IRIS_100.fits', 'G005.987-01.191_MIPSGAL_24um.fits', 'G005.987-01.191_NVSS.fits', 'G005.987-01.191_WISE_12.fits', 'G005.987-01.191_WISE_22.fits', 'G005.987-01.191_WISE_3.4.fits', 'G005.987-01.191_WISE_4.6.fits']
2025-05-20 09:44:17,985 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:44:17,985 - data_manager - INFO - 第一次匹配结果: ['G005.987-01.191_WISE_12.fits']
2025-05-20 09:44:17,985 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G005.987-01.191\G005.987-01.191_WISE_12.fits
2025-05-20 09:44:17,995 - data_manager - INFO - FITS数据统计: 最小值=24.77783203125, 最大值=10553.4814453125, 均值=1576.13818359375, 中位数=1399.173583984375
2025-05-20 09:44:17,995 - data_manager - INFO - 有效数据点数量: 434304/435600 (99.70%)
2025-05-20 09:44:18,073 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (660, 660)
2025-05-20 09:44:18,074 - data_manager - INFO - 使用12μm波段的WCS作为参考
2025-05-20 09:44:18,074 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (660, 660)
2025-05-20 09:44:18,079 - data_manager - INFO - 为源G005.987-01.191加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G005.987-01.191
2025-05-20 09:44:18,080 - data_manager - INFO - 目录中的所有文件: ['G005.987-01.191_ATLASGAL_870um.fits', 'G005.987-01.191_IRIS_100.fits', 'G005.987-01.191_MIPSGAL_24um.fits', 'G005.987-01.191_NVSS.fits', 'G005.987-01.191_WISE_12.fits', 'G005.987-01.191_WISE_22.fits', 'G005.987-01.191_WISE_3.4.fits', 'G005.987-01.191_WISE_4.6.fits']
2025-05-20 09:44:18,080 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:44:18,080 - data_manager - INFO - 第一次匹配结果: ['G005.987-01.191_WISE_22.fits']
2025-05-20 09:44:18,080 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G005.987-01.191\G005.987-01.191_WISE_22.fits
2025-05-20 09:44:18,137 - data_manager - INFO - FITS数据统计: 最小值=319.96875, 最大值=2722.285888671875, 均值=383.6278991699219, 中位数=343.46185302734375
2025-05-20 09:44:18,138 - data_manager - INFO - 有效数据点数量: 127181/127449 (99.79%)
2025-05-20 09:44:18,145 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (357, 357)
2025-05-20 09:44:18,145 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (357, 357)
2025-05-20 09:44:18,145 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w3', 'w4']
2025-05-20 09:44:18,151 - region_mapper.ratio - INFO - 调整W4波段数据从(357, 357)到(660, 660)
2025-05-20 09:44:18,151 - region_mapper.ratio - INFO - 调整W4波段数据从(357, 357)到(660, 660)
2025-05-20 09:44:18,232 - region_mapper.ratio - INFO - W3/W4比值范围: 3.17-6.12，均值: 4.09
2025-05-20 09:44:18,232 - region_mapper.ratio - INFO - W3/W4比值范围: 3.17-6.12，均值: 4.09
2025-05-20 09:44:18,240 - region_mapper.ratio - INFO - 像素尺度: 504.49 像素/角秒
2025-05-20 09:44:18,240 - region_mapper.ratio - INFO - 像素尺度: 504.49 像素/角秒
2025-05-20 09:44:18,240 - region_mapper.ratio - INFO - 有效半径: 0.9 像素
2025-05-20 09:44:18,240 - region_mapper.ratio - INFO - 有效半径: 0.9 像素
2025-05-20 09:44:18,240 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:44:18,240 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\sklearn\base.py:1152: ConvergenceWarning: Number of distinct clusters (1) found smaller than n_clusters (2). Possibly due to duplicate points in X.
  return fit_method(estimator, *args, **kwargs)
C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\sklearn\base.py:1152: ConvergenceWarning: Number of distinct clusters (1) found smaller than n_clusters (3). Possibly due to duplicate points in X.
  return fit_method(estimator, *args, **kwargs)
C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\sklearn\base.py:1152: ConvergenceWarning: Number of distinct clusters (1) found smaller than n_clusters (4). Possibly due to duplicate points in X.
  return fit_method(estimator, *args, **kwargs)
2025-05-20 09:44:19,169 - region_mapper.ratio - INFO - 使用最佳聚类数: 3
2025-05-20 09:44:19,169 - region_mapper.ratio - INFO - 使用最佳聚类数: 3
C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\sklearn\base.py:1152: ConvergenceWarning: Number of distinct clusters (1) found smaller than n_clusters (3). Possibly due to duplicate points in X.
  return fit_method(estimator, *args, **kwargs)
2025-05-20 09:44:19,239 - region_mapper.ratio - INFO - 聚类 0: 平均比值=3.17±0.00, 大小=311, 平均距离=6.6, 紧凑性=6.6
2025-05-20 09:44:19,239 - region_mapper.ratio - INFO - 聚类 0: 平均比值=3.17±0.00, 大小=311, 平均距离=6.6, 紧凑性=6.6
C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\core\fromnumeric.py:3504: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\core\_methods.py:129: RuntimeWarning: invalid value encountered in divide
  ret = ret.dtype.type(ret / rcount)
C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\core\_methods.py:206: RuntimeWarning: Degrees of freedom <= 0 for slice
  ret = _var(a, axis=axis, dtype=dtype, out=out, ddof=ddof,
C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\core\_methods.py:163: RuntimeWarning: invalid value encountered in divide
  arrmean = um.true_divide(arrmean, div, out=arrmean,
C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\core\_methods.py:198: RuntimeWarning: invalid value encountered in divide
  ret = ret.dtype.type(ret / rcount)
C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\core\_methods.py:129: RuntimeWarning: invalid value encountered in scalar divide
  ret = ret.dtype.type(ret / rcount)
C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\core\_methods.py:121: RuntimeWarning: invalid value encountered in divide
  ret = um.true_divide(
2025-05-20 09:44:19,242 - region_mapper.ratio - INFO - 聚类 1: 平均比值=nan±nan, 大小=0, 平均距离=nan, 紧凑性=nan
2025-05-20 09:44:19,242 - region_mapper.ratio - INFO - 聚类 1: 平均比值=nan±nan, 大小=0, 平均距离=nan, 紧凑性=nan
2025-05-20 09:44:19,242 - region_mapper.ratio - INFO - 聚类 2: 平均比值=nan±nan, 大小=0, 平均距离=nan, 紧凑性=nan
2025-05-20 09:44:19,242 - region_mapper.ratio - INFO - 聚类 2: 平均比值=nan±nan, 大小=0, 平均距离=nan, 紧凑性=nan
2025-05-20 09:44:19,243 - region_mapper.ratio - INFO - 选择聚类 1 作为PDR区域
2025-05-20 09:44:19,243 - region_mapper.ratio - INFO - 选择聚类 1 作为PDR区域
2025-05-20 09:44:19,293 - region_mapper.ratio - INFO - PDR掩模覆盖0个像素
2025-05-20 09:44:19,293 - region_mapper.ratio - INFO - PDR掩模覆盖0个像素
2025-05-20 09:44:19,293 - region_mapper.ratio - WARNING - PDR区域太小 (0 < 100)，使用备用方法
2025-05-20 09:44:19,293 - region_mapper.ratio - WARNING - PDR区域太小 (0 < 100)，使用备用方法
2025-05-20 09:44:19,377 - region_mapper.ratio - INFO - 备用方法后PDR掩模覆盖0个像素
2025-05-20 09:44:19,377 - region_mapper.ratio - INFO - 备用方法后PDR掩模覆盖0个像素
2025-05-20 09:44:23,925 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G005.987-01.191_w3w4_ratio.png
2025-05-20 09:44:23,925 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G005.987-01.191_w3w4_ratio.png
2025-05-20 09:44:23,927 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖0个像素
2025-05-20 09:44:23,927 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖0个像素
2025-05-20 09:44:23,969 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G005.987-01.191_region_masks.fits
2025-05-20 09:44:23,969 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G005.987-01.191_region_masks.fits
2025-05-20 09:44:23,975 - data_manager - INFO - 为源G005.987-01.191加载Gaia数据，中心坐标: RA=270.943321, Dec=-24.372945, 半径=0.5958度
2025-05-20 09:44:23,978 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances-Data/gaia_data\gaia_G005.987-01.191_36arcmin_5R.csv
H:\Augment\Parallax distances\src\data_manager.py:87: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.
  df = pd.read_csv(gaia_file)
2025-05-20 09:44:27,638 - data_manager - INFO - 成功加载Gaia数据，共247186条记录
2025-05-20 09:44:27,865 - data_manager - INFO - 成功加载Gaia数据，共247184个源在搜索半径内
2025-05-20 09:44:28,490 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G005.987-01.191_gaia_regions.fits
2025-05-20 09:45:31,514 - data_manager - WARNING - 检测到可能导致保存问题的列: ['_2MASS', 'twomass__2MASS', 'region']
2025-05-20 09:45:31,598 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:45:37,178 - data_manager - ERROR - 保存数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U1'), dtype('<U8'), dtype('<U10'), dtype('<U11'), dtype('<U9')}
2025-05-20 09:45:37,178 - main_single_folder - ERROR - 加载Gaia数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U1'), dtype('<U8'), dtype('<U10'), dtype('<U11'), dtype('<U9')}
2025-05-20 09:45:37,178 - main_single_folder - WARNING - 将使用空的Gaia数据继续处理
2025-05-20 09:45:37,314 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:45:37,314 - main_single_folder - INFO - 步骤3：恒星选择
2025-05-20 09:45:37,319 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-05-20 09:45:37,320 - star_selector - WARNING - 没有有效的视差数据，跳过视差信噪比筛选
2025-05-20 09:45:37,320 - star_selector - WARNING - 没有有效的RUWE数据，跳过RUWE筛选
2025-05-20 09:45:37,320 - star_selector - INFO - 质量筛选详细信息:
2025-05-20 09:45:37,320 - star_selector - INFO -   视差信噪比阈值: 5.0
2025-05-20 09:45:37,321 - star_selector - INFO -   RUWE阈值: 1.4
2025-05-20 09:45:37,321 - star_selector - INFO -   总体通过率: 0.0%
2025-05-20 09:45:37,321 - star_selector - INFO - 质量筛选完成，保留0/0个源
2025-05-20 09:45:37,321 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G005.987-01.191_filtered_data.fits
2025-05-20 09:45:37,334 - data_manager - INFO - 成功保存数据
2025-05-20 09:45:37,335 - main_single_folder - INFO - 保存质量筛选后的数据到: results/batch_single_folder\processed\G005.987-01.191_filtered_data.fits
2025-05-20 09:45:37,340 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-05-20 09:45:37,340 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-05-20 09:45:37,340 - star_selector - INFO - 筛选恒星样本，原始样本大小: 0
2025-05-20 09:45:37,340 - star_selector - INFO - 排除YSO后: 0/0个源
2025-05-20 09:45:37,341 - star_selector - INFO - 区域 cavity: 0个源
2025-05-20 09:45:37,341 - star_selector - INFO - 区域 pdr: 0个源
2025-05-20 09:45:37,341 - star_selector - INFO - 区域 external: 0个源
2025-05-20 09:45:37,341 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 0
2025-05-20 09:45:37,342 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G005.987-01.191_star_sample.fits
2025-05-20 09:45:37,355 - data_manager - INFO - 成功保存数据
2025-05-20 09:45:37,355 - main_single_folder - INFO - 恒星选择完成
2025-05-20 09:45:37,355 - main_single_folder - INFO - 步骤4：消光计算
2025-05-20 09:45:37,359 - extinction_estimator - INFO - 为0个恒星计算A_V，参数: {'rv': 3.1}
2025-05-20 09:45:37,360 - extinction_estimator - WARNING - 星表为空，无法计算A_V
2025-05-20 09:45:37,360 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G005.987-01.191_stars_with_av.fits
2025-05-20 09:45:37,371 - data_manager - INFO - 成功保存数据
2025-05-20 09:45:37,372 - main_single_folder - INFO - 消光计算完成
2025-05-20 09:45:37,372 - main_single_folder - INFO - 步骤5：距离分析
2025-05-20 09:45:37,372 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-05-20 09:45:37,372 - distance_analyzer - INFO - 将分析0个区域: 
2025-05-20 09:45:37,372 - distance_analyzer - INFO - 估计分子云距离
2025-05-20 09:45:37,373 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity, pdr, external
2025-05-20 09:45:37,373 - distance_analyzer - WARNING - 没有检测到任何跳变，无法估计距离
2025-05-20 09:45:37,389 - main_single_folder - INFO - 距离分析完成
2025-05-20 09:45:37,389 - main_single_folder - INFO - 步骤6：可视化和报告生成
2025-05-20 09:45:37,394 - data_manager - INFO - 为源G005.987-01.191加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G005.987-01.191
2025-05-20 09:45:37,394 - data_manager - INFO - 目录中的所有文件: ['G005.987-01.191_ATLASGAL_870um.fits', 'G005.987-01.191_IRIS_100.fits', 'G005.987-01.191_MIPSGAL_24um.fits', 'G005.987-01.191_NVSS.fits', 'G005.987-01.191_WISE_12.fits', 'G005.987-01.191_WISE_22.fits', 'G005.987-01.191_WISE_3.4.fits', 'G005.987-01.191_WISE_4.6.fits']
2025-05-20 09:45:37,394 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:45:37,395 - data_manager - INFO - 第一次匹配结果: ['G005.987-01.191_WISE_12.fits']
2025-05-20 09:45:37,395 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G005.987-01.191\G005.987-01.191_WISE_12.fits
2025-05-20 09:45:37,404 - data_manager - INFO - FITS数据统计: 最小值=24.77783203125, 最大值=10553.4814453125, 均值=1576.13818359375, 中位数=1399.173583984375
2025-05-20 09:45:37,405 - data_manager - INFO - 有效数据点数量: 434304/435600 (99.70%)
2025-05-20 09:45:37,412 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (660, 660)
2025-05-20 09:45:37,438 - visualizer - INFO - 绘制WISE图像和区域掩模，输出到: results/batch_single_folder\visualizations\G005.987-01.191_wise_regions_stars.png
2025-05-20 09:45:37,550 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:45:39,604 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:45:39,612 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:45:43,364 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G005.987-01.191_wise_regions_stars.png
2025-05-20 09:45:43,364 - main_single_folder - INFO - 成功保存WISE图像和区域掩模到: results/batch_single_folder\visualizations\G005.987-01.191_wise_regions_stars.png
2025-05-20 09:45:43,364 - main_single_folder - INFO - 加载WISE多波段数据用于RGB图像生成
2025-05-20 09:45:43,364 - data_manager - INFO - 为源G005.987-01.191加载多波段WISE数据: ('3.4', '12', '22')
2025-05-20 09:45:43,370 - data_manager - INFO - 为源G005.987-01.191加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G005.987-01.191
2025-05-20 09:45:43,370 - data_manager - INFO - 目录中的所有文件: ['G005.987-01.191_ATLASGAL_870um.fits', 'G005.987-01.191_IRIS_100.fits', 'G005.987-01.191_MIPSGAL_24um.fits', 'G005.987-01.191_NVSS.fits', 'G005.987-01.191_WISE_12.fits', 'G005.987-01.191_WISE_22.fits', 'G005.987-01.191_WISE_3.4.fits', 'G005.987-01.191_WISE_4.6.fits']
2025-05-20 09:45:43,371 - data_manager - INFO - 查找WISE 3.4μm波段的FITS文件
2025-05-20 09:45:43,371 - data_manager - INFO - 第一次匹配结果: ['G005.987-01.191_WISE_3.4.fits']
2025-05-20 09:45:43,371 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G005.987-01.191\G005.987-01.191_WISE_3.4.fits
2025-05-20 09:45:43,455 - data_manager - INFO - FITS数据统计: 最小值=31.404312133789062, 最大值=3794.464111328125, 均值=197.9075927734375, 中位数=139.8092803955078
2025-05-20 09:45:43,455 - data_manager - INFO - 有效数据点数量: 494208/494209 (100.00%)
2025-05-20 09:45:43,463 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (703, 703)
2025-05-20 09:45:43,464 - data_manager - INFO - 使用3.4μm波段的WCS作为参考
2025-05-20 09:45:43,464 - data_manager - INFO - 成功加载WISE 3.4μm波段数据，尺寸: (703, 703)
2025-05-20 09:45:43,469 - data_manager - INFO - 为源G005.987-01.191加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G005.987-01.191
2025-05-20 09:45:43,469 - data_manager - INFO - 目录中的所有文件: ['G005.987-01.191_ATLASGAL_870um.fits', 'G005.987-01.191_IRIS_100.fits', 'G005.987-01.191_MIPSGAL_24um.fits', 'G005.987-01.191_NVSS.fits', 'G005.987-01.191_WISE_12.fits', 'G005.987-01.191_WISE_22.fits', 'G005.987-01.191_WISE_3.4.fits', 'G005.987-01.191_WISE_4.6.fits']
2025-05-20 09:45:43,469 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:45:43,469 - data_manager - INFO - 第一次匹配结果: ['G005.987-01.191_WISE_12.fits']
2025-05-20 09:45:43,469 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G005.987-01.191\G005.987-01.191_WISE_12.fits
2025-05-20 09:45:43,479 - data_manager - INFO - FITS数据统计: 最小值=24.77783203125, 最大值=10553.4814453125, 均值=1576.13818359375, 中位数=1399.173583984375
2025-05-20 09:45:43,479 - data_manager - INFO - 有效数据点数量: 434304/435600 (99.70%)
2025-05-20 09:45:43,487 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (660, 660)
2025-05-20 09:45:43,487 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (660, 660)
2025-05-20 09:45:43,493 - data_manager - INFO - 为源G005.987-01.191加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G005.987-01.191
2025-05-20 09:45:43,493 - data_manager - INFO - 目录中的所有文件: ['G005.987-01.191_ATLASGAL_870um.fits', 'G005.987-01.191_IRIS_100.fits', 'G005.987-01.191_MIPSGAL_24um.fits', 'G005.987-01.191_NVSS.fits', 'G005.987-01.191_WISE_12.fits', 'G005.987-01.191_WISE_22.fits', 'G005.987-01.191_WISE_3.4.fits', 'G005.987-01.191_WISE_4.6.fits']
2025-05-20 09:45:43,493 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:45:43,493 - data_manager - INFO - 第一次匹配结果: ['G005.987-01.191_WISE_22.fits']
2025-05-20 09:45:43,493 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G005.987-01.191\G005.987-01.191_WISE_22.fits
2025-05-20 09:45:43,498 - data_manager - INFO - FITS数据统计: 最小值=319.96875, 最大值=2722.285888671875, 均值=383.6278991699219, 中位数=343.46185302734375
2025-05-20 09:45:43,498 - data_manager - INFO - 有效数据点数量: 127181/127449 (99.79%)
2025-05-20 09:45:43,505 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (357, 357)
2025-05-20 09:45:43,505 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (357, 357)
2025-05-20 09:45:43,506 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w1', 'w3', 'w4']
2025-05-20 09:45:43,506 - visualizer - INFO - 创建WISE三波段RGB图像，输出到: results/batch_single_folder\visualizations\G005.987-01.191_wise_rgb.png
2025-05-20 09:45:43,635 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:45:43,636 - visualizer - INFO - 波段 3.4μm 的尺寸: (703, 703)
2025-05-20 09:45:43,636 - visualizer - INFO - 波段 12μm 的尺寸: (660, 660)
2025-05-20 09:45:43,636 - visualizer - INFO - 波段 22μm 的尺寸: (357, 357)
2025-05-20 09:45:43,636 - visualizer - INFO - Using 12μm band size as target: (660, 660)
2025-05-20 09:45:43,636 - visualizer - INFO - Using target image size: (660, 660), original sizes: {'w1': (703, 703), 'w3': (660, 660), 'w4': (357, 357)}
2025-05-20 09:45:43,691 - visualizer - INFO - 调整w4波段数据从(357, 357)到(660, 660)
2025-05-20 09:45:43,693 - visualizer - INFO - 红色通道(22μm)数据范围: 0.004051901865750551-2722.285888671875
2025-05-20 09:45:43,698 - visualizer - INFO - 绿色通道(12μm)数据范围: 24.77783203125-10553.4814453125
2025-05-20 09:45:43,781 - visualizer - INFO - 调整w1波段数据从(703, 703)到(660, 660)
2025-05-20 09:45:43,783 - visualizer - INFO - 蓝色通道(3.4μm)数据范围: 12.602906227111816-3794.464111328125
2025-05-20 09:45:43,801 - visualizer - INFO - 使用Lupton RGB方法，stretch=2889.92, Q=10
2025-05-20 09:45:43,801 - visualizer - INFO - 各波段99.5%百分位数: R=1515.94, G=5779.84, B=1308.90
2025-05-20 09:45:43,882 - visualizer - INFO - RGB数据形状: (660, 660, 3), 类型: float32
2025-05-20 09:45:43,885 - visualizer - INFO - Red通道数据范围：0.0-0.6039215922355652，均值：0.0961
2025-05-20 09:45:43,887 - visualizer - INFO - Green通道数据范围：0.0-1.0，均值：0.3955
2025-05-20 09:45:43,889 - visualizer - INFO - Blue通道数据范围：0.0-0.7411764860153198，均值：0.0480
2025-05-20 09:45:45,360 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:45:45,366 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:45:45,428 - visualizer - INFO - 按照要求不显示Gaia星
2025-05-20 09:45:45,430 - visualizer - INFO - 设置坐标刻度间隔: RA=10.000度, Dec=10.000度
2025-05-20 09:45:49,443 - visualizer - INFO - 成功保存RGB图像到: results/batch_single_folder\visualizations\G005.987-01.191_wise_rgb.png
2025-05-20 09:45:49,445 - main_single_folder - INFO - 成功保存WISE RGB图像到: results/batch_single_folder\visualizations\G005.987-01.191_wise_rgb.png
2025-05-20 09:45:49,446 - main_single_folder - WARNING - 没有足够的数据绘制消光-距离散点图
2025-05-20 09:45:49,463 - main_single_folder - INFO - 成功保存处理报告到: results/batch_single_folder\reports\G005.987-01.191_report.txt
2025-05-20 09:45:49,463 - main_single_folder - INFO - 处理G005.987-01.191完成
