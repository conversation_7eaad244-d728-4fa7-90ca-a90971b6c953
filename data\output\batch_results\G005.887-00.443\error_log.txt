处理时间: 2025-04-24 05:00:34
耗时: 12.21秒

返回码: 1

标准输出:
错误: zero-size array to reduction operation minimum which has no identity


标准错误:
2025-04-24 05:00:25,359 - main - INFO - 输出目录: data/output/batch_results\G005.887-00.443
2025-04-24 05:00:25,359 - main - INFO - 加载源G005.887-00.443的信息
2025-04-24 05:00:25,359 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
H:\Augment\Parallax distances\src\data_manager.py:43: FutureWarning: The 'delim_whitespace' keyword in pd.read_csv is deprecated and will be removed in a future version. Use ``sep='\s+'`` instead
  df = pd.read_csv(catalog_path, delim_whitespace=True, comment='#', header=None, names=column_names)
2025-04-24 05:00:25,365 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-04-24 05:00:25,367 - main - INFO - 目录中的所有文件: ['G005.887-00.443_ATLASGAL_870um.fits', 'G005.887-00.443_IRIS_100.fits', 'G005.887-00.443_MIPSGAL_24um.fits', 'G005.887-00.443_NVSS.fits', 'G005.887-00.443_WISE_12.fits', 'G005.887-00.443_WISE_22.fits', 'G005.887-00.443_WISE_3.4.fits', 'G005.887-00.443_WISE_4.6.fits']
2025-04-24 05:00:25,367 - main - INFO - 第一次匹配结果: ['G005.887-00.443_WISE_12.fits']
2025-04-24 05:00:25,367 - main - INFO - 从FITS文件获取坐标: U:/Data/Bubbles/Wise bubbles\G005.887-00.443\G005.887-00.443_WISE_12.fits
2025-04-24 05:00:25,369 - main - INFO - 从FITS头信息获取坐标系统: fk5
2025-04-24 05:00:25,372 - main - INFO - 从FITS头信息获取坐标: RA=270.175993, Dec=-24.089806 (ICRS)
2025-04-24 05:00:25,373 - main - INFO - 使用有效半径436.0角秒 (0.121111度)
2025-04-24 05:00:25,373 - main - INFO - 从源表加载信息: RA=270.175993, Dec=-24.089806, R_eff=0.121111度
2025-04-24 05:00:25,374 - main - INFO - 步骤1：加载WISE数据
2025-04-24 05:00:25,377 - data_manager - INFO - 为源G005.887-00.443加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G005.887-00.443
2025-04-24 05:00:25,378 - data_manager - INFO - 目录中的所有文件: ['G005.887-00.443_ATLASGAL_870um.fits', 'G005.887-00.443_IRIS_100.fits', 'G005.887-00.443_MIPSGAL_24um.fits', 'G005.887-00.443_NVSS.fits', 'G005.887-00.443_WISE_12.fits', 'G005.887-00.443_WISE_22.fits', 'G005.887-00.443_WISE_3.4.fits', 'G005.887-00.443_WISE_4.6.fits']
2025-04-24 05:00:25,378 - data_manager - INFO - 第一次匹配结果: ['G005.887-00.443_WISE_12.fits']
2025-04-24 05:00:25,378 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G005.887-00.443\G005.887-00.443_WISE_12.fits
2025-04-24 05:00:25,387 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (670, 670)
2025-04-24 05:00:25,387 - main - INFO - WISE数据加载完成
2025-04-24 05:00:25,387 - main - INFO - 步骤2：区域映射
2025-04-24 05:00:25,387 - region_mapper - INFO - 处理WISE图像，平滑sigma=1.0
2025-04-24 05:00:25,389 - region_mapper - INFO - 替换图像中的NaN值
2025-04-24 05:00:25,393 - region_mapper - INFO - 应用高斯平滑
2025-04-24 05:00:25,404 - region_mapper - INFO - 估计并减除背景
2025-04-24 05:00:25,423 - region_mapper - INFO - WISE图像处理完成
2025-04-24 05:00:25,423 - region_mapper - INFO - 定义PDR掩模，阈值因子=0.5，最大半径因子=3.0
2025-04-24 05:00:25,423 - region_mapper - INFO - WCS坐标系统: fk5
2025-04-24 05:00:25,423 - region_mapper - INFO - 将中心坐标从ICRS转换为fk5
2025-04-24 05:00:25,429 - region_mapper - INFO - 中心坐标 (RA=270.175993, Dec=-24.089806) 对应像素坐标 (X=334.5, Y=334.5)
2025-04-24 05:00:25,433 - region_mapper - INFO - 像素尺度: 505.03 像素/角秒
2025-04-24 05:00:25,433 - region_mapper - INFO - 最大搜索半径: 2.6 像素
2025-04-24 05:00:25,438 - region_mapper - INFO - PDR阈值: 2590.97 (峰值的50%)
2025-04-24 05:00:25,504 - region_mapper - INFO - PDR掩模创建完成，覆盖17个像素
2025-04-24 05:00:25,506 - region_mapper - INFO - 定义空腔掩模，有效半径=0.12111111111111111度
2025-04-24 05:00:25,513 - region_mapper - INFO - 有效半径: 0.9 像素
2025-04-24 05:00:25,518 - region_mapper - INFO - 空腔掩模创建完成，覆盖0个像素
2025-04-24 05:00:25,519 - region_mapper - INFO - 定义外部区域掩模，最大半径因子=5.0
2025-04-24 05:00:25,526 - region_mapper - INFO - 最大半径: 4.3 像素
2025-04-24 05:00:25,531 - region_mapper - INFO - 外部区域掩模创建完成，覆盖43个像素
2025-04-24 05:00:25,531 - region_mapper - INFO - 保存区域掩模到: data/output/batch_results\G005.887-00.443\processed\G005.887-00.443_region_masks.npz
2025-04-24 05:00:25,543 - region_mapper - INFO - 成功保存区域掩模
2025-04-24 05:00:25,547 - data_manager - INFO - 为源G005.887-00.443加载Gaia数据，中心坐标: RA=270.175993, Dec=-24.089806, 半径=0.6056度
2025-04-24 05:00:25,550 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances/gaia_data\gaia_G005.887-00.443_36arcmin_5R.csv
H:\Augment\Parallax distances\src\data_manager.py:87: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.
  df = pd.read_csv(gaia_file)
2025-04-24 05:00:28,315 - data_manager - INFO - 成功加载Gaia数据，共182136条记录
2025-04-24 05:00:28,498 - data_manager - INFO - 成功加载Gaia数据，共181960个源在搜索半径内
2025-04-24 05:00:28,552 - region_mapper - INFO - 为181960个恒星分配区域标签
2025-04-24 05:00:28,553 - region_mapper - INFO - WCS坐标系统: fk5
2025-04-24 05:00:28,553 - region_mapper - INFO - 将恒星坐标从ICRS转换为fk5
2025-04-24 05:00:29,643 - region_mapper - INFO - 区域标签分配完成: 空腔=0, PDR=4, 外部=14, 区域外=181942
2025-04-24 05:00:29,646 - data_manager - INFO - 保存处理后的数据到: data/output/batch_results\G005.887-00.443\processed\G005.887-00.443_gaia_regions.fits
2025-04-24 05:00:33,526 - data_manager - ERROR - 保存数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U1'), dtype('<U10'), dtype('<U11'), dtype('<U9')}
2025-04-24 05:00:33,526 - main - ERROR - 加载Gaia数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U1'), dtype('<U10'), dtype('<U11'), dtype('<U9')}
2025-04-24 05:00:33,526 - main - WARNING - 将使用空的Gaia数据继续处理
2025-04-24 05:00:33,530 - main - WARNING - 区域映射完成，但没有Gaia数据
2025-04-24 05:00:33,633 - main - INFO - 步骤3：恒星选择
2025-04-24 05:00:33,637 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-04-24 05:00:33,637 - star_selector - WARNING - 没有有效的视差数据，跳过视差信噪比筛选
2025-04-24 05:00:33,638 - star_selector - WARNING - 没有有效的RUWE数据，跳过RUWE筛选
2025-04-24 05:00:33,638 - star_selector - INFO - 质量筛选完成，保留0/0个源
2025-04-24 05:00:33,642 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-04-24 05:00:33,642 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-04-24 05:00:33,642 - star_selector - INFO - 筛选恒星样本，原始样本大小: 0
2025-04-24 05:00:33,642 - star_selector - INFO - 排除YSO后: 0/0个源
2025-04-24 05:00:33,643 - star_selector - INFO - 区域 cavity: 0个源
2025-04-24 05:00:33,643 - star_selector - INFO - 区域 pdr: 0个源
2025-04-24 05:00:33,643 - star_selector - INFO - 区域 external: 0个源
2025-04-24 05:00:33,643 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 0
2025-04-24 05:00:33,644 - data_manager - INFO - 保存处理后的数据到: data/output/batch_results\G005.887-00.443\processed\G005.887-00.443_star_sample.fits
2025-04-24 05:00:33,666 - data_manager - INFO - 成功保存数据
2025-04-24 05:00:33,666 - main - INFO - 恒星选择完成
2025-04-24 05:00:33,666 - main - INFO - 步骤4：消光计算
2025-04-24 05:00:33,670 - extinction_estimator - INFO - 为0个恒星计算A_V，参数: {'rv': 3.1}
2025-04-24 05:00:33,672 - extinction_estimator - ERROR - 计算A_V失败: zero-size array to reduction operation minimum which has no identity
2025-04-24 05:00:33,672 - main - ERROR - 处理失败: zero-size array to reduction operation minimum which has no identity
Traceback (most recent call last):
  File "H:\Augment\Parallax distances\main.py", line 459, in main
    results = process_hii_region(args)
              ^^^^^^^^^^^^^^^^^^^^^^^^
  File "H:\Augment\Parallax distances\main.py", line 388, in process_hii_region
    stars_with_av = compute_av_per_star(star_sample)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "H:\Augment\Parallax distances\src\extinction_estimator.py", line 294, in compute_av_per_star
    logger.info(f"A_V计算完成，范围: {np.min(av_values):.2f}-{np.max(av_values):.2f}")
                                      ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\_core\fromnumeric.py", line 3343, in min
    return _wrapreduction(a, np.minimum, 'min', axis, None, out,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\_core\fromnumeric.py", line 86, in _wrapreduction
    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: zero-size array to reduction operation minimum which has no identity
