# HII区域批量处理说明

本文档介绍如何使用批量处理脚本来处理多个HII区域源，并将所有输出保存在同一个文件夹中。

## 新增功能

1. **单一文件夹输出**：所有源的输出（图像和报告）保存在同一个文件夹中，文件名中包含源名称以确保唯一性
2. **英文图像标注**：所有图像标注使用英文以避免乱码问题
3. **批量处理前20个源**：默认处理源表中的前20个源

## 文件说明

- `main_single_folder.py`：修改版的主程序，将所有输出保存在同一个文件夹中
- `batch_process_single_folder.py`：批量处理脚本，调用`main_single_folder.py`处理多个源

## 使用方法

### 批量处理

运行以下命令处理前20个源：

```bash
python batch_process_single_folder.py
```

默认输出目录为`results/batch_single_folder`。

### 自定义参数

可以通过命令行参数自定义处理：

```bash
python batch_process_single_folder.py --num-sources 10 --output-dir custom_output --config custom_config.yaml
```

参数说明：
- `--num-sources`：要处理的源数量（默认：20）
- `--output-dir`：输出目录（默认：results/batch_single_folder）
- `--config`：配置文件路径（默认：config/default_config.yaml）
- `--catalog`：HII区源表路径（默认：H:/Augment/Parallax distances/Parallax-based distances.dat）

### 处理单个源

如果只需处理单个源，可以直接使用`main_single_folder.py`：

```bash
python main_single_folder.py --source G000.120-00.556 --output-dir results/single_source
```

## 输出目录结构

批量处理后，输出目录结构如下：

```
results/batch_single_folder/
├── processed/                  # 处理后的数据文件
│   ├── G000.120-00.556_filtered_data.fits
│   ├── G000.120-00.556_gaia_regions.fits
│   ├── G000.120-00.556_region_masks.fits
│   ├── G000.120-00.556_star_sample.fits
│   ├── G000.120-00.556_stars_with_av.fits
│   └── ...
├── reports/                    # 报告文件
│   ├── G000.120-00.556_report.txt
│   ├── G000.120-00.556_results.txt
│   └── ...
├── visualizations/             # 可视化图像
│   ├── G000.120-00.556_extinction_distance.png
│   ├── G000.120-00.556_wise_regions_stars.png
│   ├── G000.120-00.556_wise_rgb.png
│   └── ...
├── G000.120-00.556_process_log.txt  # 处理日志
├── batch_summary.txt                # 批处理汇总
└── ...
```

## 注意事项

1. 所有图像标注使用英文以避免乱码问题
2. 所有输出文件名中包含源名称以确保唯一性
3. 处理报告使用英文以确保兼容性
4. 批处理完成后会生成汇总报告`batch_summary.txt`
