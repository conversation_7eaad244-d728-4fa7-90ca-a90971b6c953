处理时间: 2025-04-24 05:04:45
耗时: 6.67秒

标准输出:

未能估计G000.121-00.304的分子云距离


标准错误:
2025-04-24 05:04:42,712 - main - INFO - 输出目录: data/output/batch_results\G000.121-00.304
2025-04-24 05:04:42,713 - main - INFO - 加载源G000.121-00.304的信息
2025-04-24 05:04:42,713 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
H:\Augment\Parallax distances\src\data_manager.py:43: FutureWarning: The 'delim_whitespace' keyword in pd.read_csv is deprecated and will be removed in a future version. Use ``sep='\s+'`` instead
  df = pd.read_csv(catalog_path, delim_whitespace=True, comment='#', header=None, names=column_names)
2025-04-24 05:04:42,719 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-04-24 05:04:42,720 - main - INFO - 目录中的所有文件: ['G000.121-00.304_ATLASGAL_870um.fits', 'G000.121-00.304_IRIS_100.fits', 'G000.121-00.304_NVSS.fits', 'G000.121-00.304_WISE_12.fits', 'G000.121-00.304_WISE_22.fits', 'G000.121-00.304_WISE_3.4.fits', 'G000.121-00.304_WISE_4.6.fits']
2025-04-24 05:04:42,720 - main - INFO - 第一次匹配结果: ['G000.121-00.304_WISE_12.fits']
2025-04-24 05:04:42,720 - main - INFO - 从FITS文件获取坐标: U:/Data/Bubbles/Wise bubbles\G000.121-00.304\G000.121-00.304_WISE_12.fits
2025-04-24 05:04:42,723 - main - INFO - 从FITS头信息获取坐标系统: fk5
2025-04-24 05:04:42,726 - main - INFO - 从FITS头信息获取坐标: RA=266.773992, Dec=-28.989905 (ICRS)
2025-04-24 05:04:42,726 - main - INFO - 使用有效半径37.0角秒 (0.010278度)
2025-04-24 05:04:42,727 - main - INFO - 从源表加载信息: RA=266.773992, Dec=-28.989905, R_eff=0.010278度
2025-04-24 05:04:42,727 - main - INFO - 步骤1：加载WISE数据
2025-04-24 05:04:42,731 - data_manager - INFO - 为源G000.121-00.304加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.121-00.304
2025-04-24 05:04:42,732 - data_manager - INFO - 目录中的所有文件: ['G000.121-00.304_ATLASGAL_870um.fits', 'G000.121-00.304_IRIS_100.fits', 'G000.121-00.304_NVSS.fits', 'G000.121-00.304_WISE_12.fits', 'G000.121-00.304_WISE_22.fits', 'G000.121-00.304_WISE_3.4.fits', 'G000.121-00.304_WISE_4.6.fits']
2025-04-24 05:04:42,732 - data_manager - INFO - 第一次匹配结果: ['G000.121-00.304_WISE_12.fits']
2025-04-24 05:04:42,732 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.121-00.304\G000.121-00.304_WISE_12.fits
2025-04-24 05:04:42,741 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-04-24 05:04:42,741 - main - INFO - WISE数据加载完成
2025-04-24 05:04:42,742 - main - INFO - 步骤2：区域映射
2025-04-24 05:04:42,742 - region_mapper - INFO - 处理WISE图像，平滑sigma=1.0
2025-04-24 05:04:42,742 - region_mapper - INFO - 替换图像中的NaN值
2025-04-24 05:04:42,743 - region_mapper - INFO - 应用高斯平滑
2025-04-24 05:04:42,744 - region_mapper - INFO - 估计并减除背景
2025-04-24 05:04:42,756 - region_mapper - INFO - WISE图像处理完成
2025-04-24 05:04:42,756 - region_mapper - INFO - 定义PDR掩模，阈值因子=0.5，最大半径因子=3.0
2025-04-24 05:04:42,756 - region_mapper - INFO - WCS坐标系统: fk5
2025-04-24 05:04:42,756 - region_mapper - INFO - 将中心坐标从ICRS转换为fk5
2025-04-24 05:04:42,762 - region_mapper - INFO - 中心坐标 (RA=266.773992, Dec=-28.989905) 对应像素坐标 (X=91.5, Y=91.5)
2025-04-24 05:04:42,765 - region_mapper - INFO - 像素尺度: 482.84 像素/角秒
2025-04-24 05:04:42,766 - region_mapper - INFO - 最大搜索半径: 0.2 像素
2025-04-24 05:04:42,766 - region_mapper - INFO - PDR阈值: 0.00 (峰值的50%)
2025-04-24 05:04:42,783 - region_mapper - INFO - PDR掩模创建完成，覆盖33856个像素
2025-04-24 05:04:42,783 - region_mapper - INFO - 定义空腔掩模，有效半径=0.010277777777777778度
2025-04-24 05:04:42,790 - region_mapper - INFO - 有效半径: 0.1 像素
2025-04-24 05:04:42,791 - region_mapper - INFO - 空腔掩模创建完成，覆盖0个像素
2025-04-24 05:04:42,791 - region_mapper - INFO - 定义外部区域掩模，最大半径因子=5.0
2025-04-24 05:04:42,798 - region_mapper - INFO - 最大半径: 0.4 像素
2025-04-24 05:04:42,799 - region_mapper - INFO - 外部区域掩模创建完成，覆盖0个像素
2025-04-24 05:04:42,799 - region_mapper - INFO - 保存区域掩模到: data/output/batch_results\G000.121-00.304\processed\G000.121-00.304_region_masks.npz
2025-04-24 05:04:42,801 - region_mapper - INFO - 成功保存区域掩模
2025-04-24 05:04:42,804 - data_manager - INFO - 为源G000.121-00.304加载Gaia数据，中心坐标: RA=266.773992, Dec=-28.989905, 半径=0.0514度
2025-04-24 05:04:42,807 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances/gaia_data\gaia_G000.121-00.304_10arcmin_min10.csv
2025-04-24 05:04:43,060 - data_manager - INFO - 成功加载Gaia数据，共12812条记录
2025-04-24 05:04:43,075 - data_manager - INFO - 成功加载Gaia数据，共1367个源在搜索半径内
2025-04-24 05:04:43,080 - region_mapper - INFO - 为1367个恒星分配区域标签
2025-04-24 05:04:43,081 - region_mapper - INFO - WCS坐标系统: fk5
2025-04-24 05:04:43,081 - region_mapper - INFO - 将恒星坐标从ICRS转换为fk5
2025-04-24 05:04:43,093 - region_mapper - INFO - 区域标签分配完成: 空腔=0, PDR=1367, 外部=0, 区域外=0
2025-04-24 05:04:43,093 - data_manager - INFO - 保存处理后的数据到: data/output/batch_results\G000.121-00.304\processed\G000.121-00.304_gaia_regions.fits
2025-04-24 05:04:43,572 - data_manager - INFO - 成功保存数据
2025-04-24 05:04:43,573 - main - INFO - 区域映射完成
2025-04-24 05:04:43,573 - main - INFO - 步骤3：恒星选择
2025-04-24 05:04:43,577 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-04-24 05:04:43,578 - star_selector - INFO - 视差信噪比筛选: 保留227/1367个源
2025-04-24 05:04:43,579 - star_selector - INFO - RUWE筛选: 保留1327/1367个源
2025-04-24 05:04:43,587 - star_selector - INFO - 质量筛选完成，保留217/1367个源
2025-04-24 05:04:43,590 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-04-24 05:04:43,591 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-04-24 05:04:43,591 - star_selector - INFO - 筛选恒星样本，原始样本大小: 217
2025-04-24 05:04:43,591 - star_selector - INFO - 排除YSO后: 217/217个源
2025-04-24 05:04:43,599 - star_selector - INFO - 区域 cavity: 0个源
2025-04-24 05:04:43,599 - star_selector - INFO - 区域 pdr: 217个源
2025-04-24 05:04:43,600 - star_selector - INFO - 区域 external: 0个源
2025-04-24 05:04:43,600 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 217
2025-04-24 05:04:43,600 - data_manager - INFO - 保存处理后的数据到: data/output/batch_results\G000.121-00.304\processed\G000.121-00.304_star_sample.fits
2025-04-24 05:04:43,735 - data_manager - INFO - 成功保存数据
2025-04-24 05:04:43,736 - main - INFO - 恒星选择完成
2025-04-24 05:04:43,736 - main - INFO - 步骤4：消光计算
2025-04-24 05:04:43,740 - extinction_estimator - INFO - 为217个恒星计算A_V，参数: {'rv': 3.1}
2025-04-24 05:04:43,757 - extinction_estimator - INFO - 使用2MASS H-K颜色计算A_V
2025-04-24 05:04:43,757 - extinction_estimator - INFO - 估计本征H-K颜色，方法: select_giants
2025-04-24 05:04:43,757 - extinction_estimator - INFO - 使用红巨星的典型H-K颜色: 0.15
2025-04-24 05:04:43,758 - extinction_estimator - INFO - 基于H-K颜色过量计算A_V，R_V=3.1
2025-04-24 05:04:43,759 - extinction_estimator - INFO - 计算得到194个A_V值，范围: 0.00-36.33
2025-04-24 05:04:43,759 - extinction_estimator - INFO - 使用Gaia BP-RP颜色计算A_V
2025-04-24 05:04:43,760 - extinction_estimator - INFO - 估计本征BP-RP颜色，方法: fixed_value
2025-04-24 05:04:43,760 - extinction_estimator - INFO - 使用固定的BP-RP颜色: 0.8
2025-04-24 05:04:43,760 - extinction_estimator - INFO - 基于Gaia BP-RP颜色过量计算A_V，R_V=3.1
2025-04-24 05:04:43,761 - extinction_estimator - INFO - 计算得到214个A_V值，范围: 0.04-6.53
2025-04-24 05:04:43,762 - extinction_estimator - INFO - 方法 2MASS_HK: 194个源
2025-04-24 05:04:43,762 - extinction_estimator - INFO - 方法 Gaia_BPRP: 21个源
2025-04-24 05:04:43,762 - extinction_estimator - INFO - 方法 unknown: 2个源
2025-04-24 05:04:43,762 - extinction_estimator - INFO - A_V计算完成，范围: 0.00-36.33
2025-04-24 05:04:43,763 - data_manager - INFO - 保存处理后的数据到: data/output/batch_results\G000.121-00.304\processed\G000.121-00.304_stars_with_av.fits
2025-04-24 05:04:43,807 - data_manager - WARNING - 检测到可能导致保存问题的列: ['av_method']
2025-04-24 05:04:43,824 - data_manager - INFO - 已移除有问题的列，继续保存
2025-04-24 05:04:43,915 - data_manager - INFO - 成功保存数据
2025-04-24 05:04:43,915 - main - INFO - 消光计算完成
2025-04-24 05:04:43,915 - main - INFO - 步骤5：距离分析
2025-04-24 05:04:43,915 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-04-24 05:04:43,916 - distance_analyzer - INFO - 将分析1个区域: pdr
2025-04-24 05:04:43,920 - distance_analyzer - INFO - 分析pdr区域的消光-距离关系
2025-04-24 05:04:43,920 - distance_analyzer - INFO - 准备距离-消光数据，区域: pdr
2025-04-24 05:04:43,920 - distance_analyzer - INFO - 区域pdr中有217个有效源
2025-04-24 05:04:43,920 - distance_analyzer - INFO - 计算消光的滑动统计量，bin大小=50pc，最小恒星数=5
2025-04-24 05:04:43,926 - distance_analyzer - INFO - 计算了61个bin的统计量，其中23个有效
2025-04-24 05:04:43,926 - distance_analyzer - INFO - 检测消光跳变，最小跳变高度=0.5mag，最小显著性=2.0
2025-04-24 05:04:43,927 - distance_analyzer - INFO - 共检测到0个消光跳变
2025-04-24 05:04:45,228 - distance_analyzer - INFO - 保存消光-距离图到: data/output/batch_results\G000.121-00.304\extinction_distance_pdr.png
2025-04-24 05:04:45,228 - distance_analyzer - INFO - pdr区域分析完成，检测到0个跳变
2025-04-24 05:04:45,228 - distance_analyzer - INFO - 估计分子云距离
2025-04-24 05:04:45,229 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity, external
2025-04-24 05:04:45,229 - distance_analyzer - WARNING - 没有检测到任何跳变，无法估计距离
2025-04-24 05:04:45,229 - distance_analyzer - INFO - 保存分析结果到: data/output/batch_results\G000.121-00.304\G000.121-00.304_results.txt
2025-04-24 05:04:45,229 - distance_analyzer - INFO - 成功保存分析结果
2025-04-24 05:04:45,230 - main - INFO - 距离分析完成
2025-04-24 05:04:45,230 - main - INFO - 处理G000.121-00.304完成
