"""
区域映射IO模块

提供区域映射的输入输出功能。
"""

import os
import numpy as np
from astropy.io import fits
from astropy.wcs import WCS

from src.utils.logger import setup_logger
from src.utils.helpers import ensure_directory

# 设置日志记录器
logger = setup_logger(name='region_mapper.io')

def save_region_masks(masks, source_name, output_dir):
    """
    保存区域掩模

    Args:
        masks: 区域掩模字典，包含'cavity'、'pdr'和'external'键
        source_name: 源名称
        output_dir: 输出目录

    Returns:
        str: 保存的文件路径
    """
    try:
        # 确保输出目录存在
        ensure_directory(output_dir)
        
        # 设置文件路径
        file_path = os.path.join(output_dir, f"{source_name}_region_masks.fits")
        
        # 创建主HDU
        primary_hdu = fits.PrimaryHDU()
        
        # 创建HDU列表
        hdul = fits.HDUList([primary_hdu])
        
        # 为每个掩模创建HDU
        for name, mask in masks.items():
            # 将布尔掩模转换为整数
            mask_int = mask.astype(np.int16)
            
            # 创建HDU
            hdu = fits.ImageHDU(data=mask_int, name=name.upper())
            
            # 添加到HDU列表
            hdul.append(hdu)
        
        # 保存文件
        hdul.writeto(file_path, overwrite=True)
        
        logger.info(f"成功保存区域掩模到: {file_path}")
        
        return file_path
        
    except Exception as e:
        logger.error(f"保存区域掩模失败: {str(e)}")
        
        # 尝试使用numpy保存
        try:
            # 设置文件路径
            npz_path = os.path.join(output_dir, f"{source_name}_region_masks.npz")
            
            # 保存为npz文件
            np.savez(npz_path, 
                    cavity=masks.get('cavity', np.zeros((10, 10), dtype=bool)),
                    pdr=masks.get('pdr', np.zeros((10, 10), dtype=bool)),
                    external=masks.get('external', np.zeros((10, 10), dtype=bool)))
            
            logger.info(f"保存区域掩模到: {npz_path}")
            
            return npz_path
            
        except Exception as e2:
            logger.error(f"使用numpy保存区域掩模也失败: {str(e2)}")
            return None

def load_region_masks(source_name, input_dir):
    """
    加载区域掩模

    Args:
        source_name: 源名称
        input_dir: 输入目录

    Returns:
        dict: 区域掩模字典，包含'cavity'、'pdr'和'external'键
    """
    try:
        # 设置文件路径
        fits_path = os.path.join(input_dir, f"{source_name}_region_masks.fits")
        npz_path = os.path.join(input_dir, f"{source_name}_region_masks.npz")
        
        # 首先尝试加载FITS文件
        if os.path.exists(fits_path):
            with fits.open(fits_path) as hdul:
                masks = {
                    'cavity': hdul['CAVITY'].data.astype(bool),
                    'pdr': hdul['PDR'].data.astype(bool),
                    'external': hdul['EXTERNAL'].data.astype(bool)
                }
                
                logger.info(f"成功从FITS文件加载区域掩模: {fits_path}")
                return masks
                
        # 如果FITS文件不存在，尝试加载NPZ文件
        elif os.path.exists(npz_path):
            data = np.load(npz_path)
            masks = {
                'cavity': data['cavity'].astype(bool),
                'pdr': data['pdr'].astype(bool),
                'external': data['external'].astype(bool)
            }
            
            logger.info(f"成功从NPZ文件加载区域掩模: {npz_path}")
            return masks
            
        else:
            logger.error(f"找不到区域掩模文件: {fits_path} 或 {npz_path}")
            return None
            
    except Exception as e:
        logger.error(f"加载区域掩模失败: {str(e)}")
        return None
