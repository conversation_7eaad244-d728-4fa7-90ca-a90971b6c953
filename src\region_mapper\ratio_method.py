"""
波段比值法PDR检测模块

使用WISE W3(12μm)/W4(22μm)波段比值检测PDR区域。
"""

import os
import numpy as np
from scipy import ndimage
from scipy import ndimage as ndi
from skimage import measure, morphology
from skimage.transform import resize
from astropy.coordinates import SkyCoord
import astropy.units as u
import matplotlib.pyplot as plt

from src.utils.logger import setup_logger
from src.utils.helpers import ensure_directory, load_config
from src.data_manager import load_wise_fits, load_wise_multiband

# 设置日志记录器
logger = setup_logger(name='region_mapper.ratio')

def define_pdr_mask_ratio(source_name, wcs, center_coord, r_eff,
                       search_radius_factor=3.0, smooth_sigma=1.0,
                       wise_base_path=None, config_path='config/default_config.yaml',
                       output_dir=None, save_ratio_plot=False):
    """
    使用WISE W3(12μm)/W4(22μm)波段比值定义PDR区域掩模

    PDR区域通常在中红外波段具有特定的光谱特征，W3/W4比值可以帮助识别这些区域。
    该方法加载W3和W4波段数据，计算比值，并使用聚类方法自动确定PDR区域。
    同时考虑W3强度、W4强度和W3/W4比值作为聚类特征。

    Args:
        source_name: 源名称
        wcs: 世界坐标系
        center_coord: 中心坐标 (SkyCoord对象)
        r_eff: 有效半径（度）
        search_radius_factor: PDR搜索半径因子（相对于r_eff），默认为3.0
        smooth_sigma: 平滑参数，默认为1.0
        wise_base_path: WISE数据基础路径，如果为None则从配置文件读取
        config_path: 配置文件路径
        output_dir: 输出目录，用于保存比值图像
        save_ratio_plot: 是否保存比值图像，默认为False

    Returns:
        tuple: (PDR掩模（布尔数组）, W3数据, W4数据)
    """
    logger.info(f"使用W3/W4波段比值方法定义PDR掩模，搜索半径因子={search_radius_factor}")

    try:
        # 加载W3和W4波段数据
        logger.info(f"加载源{source_name}的W3(12μm)和W4(22μm)波段数据")
        wise_data, wise_wcs = load_wise_multiband(
            source_name, wise_base_path, config_path, bands=('12', '22'))

        # 检查是否成功加载了两个波段
        if 'w3' not in wise_data or 'w4' not in wise_data or wise_data['w3'] is None or wise_data['w4'] is None:
            logger.error("未能加载W3或W4波段数据，无法计算比值")
            # 返回空掩模
            if 'w3' in wise_data and wise_data['w3'] is not None:
                return np.zeros_like(wise_data['w3'], dtype=bool)
            elif 'w4' in wise_data and wise_data['w4'] is not None:
                return np.zeros_like(wise_data['w4'], dtype=bool)
            else:
                # 如果两个波段都没有，尝试加载W3波段作为参考
                w3_image, _, _ = load_wise_fits(source_name, wise_base_path, config_path, band='12')
                return np.zeros_like(w3_image, dtype=bool)

        # 获取W3和W4波段数据
        w3_data = wise_data['w3'].copy()
        w4_data = wise_data['w4'].copy()

        # 处理NaN值
        w3_data = np.nan_to_num(w3_data, nan=0.0)
        w4_data = np.nan_to_num(w4_data, nan=0.0)

        # 确保两个波段的尺寸一致（使用W3波段的尺寸作为标准）
        if w3_data.shape != w4_data.shape:
            logger.info(f"调整W4波段数据从{w4_data.shape}到{w3_data.shape}")
            w4_data = resize(w4_data, w3_data.shape, mode='reflect', anti_aliasing=True, order=3)

        # 应用高斯平滑
        w3_smoothed = ndimage.gaussian_filter(w3_data, sigma=smooth_sigma)
        w4_smoothed = ndimage.gaussian_filter(w4_data, sigma=smooth_sigma)

        # 计算W3/W4比值
        # 避免除以零，设置一个小的阈值
        epsilon = np.finfo(float).eps
        ratio_mask = w4_smoothed > epsilon
        ratio = np.zeros_like(w3_smoothed)
        ratio[ratio_mask] = w3_smoothed[ratio_mask] / w4_smoothed[ratio_mask]

        # 限制比值范围，避免极值
        ratio_valid = ratio[ratio_mask]
        if len(ratio_valid) > 0:
            p1, p99 = np.percentile(ratio_valid, [1, 99])
            logger.info(f"W3/W4比值范围: {p1:.2f}-{p99:.2f}，均值: {np.mean(ratio_valid):.2f}")
            # 限制极值
            ratio[ratio < p1] = p1
            ratio[ratio > p99] = p99
        else:
            logger.warning("没有有效的W3/W4比值数据")
            return np.zeros_like(w3_data, dtype=bool)

        # 获取中心坐标在图像中的像素位置
        center_x, center_y = wcs.world_to_pixel(center_coord)

        # 计算像素尺度
        test_offset = 1.0 / 3600.0  # 1角秒
        # 根据坐标系统类型创建测试坐标
        if center_coord.frame.name == 'galactic':
            test_coord = SkyCoord(l=center_coord.l + test_offset * u.degree,
                                 b=center_coord.b,
                                 frame='galactic')
        else:
            test_coord = SkyCoord(ra=center_coord.ra + test_offset * u.degree,
                                 dec=center_coord.dec,
                                 frame=center_coord.frame)
        test_x, test_y = wcs.world_to_pixel(test_coord)
        pixel_scale = np.sqrt((test_x - center_x)**2 + (test_y - center_y)**2) * 3600  # 像素/角秒
        logger.info(f"像素尺度: {pixel_scale:.2f} 像素/角秒")

        # 计算PDR搜索半径（默认为3R）
        r_eff_pixels = r_eff * 3600 / pixel_scale  # 转换为像素
        pdr_search_radius_pixels = r_eff * search_radius_factor * 3600 / pixel_scale  # 转换为像素
        # 确保最小半径为10像素
        pdr_search_radius_pixels = max(10, pdr_search_radius_pixels)
        logger.info(f"有效半径: {r_eff_pixels:.1f} 像素")
        logger.info(f"PDR搜索半径 ({search_radius_factor}R): {pdr_search_radius_pixels:.1f} 像素")

        # 创建距离图像
        y, x = np.ogrid[:ratio.shape[0], :ratio.shape[1]]
        distance_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)

        # 限制PDR搜索区域在指定半径内
        pdr_search_mask = distance_from_center <= pdr_search_radius_pixels

        # 使用聚类方法自动确定PDR区域
        from sklearn.cluster import KMeans

        # 准备数据：只考虑搜索区域内的有效比值
        valid_mask = pdr_search_mask & ratio_mask & np.isfinite(ratio)
        valid_ratio = ratio[valid_mask]
        valid_positions = np.array(np.where(valid_mask)).T

        if len(valid_ratio) < 100:
            logger.warning(f"有效比值数据太少 ({len(valid_ratio)} < 100)，使用备用方法")
            # 使用简单阈值作为备用方法
            w3_mean = np.mean(w3_smoothed[w3_smoothed > 0])
            w3_std = np.std(w3_smoothed[w3_smoothed > 0])
            threshold = w3_mean + 0.5 * w3_std
            pdr_mask = (w3_smoothed >= threshold) & pdr_search_mask
            pdr_mask = morphology.binary_closing(pdr_mask, morphology.disk(5))
            pdr_mask = ndi.binary_fill_holes(pdr_mask)
            logger.info(f"备用方法后PDR掩模覆盖{np.sum(pdr_mask)}个像素")
        else:
            # 准备聚类数据：W3强度、W4强度、W3/W4比值和位置信息
            # 提取有效像素的W3和W4强度
            valid_w3 = w3_smoothed[valid_mask]
            valid_w4 = w4_smoothed[valid_mask]

            # 归一化W3和W4强度
            if np.max(valid_w3) > np.min(valid_w3):
                w3_normalized = (valid_w3 - np.min(valid_w3)) / (np.max(valid_w3) - np.min(valid_w3))
            else:
                w3_normalized = np.zeros_like(valid_w3)

            if np.max(valid_w4) > np.min(valid_w4):
                w4_normalized = (valid_w4 - np.min(valid_w4)) / (np.max(valid_w4) - np.min(valid_w4))
            else:
                w4_normalized = np.zeros_like(valid_w4)

            # 将位置信息归一化，使其与强度和比值具有相似的尺度
            positions_scaled = valid_positions.copy()
            positions_scaled[:, 0] = (positions_scaled[:, 0] - center_y) / pdr_search_radius_pixels
            positions_scaled[:, 1] = (positions_scaled[:, 1] - center_x) / pdr_search_radius_pixels

            # 组合所有特征：W3强度、W4强度、W3/W4比值和位置信息
            X = np.column_stack([w3_normalized, w4_normalized, valid_ratio, positions_scaled])

            logger.info(f"聚类特征包含: W3强度, W4强度, W3/W4比值, 位置信息")

            # 根据环境变量决定使用KMeans还是DBSCAN
            use_dbscan = os.environ.get('USE_DBSCAN', 'true').lower() == 'true'

            if use_dbscan:
                logger.info("使用DBSCAN聚类算法")
                from sklearn.cluster import DBSCAN
                from sklearn.neighbors import NearestNeighbors
            else:
                logger.info("使用KMeans聚类算法")
                from sklearn.cluster import KMeans
                from sklearn.metrics import silhouette_score

            if use_dbscan:
                # 使用DBSCAN聚类
                # 计算min_samples参数（总样本数的2%，但不少于5个样本）
                min_samples = max(5, int(0.02 * len(X)))
                logger.info(f"DBSCAN min_samples参数: {min_samples} (总样本数的2%)")

                # 自适应确定eps参数
                # 方法1：使用k-距离图的拐点
                k = min(min_samples, len(X) - 1)  # 确保k不超过样本数
                nbrs = NearestNeighbors(n_neighbors=k).fit(X)
                distances, _ = nbrs.kneighbors(X)
                # 获取每个点到其第k个最近邻居的距离
                k_distances = distances[:, -1]
                # 排序距离
                k_distances_sorted = np.sort(k_distances)

                # 尝试找到拐点（距离突然增大的点）
                # 计算距离的一阶差分
                diffs = np.diff(k_distances_sorted)
                # 找到差分最大的点的索引
                knee_idx = np.argmax(diffs) + 1  # +1是因为diff减少了一个元素
                # 使用拐点作为eps
                eps_knee = k_distances_sorted[knee_idx]

                # 方法2：使用距离分布的百分位数
                # 尝试多个百分位数，找到产生合适聚类数的那个
                percentiles = [10, 15, 20, 25, 30]
                eps_values = [np.percentile(k_distances, p) for p in percentiles]

                # 记录每个eps值
                logger.info(f"DBSCAN eps候选值: 拐点={eps_knee:.4f}, 百分位数={[f'{p}%={eps:.4f}' for p, eps in zip(percentiles, eps_values)]}")

                # 尝试不同的eps值，选择产生合适聚类数的那个
                best_eps = None
                best_n_clusters = 0
                best_labels = None
                target_n_clusters = 3  # 目标聚类数（不包括噪声）

                # 首先尝试拐点eps
                dbscan = DBSCAN(eps=eps_knee, min_samples=min_samples).fit(X)
                labels = dbscan.labels_
                n_clusters = len(set(labels)) - (1 if -1 in labels else 0)  # 不计算噪声
                logger.info(f"使用拐点eps={eps_knee:.4f}，找到{n_clusters}个聚类，噪声点比例={np.sum(labels == -1) / len(labels):.2%}")

                if 2 <= n_clusters <= 5:  # 如果聚类数在合理范围内
                    best_eps = eps_knee
                    best_n_clusters = n_clusters
                    best_labels = labels

                # 如果拐点eps不理想，尝试百分位数eps
                if best_eps is None:
                    for p, eps in zip(percentiles, eps_values):
                        dbscan = DBSCAN(eps=eps, min_samples=min_samples).fit(X)
                        labels = dbscan.labels_
                        n_clusters = len(set(labels)) - (1 if -1 in labels else 0)  # 不计算噪声
                        noise_ratio = np.sum(labels == -1) / len(labels)
                        logger.info(f"使用{p}%分位数eps={eps:.4f}，找到{n_clusters}个聚类，噪声点比例={noise_ratio:.2%}")

                        # 如果聚类数在合理范围内，且噪声点比例不太高
                        if 2 <= n_clusters <= 5 and noise_ratio < 0.5:
                            # 如果这个eps值产生的聚类数更接近目标聚类数，或者是第一个合适的eps值
                            if best_eps is None or abs(n_clusters - target_n_clusters) < abs(best_n_clusters - target_n_clusters):
                                best_eps = eps
                                best_n_clusters = n_clusters
                                best_labels = labels

                # 如果仍然没有找到合适的eps值，使用产生最多聚类的eps值（但不超过5个）
                if best_eps is None:
                    max_clusters = 0
                    for p, eps in zip(percentiles, eps_values):
                        dbscan = DBSCAN(eps=eps, min_samples=min_samples).fit(X)
                        labels = dbscan.labels_
                        n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
                        if 1 <= n_clusters <= 5 and n_clusters > max_clusters:
                            max_clusters = n_clusters
                            best_eps = eps
                            best_n_clusters = n_clusters
                            best_labels = labels

                # 如果所有尝试都失败，使用最小的eps值
                if best_eps is None:
                    best_eps = min(eps_values)
                    dbscan = DBSCAN(eps=best_eps, min_samples=min_samples).fit(X)
                    best_labels = dbscan.labels_
                    best_n_clusters = len(set(best_labels)) - (1 if -1 in best_labels else 0)
                    logger.warning(f"无法找到合适的eps值，使用最小值{best_eps:.4f}，找到{best_n_clusters}个聚类")

                # 使用最佳eps值进行最终聚类
                logger.info(f"使用最佳eps值: {best_eps:.4f}，找到{best_n_clusters}个聚类")
                cluster_labels = best_labels

            else:
                # 使用KMeans聚类
                best_n_clusters = 3  # 默认聚类数
                best_silhouette = -1

                # 尝试不同的聚类数，选择最佳的
                for n_clusters in range(2, 5):
                    try:
                        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                        labels = kmeans.fit_predict(X)

                        # 如果聚类数大于1，计算轮廓系数
                        if len(np.unique(labels)) > 1:
                            silhouette_avg = silhouette_score(X, labels)
                            logger.info(f"聚类数={n_clusters}的轮廓系数: {silhouette_avg:.3f}")

                            if silhouette_avg > best_silhouette:
                                best_silhouette = silhouette_avg
                                best_n_clusters = n_clusters
                    except Exception as e:
                        logger.warning(f"聚类数={n_clusters}时出错: {str(e)}")

                # 使用最佳聚类数进行聚类
                logger.info(f"使用最佳聚类数: {best_n_clusters}")
                kmeans = KMeans(n_clusters=best_n_clusters, random_state=42, n_init=10)
                cluster_labels = kmeans.fit_predict(X)

            # 分析各聚类的特征（包括噪声点）
            unique_labels = np.unique(cluster_labels)
            cluster_stats = []

            # 计算每个聚类的特征
            for label in unique_labels:
                # 跳过噪声点（标签为-1）
                if label == -1:
                    noise_mask = cluster_labels == -1
                    noise_ratio = np.sum(noise_mask) / len(cluster_labels)
                    logger.info(f"噪声点: {np.sum(noise_mask)}个 ({noise_ratio:.2%})")
                    continue

                cluster_mask = cluster_labels == label
                cluster_ratio = valid_ratio[cluster_mask]
                cluster_w3 = valid_w3[cluster_mask]
                cluster_w4 = valid_w4[cluster_mask]
                cluster_positions = valid_positions[cluster_mask]

                # 计算聚类的统计特征
                cluster_ratio_mean = np.mean(cluster_ratio)
                cluster_ratio_std = np.std(cluster_ratio)
                cluster_w3_mean = np.mean(cluster_w3)
                cluster_w3_std = np.std(cluster_w3)
                cluster_w4_mean = np.mean(cluster_w4)
                cluster_w4_std = np.std(cluster_w4)
                cluster_size = np.sum(cluster_mask)

                # 计算聚类中心到HII区中心的平均距离
                cluster_distances = np.sqrt(
                    ((cluster_positions[:, 0] - center_y) ** 2) +
                    ((cluster_positions[:, 1] - center_x) ** 2)
                )
                cluster_mean_distance = np.mean(cluster_distances)

                # 计算聚类的紧凑性（平均到聚类中心的距离）
                if len(cluster_positions) > 1:
                    cluster_center_y, cluster_center_x = np.mean(cluster_positions, axis=0)
                    cluster_compactness = np.mean(
                        np.sqrt(
                            ((cluster_positions[:, 0] - cluster_center_y) ** 2) +
                            ((cluster_positions[:, 1] - cluster_center_x) ** 2)
                        )
                    )
                else:
                    cluster_compactness = 0

                # 计算聚类的形状特征（凸包面积与聚类面积的比值）
                # 值越接近1，形状越规则；值越大，形状越不规则
                try:
                    from scipy.spatial import ConvexHull
                    if len(cluster_positions) > 3:  # 需要至少4个点才能计算凸包
                        hull = ConvexHull(cluster_positions)
                        convex_area = hull.volume  # 2D中volume实际上是面积
                        shape_irregularity = convex_area / cluster_size if cluster_size > 0 else float('inf')
                    else:
                        shape_irregularity = 1.0
                except Exception as e:
                    logger.warning(f"计算聚类{label}的形状特征时出错: {str(e)}")
                    shape_irregularity = 1.0

                # 存储聚类统计信息
                cluster_stats.append({
                    'id': label,
                    'ratio_mean': cluster_ratio_mean,
                    'ratio_std': cluster_ratio_std,
                    'w3_mean': cluster_w3_mean,
                    'w3_std': cluster_w3_std,
                    'w4_mean': cluster_w4_mean,
                    'w4_std': cluster_w4_std,
                    'size': cluster_size,
                    'mean_distance': cluster_mean_distance,
                    'compactness': cluster_compactness,
                    'shape_irregularity': shape_irregularity
                })

                logger.info(f"聚类 {label}: 平均比值={cluster_ratio_mean:.2f}±{cluster_ratio_std:.2f}, "
                           f"W3强度={cluster_w3_mean:.2f}±{cluster_w3_std:.2f}, "
                           f"W4强度={cluster_w4_mean:.2f}±{cluster_w4_std:.2f}, "
                           f"大小={cluster_size}, 平均距离={cluster_mean_distance:.1f}, "
                           f"紧凑性={cluster_compactness:.1f}, 形状不规则度={shape_irregularity:.2f}")

            # 如果没有找到任何聚类，使用备用方法
            if len(cluster_stats) == 0:
                logger.warning("DBSCAN没有找到任何聚类，使用备用方法")
                # 使用简单阈值作为备用方法
                w3_mean = np.mean(w3_smoothed[w3_smoothed > 0])
                w3_std = np.std(w3_smoothed[w3_smoothed > 0])
                threshold = w3_mean + 0.5 * w3_std
                pdr_mask = (w3_smoothed >= threshold) & pdr_search_mask
                pdr_mask = morphology.binary_closing(pdr_mask, morphology.disk(5))
                pdr_mask = ndi.binary_fill_holes(pdr_mask)
                logger.info(f"备用方法后PDR掩模覆盖{np.sum(pdr_mask)}个像素")
                return pdr_mask, w3_smoothed, w4_smoothed

            # 识别PDR聚类：PDR区域通常具有较高的W3/W4比值，中等的W3强度，距离中心适中
            # 首先按照到中心的距离排序
            cluster_stats.sort(key=lambda x: x['mean_distance'])

            # 计算全局统计量，用于归一化
            all_w3_mean = np.mean(valid_w3)
            all_w3_std = np.std(valid_w3)
            all_ratio_mean = np.mean(valid_ratio)
            all_ratio_std = np.std(valid_ratio)

            # 为每个聚类计算PDR得分
            for cluster in cluster_stats:
                # 归一化W3强度和比值
                w3_z = (cluster['w3_mean'] - all_w3_mean) / all_w3_std if all_w3_std > 0 else 0
                ratio_z = (cluster['ratio_mean'] - all_ratio_mean) / all_ratio_std if all_ratio_std > 0 else 0

                # PDR得分：高比值、中等W3强度、适中距离的聚类得分高
                # 比值越高越好
                ratio_score = ratio_z
                # W3强度应该适中（不太高也不太低）
                w3_score = 1.0 - abs(w3_z)
                # 距离应该适中（不太近也不太远）
                distance_factor = cluster['mean_distance'] / pdr_search_radius_pixels
                distance_score = 1.0 - abs(distance_factor - 0.5) * 2  # 0.5是理想距离因子

                # 综合得分（加权平均）
                cluster['pdr_score'] = 0.5 * ratio_score + 0.3 * w3_score + 0.2 * distance_score

                logger.info(f"聚类 {cluster['id']} PDR得分: {cluster['pdr_score']:.2f} "
                           f"(比值得分={ratio_score:.2f}, W3得分={w3_score:.2f}, 距离得分={distance_score:.2f})")

            # 选择PDR得分最高的聚类作为PDR区域
            cluster_stats.sort(key=lambda x: x['pdr_score'], reverse=True)
            pdr_cluster_id = cluster_stats[0]['id']

            logger.info(f"选择聚类 {pdr_cluster_id} 作为PDR区域，PDR得分={cluster_stats[0]['pdr_score']:.2f}")

            # 创建PDR掩模
            pdr_mask = np.zeros_like(ratio, dtype=bool)
            pdr_mask[valid_mask] = cluster_labels == pdr_cluster_id

            # 应用形态学操作，填充孔洞并平滑边界
            pdr_mask = morphology.binary_closing(pdr_mask, morphology.disk(3))
            pdr_mask = ndi.binary_fill_holes(pdr_mask)

            # 检查PDR区域大小
            pdr_pixels = np.sum(pdr_mask)
            logger.info(f"PDR掩模覆盖{pdr_pixels}个像素")

            # 如果PDR区域太小，尝试使用备用方法
            min_pdr_size = 100  # 最小像素数
            if pdr_pixels < min_pdr_size:
                logger.warning(f"PDR区域太小 ({pdr_pixels} < {min_pdr_size})，使用备用方法")
                # 使用简单阈值作为备用方法
                w3_mean = np.mean(w3_smoothed[w3_smoothed > 0])
                w3_std = np.std(w3_smoothed[w3_smoothed > 0])
                threshold = w3_mean + 0.5 * w3_std
                pdr_mask = (w3_smoothed >= threshold) & pdr_search_mask
                pdr_mask = morphology.binary_closing(pdr_mask, morphology.disk(5))
                pdr_mask = ndi.binary_fill_holes(pdr_mask)
                logger.info(f"备用方法后PDR掩模覆盖{np.sum(pdr_mask)}个像素")

        # 获取连通区域
        labels = measure.label(pdr_mask)
        regions = measure.regionprops(labels)

        # 如果有多个连通区域，只保留包含中心或最近的区域
        if len(regions) > 1:
            logger.info(f"发现{len(regions)}个连通区域，选择包含中心或最近的区域")

            # 获取中心坐标的整数值
            if hasattr(center_x, 'item'):
                center_x_int = int(center_x.item())
            else:
                center_x_int = int(center_x)

            if hasattr(center_y, 'item'):
                center_y_int = int(center_y.item())
            else:
                center_y_int = int(center_y)

            if 0 <= center_y_int < labels.shape[0] and 0 <= center_x_int < labels.shape[1]:
                center_label = labels[center_y_int, center_x_int]
                if center_label > 0:
                    # 只保留包含中心的区域
                    pdr_mask = labels == center_label
                else:
                    # 如果中心不在任何区域内，选择最近的区域
                    min_dist = float('inf')
                    closest_label = 0
                    for region in regions:
                        y, x = region.centroid
                        dist = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                        if dist < min_dist:
                            min_dist = dist
                            closest_label = region.label
                    pdr_mask = labels == closest_label

        # 如果需要保存比值图像
        if save_ratio_plot and output_dir:
            # 计算空腔掩模
            from src.region_mapper.base import define_cavity_mask
            try:
                cavity_mask = define_cavity_mask(pdr_mask, w4_smoothed, wcs, center_coord, r_eff)
            except Exception as e:
                logger.warning(f"计算空腔掩模失败: {str(e)}")
                cavity_mask = None

            save_ratio_plot_image(
                ratio, pdr_mask, center_x, center_y, r_eff_pixels,
                pdr_search_radius_pixels, source_name, output_dir,
                cluster_stats if 'cluster_stats' in locals() else None,
                w3_smoothed, w4_smoothed, cavity_mask,
                cluster_labels if 'cluster_labels' in locals() else None,
                valid_mask if 'valid_mask' in locals() else None,
                valid_positions if 'valid_positions' in locals() else None
            )

        logger.info(f"W3/W4波段比值方法PDR掩模创建完成，最终覆盖{np.sum(pdr_mask)}个像素")
        return pdr_mask, w3_smoothed, w4_smoothed

    except Exception as e:
        logger.error(f"使用W3/W4波段比值方法定义PDR掩模失败: {str(e)}")
        # 出错时返回空掩模和空数据
        empty_array = np.zeros((100, 100), dtype=bool)
        return empty_array, empty_array, empty_array  # 返回默认大小的空掩模和空数据

def save_ratio_plot_image(ratio_data, pdr_mask, center_x, center_y, r_eff_pixels,
                      pdr_search_radius_pixels, source_name, output_dir,
                      cluster_stats=None, w3_data=None, w4_data=None, cavity_mask=None,
                      cluster_labels=None, valid_mask=None, valid_positions=None):
    """
    保存W3/W4比值图像

    Args:
        ratio_data: W3/W4比值数据
        pdr_mask: PDR掩模
        center_x: 中心X坐标
        center_y: 中心Y坐标
        r_eff_pixels: 有效半径（像素）
        pdr_search_radius_pixels: PDR搜索半径（像素）
        source_name: 源名称
        output_dir: 输出目录
        cluster_stats: 聚类统计信息，默认为None
        w3_data: W3波段数据，默认为None
        w4_data: W4波段数据，默认为None
        cavity_mask: 空腔掩模，默认为None
        cluster_labels: 聚类标签，默认为None
        valid_mask: 有效像素掩模，默认为None
        valid_positions: 有效像素位置，默认为None
    """
    try:
        # 创建可视化目录
        vis_dir = os.path.join(output_dir, 'visualizations')
        ensure_directory(vis_dir)

        # 设置图像路径
        ratio_plot_path = os.path.join(vis_dir, f"{source_name}_w3w4_ratio.png")

        # 创建图像
        plt.figure(figsize=(15, 12))

        # 绘制比值图像
        plt.subplot(231)
        plt.title("W3/W4 Ratio")
        im = plt.imshow(ratio_data, origin='lower', cmap='viridis')
        plt.colorbar(im, label='W3/W4 Ratio')
        plt.plot(center_x, center_y, 'r+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False, color='r', linestyle='--')
        plt.gca().add_patch(circle)
        circle = plt.Circle((center_x, center_y), pdr_search_radius_pixels, fill=False, color='white')
        plt.gca().add_patch(circle)

        # 绘制W3数据
        plt.subplot(232)
        plt.title("W3 (12μm)")
        if w3_data is not None:
            w3_display = np.log1p(w3_data)
            w3_display = w3_display / np.percentile(w3_display[w3_display > 0], 99.5) if np.any(w3_display > 0) else w3_display
            im = plt.imshow(w3_display, origin='lower', cmap='inferno')
            plt.colorbar(im, label='log(W3+1) [normalized]')
        else:
            plt.text(0.5, 0.5, "W3 data not available", ha='center', va='center', transform=plt.gca().transAxes)
        plt.plot(center_x, center_y, 'c+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False, color='c', linestyle='--')
        plt.gca().add_patch(circle)

        # 绘制W4数据
        plt.subplot(233)
        plt.title("W4 (22μm)")
        if w4_data is not None:
            w4_display = np.log1p(w4_data)
            w4_display = w4_display / np.percentile(w4_display[w4_display > 0], 99.5) if np.any(w4_display > 0) else w4_display
            im = plt.imshow(w4_display, origin='lower', cmap='inferno')
            plt.colorbar(im, label='log(W4+1) [normalized]')
        else:
            plt.text(0.5, 0.5, "W4 data not available", ha='center', va='center', transform=plt.gca().transAxes)
        plt.plot(center_x, center_y, 'c+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False, color='c', linestyle='--')
        plt.gca().add_patch(circle)

        # 绘制聚类结果
        plt.subplot(234)
        plt.title("DBSCAN Clustering")
        if cluster_labels is not None and valid_mask is not None and valid_positions is not None:
            # 创建一个空白图像
            cluster_image = np.zeros_like(ratio_data) - 1  # 初始化为-1（噪声）

            # 填充聚类标签
            for i, pos in enumerate(valid_positions):
                y, x = int(pos[0]), int(pos[1])
                if 0 <= y < cluster_image.shape[0] and 0 <= x < cluster_image.shape[1]:
                    cluster_image[y, x] = cluster_labels[i]

            # 创建自定义颜色映射，噪声点为黑色
            from matplotlib.colors import ListedColormap
            import matplotlib.colors as mcolors

            # 获取唯一标签（包括噪声）
            unique_labels = np.unique(cluster_labels)
            n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)

            # 创建颜色映射
            colors = plt.cm.tab10(np.linspace(0, 1, max(10, n_clusters)))
            cmap_list = ['black']  # 噪声点为黑色
            cmap_list.extend([mcolors.rgb2hex(colors[i % 10][:3]) for i in range(n_clusters)])
            custom_cmap = ListedColormap(cmap_list)

            # 显示聚类结果
            plt.imshow(cluster_image, origin='lower', cmap=custom_cmap, vmin=-1, vmax=n_clusters-1)

            # 添加图例
            if cluster_stats is not None:
                handles = []
                labels = []
                for cluster in cluster_stats:
                    color = colors[cluster['id'] % 10]
                    handles.append(plt.Line2D([0], [0], marker='o', color='w',
                                             markerfacecolor=mcolors.rgb2hex(color[:3]), markersize=8))
                    labels.append(f"Cluster {cluster['id']}")
                plt.legend(handles, labels, loc='upper right', fontsize='x-small')
        else:
            plt.imshow(pdr_mask, origin='lower', cmap='gray')
            plt.title("PDR Mask (No Clustering Data)")

        plt.plot(center_x, center_y, 'r+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False, color='r', linestyle='--')
        plt.gca().add_patch(circle)
        circle = plt.Circle((center_x, center_y), pdr_search_radius_pixels, fill=False, color='white')
        plt.gca().add_patch(circle)

        # 绘制PDR掩模
        plt.subplot(235)
        plt.title("PDR Mask")
        plt.imshow(pdr_mask, origin='lower', cmap='gray')
        plt.plot(center_x, center_y, 'r+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False, color='r', linestyle='--')
        plt.gca().add_patch(circle)

        # 绘制空腔掩模
        plt.subplot(236)
        plt.title("Cavity Mask")
        if cavity_mask is not None:
            plt.imshow(cavity_mask, origin='lower', cmap='gray')
        else:
            # 如果没有提供空腔掩模，使用几何定义计算一个
            y, x = np.ogrid[:pdr_mask.shape[0], :pdr_mask.shape[1]]
            distance_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)
            temp_cavity_mask = (distance_from_center <= r_eff_pixels) & ~pdr_mask
            plt.imshow(temp_cavity_mask, origin='lower', cmap='gray')
            plt.title("Cavity Mask (Geometric)")
        plt.plot(center_x, center_y, 'r+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False, color='r', linestyle='--')
        plt.gca().add_patch(circle)

        # 创建第二个图像，显示比值直方图和聚类统计信息
        plt.figure(figsize=(15, 8))

        # 绘制比值直方图
        plt.subplot(121)
        plt.title("W3/W4 Ratio Histogram")
        valid_ratio_data = ratio_data[np.isfinite(ratio_data) & (ratio_data > 0)]
        if len(valid_ratio_data) > 0:
            plt.hist(valid_ratio_data.flatten(), bins=50, alpha=0.7, color='gray')

            # 如果有聚类统计信息，显示各聚类的比值分布
            if cluster_stats is not None and cluster_labels is not None and valid_mask is not None:
                for cluster in cluster_stats:
                    if cluster['id'] == -1:  # 跳过噪声
                        continue
                    cluster_mask = cluster_labels == cluster['id']
                    if np.sum(cluster_mask) > 0:
                        # 使用聚类统计信息中的平均值和标准差
                        plt.axvline(cluster['ratio_mean'], color=f'C{cluster["id"] % 10}', linestyle='--',
                                   label=f"Cluster {cluster['id']}: {cluster['ratio_mean']:.2f}±{cluster['ratio_std']:.2f}")

            plt.xlabel('W3/W4 Ratio')
            plt.ylabel('Frequency')
            plt.legend(fontsize='small')

        # 绘制聚类统计信息
        plt.subplot(122)
        plt.title("Cluster Statistics")
        if cluster_stats is not None:
            # 创建表格显示聚类统计信息
            cluster_ids = [c['id'] for c in cluster_stats if c['id'] != -1]
            if len(cluster_ids) > 0:
                # 提取要显示的统计信息
                stats = {
                    'W3/W4 Ratio': [f"{c['ratio_mean']:.2f}±{c['ratio_std']:.2f}" for c in cluster_stats if c['id'] != -1],
                    'W3 Intensity': [f"{c['w3_mean']:.2f}±{c['w3_std']:.2f}" for c in cluster_stats if c['id'] != -1],
                    'Size (pixels)': [c['size'] for c in cluster_stats if c['id'] != -1],
                    'Distance': [f"{c['mean_distance']:.1f}" for c in cluster_stats if c['id'] != -1],
                    'Irregularity': [f"{c['shape_irregularity']:.2f}" for c in cluster_stats if c['id'] != -1],
                    'PDR Score': [f"{c.get('pdr_score', 0):.2f}" for c in cluster_stats if c['id'] != -1]
                }

                # 创建表格
                plt.axis('off')
                table = plt.table(
                    cellText=[stats[k] for k in stats.keys()],
                    rowLabels=list(stats.keys()),
                    colLabels=[f"Cluster {i}" for i in cluster_ids],
                    loc='center',
                    cellLoc='center'
                )
                table.auto_set_font_size(False)
                table.set_fontsize(9)
                table.scale(1.2, 1.5)

                # 高亮PDR区域所在的聚类
                if 'pdr_score' in cluster_stats[0]:
                    pdr_cluster_idx = cluster_ids.index(cluster_stats[0]['id'])
                    for i in range(len(stats.keys())):
                        cell = table.get_celld()[i, pdr_cluster_idx]
                        cell.set_facecolor('lightgreen')
            else:
                plt.text(0.5, 0.5, "No valid clusters found", ha='center', va='center', transform=plt.gca().transAxes)
        else:
            plt.text(0.5, 0.5, "No cluster statistics available", ha='center', va='center', transform=plt.gca().transAxes)

        # 调整布局并保存第一个图像
        plt.figure(1)
        plt.tight_layout()
        plt.savefig(ratio_plot_path, dpi=300)

        # 保存第二个图像（聚类统计信息）
        plt.figure(2)
        plt.tight_layout()
        stats_plot_path = os.path.join(vis_dir, f"{source_name}_cluster_stats.png")
        plt.savefig(stats_plot_path, dpi=300)

        # 关闭所有图像
        plt.close('all')

        logger.info(f"成功保存W3/W4比值图像到: {ratio_plot_path}")
        logger.info(f"成功保存聚类统计信息到: {stats_plot_path}")

    except Exception as e:
        logger.error(f"保存W3/W4比值图像失败: {str(e)}")
