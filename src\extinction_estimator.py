"""
消光估计模块

负责计算恒星的消光值，使用近红外或Gaia颜色。
"""

import numpy as np
from astropy.table import Table, Column

from src.utils.logger import setup_logger
from src.utils.helpers import load_config

# 设置日志记录器
logger = setup_logger(name='extinction_estimator')

def estimate_intrinsic_hk(star_table, method='select_giants', config_path='config/default_config.yaml'):
    """
    估计恒星的本征H-K颜色

    Args:
        star_table: 恒星表 (astropy.table.Table)
        method: 估计方法 ('select_giants', 'fixed_value')
        config_path: 配置文件路径

    Returns:
        numpy.ndarray: 本征H-K颜色数组
    """
    logger.info(f"估计本征H-K颜色，方法: {method}")

    try:
        # 初始化本征颜色数组
        intrinsic_hk = np.zeros(len(star_table))

        if method == 'select_giants':
            # 使用红巨星的典型颜色
            # 这里需要实现红巨星的选择逻辑
            # 简单示例：假设所有恒星都是红巨星，使用固定值
            intrinsic_hk.fill(0.15)  # 红巨星的典型H-K颜色
            logger.info(f"使用红巨星的典型H-K颜色: 0.15")

        elif method == 'fixed_value':
            # 使用固定值
            intrinsic_hk.fill(0.1)  # 主序星的典型H-K颜色
            logger.info(f"使用固定的H-K颜色: 0.1")

        else:
            logger.warning(f"未知的本征颜色估计方法: {method}，使用默认值0.1")
            intrinsic_hk.fill(0.1)

        return intrinsic_hk

    except Exception as e:
        logger.error(f"估计本征H-K颜色失败: {str(e)}")
        raise

def calculate_av_nir(h_mag, k_mag, intrinsic_hk, rv=3.1):
    """
    基于近红外颜色过量计算A_V

    Args:
        h_mag: H波段星等
        k_mag: K波段星等
        intrinsic_hk: 本征H-K颜色
        rv: 消光律R_V值

    Returns:
        numpy.ndarray: A_V值
    """
    logger.info(f"基于H-K颜色过量计算A_V，R_V={rv}")

    try:
        # 计算观测到的H-K颜色
        observed_hk = h_mag - k_mag

        # 计算颜色过量E(H-K)
        ehk = observed_hk - intrinsic_hk

        # 转换为A_V
        # 使用Cardelli et al. (1989)消光律
        # A_V = E(H-K) * (A_V/E(H-K))
        # 对于R_V=3.1，A_V/E(H-K) ≈ 15.87
        av_ehk_ratio = 15.87  # 对于R_V=3.1

        # 如果R_V不是3.1，需要调整比例
        if rv != 3.1:
            # 这是一个近似调整，实际上需要更复杂的计算
            av_ehk_ratio *= (rv / 3.1)

        # 计算A_V
        av = ehk * av_ehk_ratio

        # 处理负值
        av[av < 0] = 0

        logger.info(f"计算得到{len(av)}个A_V值，范围: {np.min(av):.2f}-{np.max(av):.2f}")
        return av

    except Exception as e:
        logger.error(f"计算A_V失败: {str(e)}")
        raise

def calculate_av_gaia(bp_rp, bp_rp_intrinsic, rv=3.1):
    """
    基于Gaia颜色过量计算A_V

    Args:
        bp_rp: Gaia BP-RP颜色
        bp_rp_intrinsic: 本征BP-RP颜色
        rv: 消光律R_V值

    Returns:
        numpy.ndarray: A_V值
    """
    logger.info(f"基于Gaia BP-RP颜色过量计算A_V，R_V={rv}")

    try:
        # 计算颜色过量E(BP-RP)
        ebprp = bp_rp - bp_rp_intrinsic

        # 转换为A_V
        # 使用Gaia DR2推荐的转换关系
        # 对于R_V=3.1，A_V/E(BP-RP) ≈ 2.1
        av_ebprp_ratio = 2.1  # 对于R_V=3.1

        # 如果R_V不是3.1，需要调整比例
        if rv != 3.1:
            # 这是一个近似调整，实际上需要更复杂的计算
            av_ebprp_ratio *= (rv / 3.1)

        # 计算A_V
        av = ebprp * av_ebprp_ratio

        # 处理负值
        av[av < 0] = 0

        logger.info(f"计算得到{len(av)}个A_V值，范围: {np.min(av):.2f}-{np.max(av):.2f}")
        return av

    except Exception as e:
        logger.error(f"计算A_V失败: {str(e)}")
        raise

def estimate_bp_rp_intrinsic(star_table, method='fixed_value'):
    """
    估计恒星的本征BP-RP颜色

    Args:
        star_table: 恒星表 (astropy.table.Table)
        method: 估计方法 ('fixed_value', 'teff_based')

    Returns:
        numpy.ndarray: 本征BP-RP颜色数组
    """
    logger.info(f"估计本征BP-RP颜色，方法: {method}")

    try:
        # 初始化本征颜色数组
        intrinsic_bprp = np.zeros(len(star_table))

        if method == 'fixed_value':
            # 使用固定值
            intrinsic_bprp.fill(0.8)  # 典型G型恒星的BP-RP颜色
            logger.info(f"使用固定的BP-RP颜色: 0.8")

        elif method == 'teff_based':
            # 基于有效温度估计本征颜色
            # 这里需要实现基于有效温度的颜色估计
            # 简单示例：
            if 'teff' in star_table.colnames:
                # 这是一个非常简化的关系，实际上需要更复杂的模型
                teff = star_table['teff']
                # 简单线性关系示例
                intrinsic_bprp = 2.5 - 0.0005 * teff
                logger.info(f"基于有效温度估计BP-RP颜色")
            else:
                logger.warning("表中缺少有效温度数据，使用默认值0.8")
                intrinsic_bprp.fill(0.8)

        else:
            logger.warning(f"未知的本征颜色估计方法: {method}，使用默认值0.8")
            intrinsic_bprp.fill(0.8)

        return intrinsic_bprp

    except Exception as e:
        logger.error(f"估计本征BP-RP颜色失败: {str(e)}")
        raise

def compute_av_per_star(star_table, config_path='config/default_config.yaml'):
    """
    为每个恒星计算A_V

    Args:
        star_table: 恒星表 (astropy.table.Table)
        config_path: 配置文件路径

    Returns:
        astropy.table.Table: 添加了A_V列的表
    """
    config = load_config(config_path)
    extinction_params = config['processing']['extinction']

    logger.info(f"为{len(star_table)}个恒星计算A_V，参数: {extinction_params}")

    # 检查表是否为空
    if len(star_table) == 0:
        logger.warning("星表为空，无法计算A_V")
        # 返回一个空表，但保留原始结构
        return star_table

    try:
        # 创建结果表
        result_table = star_table.copy()

        # 初始化A_V列和方法列
        av_values = np.zeros(len(star_table))
        av_methods = np.full(len(star_table), 'unknown', dtype='U10')

        # 检查是否有2MASS数据
        has_2mass = all(col in star_table.colnames for col in ['h_m', 'k_m'])

        if has_2mass and len(star_table) > 0:
            # 检查是否有有效的2MASS数据
            valid_2mass = ~np.isnan(star_table['h_m']) & ~np.isnan(star_table['k_m'])

            if np.sum(valid_2mass) > 0:
                logger.info("使用2MASS H-K颜色计算A_V")

                # 估计本征H-K颜色
                intrinsic_hk = estimate_intrinsic_hk(star_table, method='select_giants')

                # 计算A_V
                av_nir = np.zeros(len(star_table))
                av_nir[valid_2mass] = calculate_av_nir(
                    star_table['h_m'][valid_2mass],
                    star_table['k_m'][valid_2mass],
                    intrinsic_hk[valid_2mass],
                    rv=extinction_params['rv']
                )

                # 更新A_V值和方法
                av_values = av_nir
                av_methods[valid_2mass] = '2MASS_HK'
            else:
                logger.warning("没有有效的2MASS数据")

        # 如果没有2MASS数据或者有些源没有有效的2MASS数据，使用Gaia数据
        if (not has_2mass or np.any(av_methods == 'unknown')) and len(star_table) > 0:
            logger.info("使用Gaia BP-RP颜色计算A_V")

            # 检查是否有Gaia颜色数据
            has_gaia_colors = 'bp_rp' in star_table.colnames

            if has_gaia_colors:
                # 检查是否有有效的Gaia颜色数据
                valid_gaia = ~np.isnan(star_table['bp_rp'])

                if np.sum(valid_gaia) > 0:
                    # 估计本征BP-RP颜色
                    intrinsic_bprp = estimate_bp_rp_intrinsic(star_table, method='fixed_value')

                    # 计算A_V
                    av_gaia = np.zeros(len(star_table))
                    av_gaia[valid_gaia] = calculate_av_gaia(
                        star_table['bp_rp'][valid_gaia],
                        intrinsic_bprp[valid_gaia],
                        rv=extinction_params['rv']
                    )

                    # 更新没有2MASS数据的源
                    if not has_2mass:
                        av_values = av_gaia
                        av_methods[valid_gaia] = 'Gaia_BPRP'
                    else:
                        # 对于有2MASS数据但A_V无效的源，使用Gaia数据
                        mask = (av_methods == 'unknown') & valid_gaia
                        av_values[mask] = av_gaia[mask]
                        av_methods[mask] = 'Gaia_BPRP'
                else:
                    logger.warning("没有有效的Gaia颜色数据")
            else:
                logger.warning("表中既缺少2MASS数据又缺少Gaia颜色数据，无法计算A_V")

        # 添加A_V列和方法列
        if 'a_v' not in result_table.colnames:
            result_table.add_column(Column(av_values, name='a_v'))
        else:
            result_table['a_v'] = av_values

        if 'av_method' not in result_table.colnames:
            result_table.add_column(Column(av_methods, name='av_method'))
        else:
            result_table['av_method'] = av_methods

        # 统计各方法的使用情况
        for method in np.unique(av_methods):
            count = np.sum(av_methods == method)
            logger.info(f"方法 {method}: {count}个源")

        # 检查是否有有效的A_V值
        if len(av_values) > 0 and not np.all(np.isnan(av_values)):
            min_av = np.nanmin(av_values)
            max_av = np.nanmax(av_values)
            mean_av = np.nanmean(av_values)
            median_av = np.nanmedian(av_values)
            std_av = np.nanstd(av_values)

            logger.info(f"A_V计算完成，统计信息:")
            logger.info(f"  范围: {min_av:.2f}-{max_av:.2f} mag")
            logger.info(f"  均值: {mean_av:.2f} mag")
            logger.info(f"  中位数: {median_av:.2f} mag")
            logger.info(f"  标准差: {std_av:.2f} mag")

            # 记录各种消光计算方法的使用情况
            if 'av_method' in result_table.colnames:
                methods = np.unique(result_table['av_method'])
                logger.info(f"消光计算方法统计:")
                for method in methods:
                    count = np.sum(result_table['av_method'] == method)
                    percentage = count / len(result_table) * 100
                    logger.info(f"  {method}: {count}颗恒星 ({percentage:.1f}%)")
        else:
            logger.warning("没有有效的A_V值")
        return result_table

    except Exception as e:
        logger.error(f"计算A_V失败: {str(e)}")
        raise
