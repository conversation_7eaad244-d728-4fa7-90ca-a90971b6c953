"""
测试DBSCAN聚类方法在PDR区域检测中的效果

此脚本对比KMeans和DBSCAN两种聚类方法在PDR区域检测中的效果。
"""

import os
import sys
import argparse
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import random
from astropy.coordinates import SkyCoord
import astropy.units as u
from astropy.io import fits
from astropy.wcs import WCS
from skimage.transform import resize
from skimage import measure
from scipy import ndimage as ndi
from datetime import datetime

# 添加src目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import setup_logger
from src.utils.helpers import load_config, ensure_directory
from src.data_manager import load_wise_fits, load_wise_multiband
from src.region_mapper.base import process_wise_image, get_region_masks
from src.region_mapper.ratio_method import define_pdr_mask_ratio

# 设置日志记录器
logger = setup_logger(name='test_dbscan_pdr_detection')

def load_hii_region_catalog(catalog_path):
    """
    加载HII区源表

    Args:
        catalog_path: 源表路径

    Returns:
        dict: 源信息字典，键为源名称
    """
    try:
        # 读取源表
        with open(catalog_path, 'r') as f:
            lines = f.readlines()

        # 过滤掉注释行和空行
        data_lines = [line for line in lines if not line.startswith('#') and line.strip()]

        # 创建源信息字典
        sources = {}
        for line in data_lines:
            parts = line.strip().split()
            if len(parts) >= 5:  # 确保有足够的列
                try:
                    name = parts[0]  # 源名称
                    method = parts[1]  # 方法
                    N = float(parts[2])  # 有效半径（角秒）
                    r_eff = N / 3600.0  # 有效半径（度）

                    # 从源名中提取银道坐标
                    if 'G' in name and ('+' in name or '-' in name):
                        # 提取l坐标（G后面的数字，直到+或-）
                        l_str = name.replace('G', '').split('+')[0].split('-')[0]
                        # 提取b坐标（l坐标后面的+或-开头的部分）
                        if '+' in name:
                            b_str = '+' + name.split('+')[1]
                        else:
                            b_str = '-' + name.split('-')[1]

                        l = float(l_str)
                        b = float(b_str)

                        sources[name] = {
                            'name': name,
                            'l': l,
                            'b': b,
                            'N': N,
                            'r_eff': r_eff,
                            'method': method
                        }
                    else:
                        logger.warning(f"源名 {name} 不包含坐标信息，跳过")

                except (ValueError, IndexError) as e:
                    logger.warning(f"解析源 {name} 时出错: {str(e)}")

        logger.info(f"成功加载{len(sources)}个HII区源")
        return sources

    except Exception as e:
        logger.error(f"加载HII区源表失败: {str(e)}")
        # 如果加载失败，返回一个包含已知可用源的字典
        fallback_sources = {
            'G010.964+00.006': {'name': 'G010.964+00.006', 'l': 10.964, 'b': 0.006, 'N': 120.0, 'r_eff': 120.0 / 3600.0}
        }
        logger.info(f"使用备用源列表，包含{len(fallback_sources)}个源")
        return fallback_sources

def process_source(source_name, config_path='config/default_config.yaml', use_dbscan=True):
    """
    处理单个HII区源

    Args:
        source_name: 源名称
        config_path: 配置文件路径
        use_dbscan: 是否使用DBSCAN聚类方法，默认为True

    Returns:
        dict: 处理结果
    """
    try:
        # 加载配置
        config = load_config(config_path)

        # 创建输出目录
        method_name = "dbscan" if use_dbscan else "kmeans"
        output_dir = os.path.join('results', f'pdr_detection_{method_name}')
        ensure_directory(output_dir)

        # 为当前源创建输出目录
        source_output_dir = os.path.join(output_dir, source_name)
        ensure_directory(source_output_dir)

        # 加载源信息
        catalog_path = config['data_paths']['hii_region_catalog']
        sources = load_hii_region_catalog(catalog_path)

        if source_name not in sources:
            logger.error(f"源 {source_name} 不在源表中")
            return {'success': False, 'error': f"源 {source_name} 不在源表中"}

        source_info = sources[source_name]

        # 获取源参数
        r_eff_deg = source_info['r_eff']  # 有效半径（度）

        # 获取银道坐标
        l, b = source_info['l'], source_info['b']
        center_coord = SkyCoord(l=l*u.degree, b=b*u.degree, frame='galactic')
        logger.info(f"源中心坐标: l={l:.6f}°, b={b:.6f}°")

        # 加载WISE 12μm数据
        wise_base_path = config['data_paths']['wise_fits_base']
        w3_data, w3_header, w3_wcs = load_wise_fits(
            source_name, wise_base_path, config_path, band='12')

        if w3_data is None:
            logger.error(f"未能加载源 {source_name} 的WISE 12μm数据")
            return {'success': False, 'error': f"未能加载WISE 12μm数据"}

        # 处理WISE图像
        processed_w3 = process_wise_image(w3_data)

        # 使用ratio_method直接定义PDR掩模
        logger.info(f"使用{'DBSCAN' if use_dbscan else 'KMeans'}聚类方法定义PDR区域")

        # 修改配置文件中的聚类方法
        if use_dbscan:
            # 确保使用DBSCAN方法
            os.environ['USE_DBSCAN'] = 'true'
        else:
            # 确保使用KMeans方法
            os.environ['USE_DBSCAN'] = 'false'

        # 调用define_pdr_mask_ratio函数
        pdr_mask, w3_data, w4_data = define_pdr_mask_ratio(
            source_name, w3_wcs, center_coord, r_eff_deg,
            search_radius_factor=3.0, smooth_sigma=1.0,
            wise_base_path=wise_base_path, config_path=config_path,
            output_dir=source_output_dir, save_ratio_plot=True
        )

        # 计算PDR区域的特征
        pdr_pixels = np.sum(pdr_mask)

        # 计算PDR区域的形状特征
        labels = measure.label(pdr_mask)
        regions = measure.regionprops(labels)

        shape_features = {}
        if len(regions) > 0:
            # 计算最大连通区域的面积
            max_region = max(regions, key=lambda r: r.area)
            shape_features['area'] = max_region.area

            # 计算周长
            shape_features['perimeter'] = max_region.perimeter

            # 计算圆形度（circularity）：4π*面积/周长²，值越接近1表示越接近圆形
            if max_region.perimeter > 0:
                shape_features['circularity'] = 4 * np.pi * max_region.area / (max_region.perimeter ** 2)
            else:
                shape_features['circularity'] = 0

            # 计算离心率（eccentricity）：值越接近1表示越细长
            shape_features['eccentricity'] = max_region.eccentricity

            # 计算不规则度（irregularity）：凸包面积与区域面积的比值
            shape_features['irregularity'] = max_region.convex_area / max_region.area if max_region.area > 0 else float('inf')

        # 创建可视化比较图
        plt.figure(figsize=(15, 10))

        # 显示原始WISE 12μm图像
        plt.subplot(221)
        plt.title(f"{source_name} - WISE 12μm")
        plt.imshow(np.log1p(w3_data), origin='lower', cmap='inferno')
        center_x, center_y = w3_wcs.world_to_pixel(center_coord)
        plt.plot(center_x, center_y, 'c+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_deg * 3600 / (w3_header['CDELT2'] * 3600),
                          fill=False, color='cyan', linestyle='--')
        plt.gca().add_patch(circle)

        # 显示PDR掩模
        plt.subplot(222)
        plt.title(f"PDR Mask ({method_name.upper()})")
        plt.imshow(pdr_mask, origin='lower', cmap='gray')
        plt.plot(center_x, center_y, 'c+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_deg * 3600 / (w3_header['CDELT2'] * 3600),
                          fill=False, color='cyan', linestyle='--')
        plt.gca().add_patch(circle)

        # 显示PDR区域轮廓叠加在原始图像上
        plt.subplot(223)
        plt.title(f"PDR Contours on 12μm ({method_name.upper()})")
        plt.imshow(np.log1p(w3_data), origin='lower', cmap='inferno')

        # 绘制PDR区域轮廓
        contours = measure.find_contours(pdr_mask.astype(float), 0.5)
        for contour in contours:
            plt.plot(contour[:, 1], contour[:, 0], 'r-', linewidth=2)

        plt.plot(center_x, center_y, 'c+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_deg * 3600 / (w3_header['CDELT2'] * 3600),
                          fill=False, color='cyan', linestyle='--')
        plt.gca().add_patch(circle)

        # 显示形状特征
        plt.subplot(224)
        plt.title(f"Shape Features ({method_name.upper()})")
        plt.axis('off')

        if shape_features:
            text = "\n".join([
                f"PDR Area: {shape_features['area']} pixels",
                f"Perimeter: {shape_features['perimeter']:.2f}",
                f"Circularity: {shape_features['circularity']:.4f}",
                f"Eccentricity: {shape_features['eccentricity']:.4f}",
                f"Irregularity: {shape_features['irregularity']:.4f}"
            ])
        else:
            text = "No PDR region detected"

        plt.text(0.5, 0.5, text, ha='center', va='center', transform=plt.gca().transAxes)

        # 保存图像
        plt.tight_layout()
        plt.savefig(os.path.join(source_output_dir, f"{source_name}_{method_name}_results.png"), dpi=300)
        plt.close()

        logger.info(f"成功处理源 {source_name} 使用 {method_name} 方法")
        return {
            'success': True,
            'source_name': source_name,
            'method': method_name,
            'pdr_pixels': pdr_pixels,
            'shape_features': shape_features
        }

    except Exception as e:
        logger.error(f"处理源 {source_name} 失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

def compare_methods(source_name, config_path='config/default_config.yaml'):
    """
    对比KMeans和DBSCAN两种方法在PDR区域检测中的效果

    Args:
        source_name: 源名称
        config_path: 配置文件路径

    Returns:
        dict: 对比结果
    """
    # 使用KMeans方法处理源
    kmeans_result = process_source(source_name, config_path, use_dbscan=False)

    # 使用DBSCAN方法处理源
    dbscan_result = process_source(source_name, config_path, use_dbscan=True)

    # 创建对比结果
    comparison = {
        'source_name': source_name,
        'kmeans': kmeans_result,
        'dbscan': dbscan_result,
        'success': kmeans_result['success'] and dbscan_result['success']
    }

    # 如果两种方法都成功，计算对比指标
    if comparison['success']:
        # 计算PDR区域大小的差异
        kmeans_pixels = kmeans_result['pdr_pixels']
        dbscan_pixels = dbscan_result['pdr_pixels']
        size_diff = dbscan_pixels - kmeans_pixels
        size_ratio = dbscan_pixels / kmeans_pixels if kmeans_pixels > 0 else float('inf')

        # 计算形状特征的差异
        shape_diff = {}
        if 'shape_features' in kmeans_result and 'shape_features' in dbscan_result:
            kmeans_shape = kmeans_result['shape_features']
            dbscan_shape = dbscan_result['shape_features']

            for feature in ['circularity', 'eccentricity', 'irregularity']:
                if feature in kmeans_shape and feature in dbscan_shape:
                    shape_diff[feature] = dbscan_shape[feature] - kmeans_shape[feature]

        comparison['metrics'] = {
            'size_diff': size_diff,
            'size_ratio': size_ratio,
            'shape_diff': shape_diff
        }

    return comparison

def main():
    """
    主函数
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='测试DBSCAN聚类方法在PDR区域检测中的效果')
    parser.add_argument('--config', type=str, default='config/default_config.yaml', help='配置文件路径')
    parser.add_argument('--sources', type=str, nargs='+', help='要处理的源名称列表')
    parser.add_argument('--num_sources', type=int, default=20, help='要随机选择的源数量，默认为20')
    parser.add_argument('--min_radius', type=float, default=60.0, help='最小有效半径（角秒），默认为60.0')
    args = parser.parse_args()

    # 加载配置
    config = load_config(args.config)

    # 加载HII区源表
    catalog_path = config['data_paths']['hii_region_catalog']
    all_sources = load_hii_region_catalog(catalog_path)

    # 如果没有指定源，随机选择符合条件的源
    if args.sources is None or len(args.sources) == 0:
        # 筛选有效半径不小于指定值的源
        eligible_sources = {name: info for name, info in all_sources.items()
                           if info['N'] >= args.min_radius}

        logger.info(f"找到{len(eligible_sources)}个有效半径不小于{args.min_radius}角秒的源")

        if len(eligible_sources) == 0:
            logger.error(f"没有找到有效半径不小于{args.min_radius}角秒的源")
            return

        # 随机选择指定数量的源
        num_to_select = min(args.num_sources, len(eligible_sources))
        selected_sources = random.sample(list(eligible_sources.keys()), num_to_select)

        logger.info(f"随机选择了{len(selected_sources)}个源进行测试")
    else:
        selected_sources = args.sources
        logger.info(f"使用指定的{len(selected_sources)}个源进行测试")

    # 处理每个源，对比两种方法
    results = []
    for source_name in selected_sources:
        logger.info(f"处理源 {source_name}")
        comparison = compare_methods(source_name, args.config)
        results.append(comparison)

    # 生成对比报告
    generate_comparison_report(results)

    return results

def generate_comparison_report(results):
    """
    生成对比报告

    Args:
        results: 对比结果列表
    """
    # 创建报告目录
    report_dir = os.path.join('results', 'comparison_reports')
    ensure_directory(report_dir)

    # 创建报告文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_path = os.path.join(report_dir, f"comparison_report_{timestamp}.md")

    # 统计成功处理的源数量
    successful_comparisons = [r for r in results if r['success']]

    with open(report_path, 'w') as f:
        f.write("# PDR区域检测方法对比报告\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(f"总共测试源数量: {len(results)}\n")
        f.write(f"成功对比源数量: {len(successful_comparisons)}\n\n")

        if len(successful_comparisons) > 0:
            # 计算统计指标
            size_diffs = [r['metrics']['size_diff'] for r in successful_comparisons if 'metrics' in r]
            size_ratios = [r['metrics']['size_ratio'] for r in successful_comparisons if 'metrics' in r]

            # 计算形状特征差异的统计指标
            shape_diff_stats = {}
            for feature in ['circularity', 'eccentricity', 'irregularity']:
                values = [r['metrics']['shape_diff'][feature] for r in successful_comparisons
                         if 'metrics' in r and 'shape_diff' in r['metrics'] and feature in r['metrics']['shape_diff']]
                if values:
                    shape_diff_stats[feature] = {
                        'mean': np.mean(values),
                        'std': np.std(values),
                        'min': np.min(values),
                        'max': np.max(values)
                    }

            # 写入统计结果
            f.write("## 统计结果\n\n")

            f.write("### PDR区域大小对比\n\n")
            f.write(f"- DBSCAN比KMeans平均多识别像素数: {np.mean(size_diffs):.2f} ± {np.std(size_diffs):.2f}\n")
            f.write(f"- DBSCAN/KMeans区域大小比值: {np.mean(size_ratios):.2f} ± {np.std(size_ratios):.2f}\n")
            f.write(f"- 最小差异: {np.min(size_diffs):.2f} 像素\n")
            f.write(f"- 最大差异: {np.max(size_diffs):.2f} 像素\n\n")

            f.write("### 形状特征对比\n\n")
            for feature, stats in shape_diff_stats.items():
                f.write(f"#### {feature.capitalize()}\n\n")
                f.write(f"- 平均差异: {stats['mean']:.4f} ± {stats['std']:.4f}\n")
                f.write(f"- 最小差异: {stats['min']:.4f}\n")
                f.write(f"- 最大差异: {stats['max']:.4f}\n\n")

            # 写入每个源的详细结果
            f.write("## 详细结果\n\n")
            for i, result in enumerate(successful_comparisons):
                source_name = result['source_name']
                f.write(f"### {i+1}. {source_name}\n\n")

                kmeans_pixels = result['kmeans']['pdr_pixels']
                dbscan_pixels = result['dbscan']['pdr_pixels']

                f.write(f"- KMeans PDR区域大小: {kmeans_pixels} 像素\n")
                f.write(f"- DBSCAN PDR区域大小: {dbscan_pixels} 像素\n")
                f.write(f"- 差异: {dbscan_pixels - kmeans_pixels} 像素 ({dbscan_pixels/kmeans_pixels:.2f}倍)\n\n")

                if 'shape_features' in result['kmeans'] and 'shape_features' in result['dbscan']:
                    kmeans_shape = result['kmeans']['shape_features']
                    dbscan_shape = result['dbscan']['shape_features']

                    f.write("#### 形状特征\n\n")
                    f.write("| 特征 | KMeans | DBSCAN | 差异 |\n")
                    f.write("|------|--------|--------|------|\n")

                    for feature in ['circularity', 'eccentricity', 'irregularity']:
                        if feature in kmeans_shape and feature in dbscan_shape:
                            kmeans_val = kmeans_shape[feature]
                            dbscan_val = dbscan_shape[feature]
                            diff = dbscan_val - kmeans_val
                            f.write(f"| {feature.capitalize()} | {kmeans_val:.4f} | {dbscan_val:.4f} | {diff:.4f} |\n")

                    f.write("\n")

                # 添加图像链接
                kmeans_img = f"../../pdr_detection_kmeans/{source_name}/{source_name}_kmeans_results.png"
                dbscan_img = f"../../pdr_detection_dbscan/{source_name}/{source_name}_dbscan_results.png"

                f.write("#### 可视化结果\n\n")
                f.write(f"- [KMeans结果]({kmeans_img})\n")
                f.write(f"- [DBSCAN结果]({dbscan_img})\n\n")

                # 添加分隔线
                if i < len(successful_comparisons) - 1:
                    f.write("---\n\n")

        else:
            f.write("没有成功的对比结果。\n")

    logger.info(f"对比报告已生成: {report_path}")

if __name__ == '__main__':
    main()
