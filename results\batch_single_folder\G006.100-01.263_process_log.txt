处理时间: 2025-05-20 09:51:16
耗时: 326.78秒

标准输出:

未能估计G006.100-01.263的分子云距离


标准错误:
2025-05-20 09:45:53,316 - main_single_folder - INFO - 输出目录: results/batch_single_folder
2025-05-20 09:45:53,317 - main_single_folder - INFO - 加载源G006.100-01.263的信息
2025-05-20 09:45:53,317 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
2025-05-20 09:45:53,323 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-05-20 09:45:53,324 - main_single_folder - INFO - 从源名解析银道坐标: l=6.100000, b=-1.263000
2025-05-20 09:45:53,330 - main_single_folder - INFO - 银道坐标转换为赤道坐标: RA=271.073039, Dec=-24.309865 (ICRS)
2025-05-20 09:45:53,330 - main_single_folder - INFO - 从源表加载信息: RA=271.073039, Dec=-24.309865, R_eff=0.405278度
2025-05-20 09:45:53,331 - main_single_folder - INFO - 步骤1：加载WISE数据
2025-05-20 09:45:53,336 - data_manager - INFO - 为源G006.100-01.263加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.100-01.263
2025-05-20 09:45:53,351 - data_manager - INFO - 目录中的所有文件: ['G006.100-01.263_ATLASGAL_870um.fits', 'G006.100-01.263_IRIS_100.fits', 'G006.100-01.263_MIPSGAL_24um.fits', 'G006.100-01.263_NVSS.fits', 'G006.100-01.263_WISE_12.fits', 'G006.100-01.263_WISE_22.fits', 'G006.100-01.263_WISE_3.4.fits', 'G006.100-01.263_WISE_4.6.fits']
2025-05-20 09:45:53,351 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:45:53,352 - data_manager - INFO - 第一次匹配结果: ['G006.100-01.263_WISE_12.fits']
2025-05-20 09:45:53,352 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.100-01.263\G006.100-01.263_WISE_12.fits
2025-05-20 09:45:53,668 - data_manager - INFO - FITS数据统计: 最小值=25.1376953125, 最大值=10432.1884765625, 均值=1288.1546630859375, 中位数=1195.2789306640625
2025-05-20 09:45:53,668 - data_manager - INFO - 有效数据点数量: 5033955/5035536 (99.97%)
2025-05-20 09:45:53,745 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (2244, 2244)
2025-05-20 09:45:53,747 - main_single_folder - INFO - WISE数据加载完成
2025-05-20 09:45:53,747 - main_single_folder - INFO - 步骤2：区域映射
2025-05-20 09:45:53,922 - main_single_folder - INFO - 使用W3/W4波段比值方法定义PDR区域
2025-05-20 09:45:53,922 - region_mapper - INFO - 使用W3/W4波段比值方法定义PDR掩模
2025-05-20 09:45:53,923 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:45:53,923 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:45:53,923 - region_mapper.ratio - INFO - 加载源G006.100-01.263的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:45:53,923 - region_mapper.ratio - INFO - 加载源G006.100-01.263的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:45:53,923 - data_manager - INFO - 为源G006.100-01.263加载多波段WISE数据: ('12', '22')
2025-05-20 09:45:53,928 - data_manager - INFO - 为源G006.100-01.263加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.100-01.263
2025-05-20 09:45:53,929 - data_manager - INFO - 目录中的所有文件: ['G006.100-01.263_ATLASGAL_870um.fits', 'G006.100-01.263_IRIS_100.fits', 'G006.100-01.263_MIPSGAL_24um.fits', 'G006.100-01.263_NVSS.fits', 'G006.100-01.263_WISE_12.fits', 'G006.100-01.263_WISE_22.fits', 'G006.100-01.263_WISE_3.4.fits', 'G006.100-01.263_WISE_4.6.fits']
2025-05-20 09:45:53,929 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:45:53,929 - data_manager - INFO - 第一次匹配结果: ['G006.100-01.263_WISE_12.fits']
2025-05-20 09:45:53,929 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.100-01.263\G006.100-01.263_WISE_12.fits
2025-05-20 09:45:54,036 - data_manager - INFO - FITS数据统计: 最小值=25.1376953125, 最大值=10432.1884765625, 均值=1288.1546630859375, 中位数=1195.2789306640625
2025-05-20 09:45:54,037 - data_manager - INFO - 有效数据点数量: 5033955/5035536 (99.97%)
2025-05-20 09:45:54,045 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (2244, 2244)
2025-05-20 09:45:54,048 - data_manager - INFO - 使用12μm波段的WCS作为参考
2025-05-20 09:45:54,048 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (2244, 2244)
2025-05-20 09:45:54,053 - data_manager - INFO - 为源G006.100-01.263加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.100-01.263
2025-05-20 09:45:54,054 - data_manager - INFO - 目录中的所有文件: ['G006.100-01.263_ATLASGAL_870um.fits', 'G006.100-01.263_IRIS_100.fits', 'G006.100-01.263_MIPSGAL_24um.fits', 'G006.100-01.263_NVSS.fits', 'G006.100-01.263_WISE_12.fits', 'G006.100-01.263_WISE_22.fits', 'G006.100-01.263_WISE_3.4.fits', 'G006.100-01.263_WISE_4.6.fits']
2025-05-20 09:45:54,054 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:45:54,054 - data_manager - INFO - 第一次匹配结果: ['G006.100-01.263_WISE_22.fits']
2025-05-20 09:45:54,054 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.100-01.263\G006.100-01.263_WISE_22.fits
2025-05-20 09:45:54,195 - data_manager - INFO - FITS数据统计: 最小值=311.2532653808594, 最大值=2687.279052734375, 均值=337.4683532714844, 中位数=328.2086181640625
2025-05-20 09:45:54,196 - data_manager - INFO - 有效数据点数量: 1475905/1476225 (99.98%)
2025-05-20 09:45:54,204 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (1215, 1215)
2025-05-20 09:45:54,205 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (1215, 1215)
2025-05-20 09:45:54,206 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w3', 'w4']
2025-05-20 09:45:54,281 - region_mapper.ratio - INFO - 调整W4波段数据从(1215, 1215)到(2244, 2244)
2025-05-20 09:45:54,281 - region_mapper.ratio - INFO - 调整W4波段数据从(1215, 1215)到(2244, 2244)
2025-05-20 09:45:55,435 - region_mapper.ratio - INFO - W3/W4比值范围: 2.78-5.86，均值: 3.78
2025-05-20 09:45:55,435 - region_mapper.ratio - INFO - W3/W4比值范围: 2.78-5.86，均值: 3.78
2025-05-20 09:45:55,454 - region_mapper.ratio - INFO - 像素尺度: 504.60 像素/角秒
2025-05-20 09:45:55,454 - region_mapper.ratio - INFO - 像素尺度: 504.60 像素/角秒
2025-05-20 09:45:55,454 - region_mapper.ratio - INFO - 有效半径: 2.9 像素
2025-05-20 09:45:55,454 - region_mapper.ratio - INFO - 有效半径: 2.9 像素
2025-05-20 09:45:55,455 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:45:55,455 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:45:56,276 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.649
2025-05-20 09:45:56,276 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.649
2025-05-20 09:45:56,354 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.566
2025-05-20 09:45:56,354 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.566
2025-05-20 09:45:56,438 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.554
2025-05-20 09:45:56,438 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.554
2025-05-20 09:45:56,438 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 09:45:56,438 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 09:45:56,508 - region_mapper.ratio - INFO - 聚类 0: 平均比值=4.15±0.11, 大小=155, 平均距离=6.6, 紧凑性=5.9
2025-05-20 09:45:56,508 - region_mapper.ratio - INFO - 聚类 0: 平均比值=4.15±0.11, 大小=155, 平均距离=6.6, 紧凑性=5.9
2025-05-20 09:45:56,509 - region_mapper.ratio - INFO - 聚类 1: 平均比值=3.73±0.13, 大小=156, 平均距离=6.7, 紧凑性=6.0
2025-05-20 09:45:56,509 - region_mapper.ratio - INFO - 聚类 1: 平均比值=3.73±0.13, 大小=156, 平均距离=6.7, 紧凑性=6.0
2025-05-20 09:45:56,509 - region_mapper.ratio - INFO - 选择聚类 1 作为PDR区域
2025-05-20 09:45:56,509 - region_mapper.ratio - INFO - 选择聚类 1 作为PDR区域
2025-05-20 09:45:57,100 - region_mapper.ratio - INFO - PDR掩模覆盖160个像素
2025-05-20 09:45:57,100 - region_mapper.ratio - INFO - PDR掩模覆盖160个像素
2025-05-20 09:46:03,344 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G006.100-01.263_w3w4_ratio.png
2025-05-20 09:46:03,344 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G006.100-01.263_w3w4_ratio.png
2025-05-20 09:46:03,352 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖160个像素
2025-05-20 09:46:03,352 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖160个像素
2025-05-20 09:46:03,849 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G006.100-01.263_region_masks.fits
2025-05-20 09:46:03,849 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G006.100-01.263_region_masks.fits
2025-05-20 09:46:03,858 - data_manager - INFO - 为源G006.100-01.263加载Gaia数据，中心坐标: RA=271.073039, Dec=-24.309865, 半径=2.0264度
2025-05-20 09:46:03,860 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances-Data/gaia_data\gaia_G006.100-01.263_61arcmin_2.5R.csv
H:\Augment\Parallax distances\src\data_manager.py:87: DtypeWarning: Columns (47,52,54) have mixed types. Specify dtype option on import or set low_memory=False.
  df = pd.read_csv(gaia_file)
2025-05-20 09:46:18,813 - data_manager - INFO - 成功加载Gaia数据，共964179条记录
2025-05-20 09:46:19,620 - data_manager - INFO - 成功加载Gaia数据，共964179个源在搜索半径内
2025-05-20 09:46:21,840 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.100-01.263_gaia_regions.fits
2025-05-20 09:50:33,565 - data_manager - WARNING - 检测到可能导致保存问题的列: ['_2MASS', 'twomass__2MASS', 'region']
2025-05-20 09:50:33,848 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:50:53,911 - data_manager - ERROR - 保存数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U11'), dtype('<U1'), dtype('<U10'), dtype('<U8'), dtype('<U9')}
2025-05-20 09:50:53,911 - main_single_folder - ERROR - 加载Gaia数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U11'), dtype('<U1'), dtype('<U10'), dtype('<U8'), dtype('<U9')}
2025-05-20 09:50:53,912 - main_single_folder - WARNING - 将使用空的Gaia数据继续处理
2025-05-20 09:50:54,594 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:50:54,594 - main_single_folder - INFO - 步骤3：恒星选择
2025-05-20 09:50:54,599 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-05-20 09:50:54,600 - star_selector - WARNING - 没有有效的视差数据，跳过视差信噪比筛选
2025-05-20 09:50:54,600 - star_selector - WARNING - 没有有效的RUWE数据，跳过RUWE筛选
2025-05-20 09:50:54,600 - star_selector - INFO - 质量筛选详细信息:
2025-05-20 09:50:54,600 - star_selector - INFO -   视差信噪比阈值: 5.0
2025-05-20 09:50:54,600 - star_selector - INFO -   RUWE阈值: 1.4
2025-05-20 09:50:54,601 - star_selector - INFO -   总体通过率: 0.0%
2025-05-20 09:50:54,601 - star_selector - INFO - 质量筛选完成，保留0/0个源
2025-05-20 09:50:54,601 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.100-01.263_filtered_data.fits
2025-05-20 09:50:54,614 - data_manager - INFO - 成功保存数据
2025-05-20 09:50:54,614 - main_single_folder - INFO - 保存质量筛选后的数据到: results/batch_single_folder\processed\G006.100-01.263_filtered_data.fits
2025-05-20 09:50:54,619 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-05-20 09:50:54,619 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-05-20 09:50:54,619 - star_selector - INFO - 筛选恒星样本，原始样本大小: 0
2025-05-20 09:50:54,619 - star_selector - INFO - 排除YSO后: 0/0个源
2025-05-20 09:50:54,620 - star_selector - INFO - 区域 cavity: 0个源
2025-05-20 09:50:54,620 - star_selector - INFO - 区域 pdr: 0个源
2025-05-20 09:50:54,620 - star_selector - INFO - 区域 external: 0个源
2025-05-20 09:50:54,621 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 0
2025-05-20 09:50:54,621 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.100-01.263_star_sample.fits
2025-05-20 09:50:54,648 - data_manager - INFO - 成功保存数据
2025-05-20 09:50:54,648 - main_single_folder - INFO - 恒星选择完成
2025-05-20 09:50:54,648 - main_single_folder - INFO - 步骤4：消光计算
2025-05-20 09:50:54,653 - extinction_estimator - INFO - 为0个恒星计算A_V，参数: {'rv': 3.1}
2025-05-20 09:50:54,653 - extinction_estimator - WARNING - 星表为空，无法计算A_V
2025-05-20 09:50:54,654 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.100-01.263_stars_with_av.fits
2025-05-20 09:50:54,665 - data_manager - INFO - 成功保存数据
2025-05-20 09:50:54,665 - main_single_folder - INFO - 消光计算完成
2025-05-20 09:50:54,666 - main_single_folder - INFO - 步骤5：距离分析
2025-05-20 09:50:54,666 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-05-20 09:50:54,666 - distance_analyzer - INFO - 将分析0个区域: 
2025-05-20 09:50:54,666 - distance_analyzer - INFO - 估计分子云距离
2025-05-20 09:50:54,666 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity, pdr, external
2025-05-20 09:50:54,667 - distance_analyzer - WARNING - 没有检测到任何跳变，无法估计距离
2025-05-20 09:50:54,667 - main_single_folder - INFO - 距离分析完成
2025-05-20 09:50:54,667 - main_single_folder - INFO - 步骤6：可视化和报告生成
2025-05-20 09:50:54,672 - data_manager - INFO - 为源G006.100-01.263加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.100-01.263
2025-05-20 09:50:54,672 - data_manager - INFO - 目录中的所有文件: ['G006.100-01.263_ATLASGAL_870um.fits', 'G006.100-01.263_IRIS_100.fits', 'G006.100-01.263_MIPSGAL_24um.fits', 'G006.100-01.263_NVSS.fits', 'G006.100-01.263_WISE_12.fits', 'G006.100-01.263_WISE_22.fits', 'G006.100-01.263_WISE_3.4.fits', 'G006.100-01.263_WISE_4.6.fits']
2025-05-20 09:50:54,672 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:50:54,672 - data_manager - INFO - 第一次匹配结果: ['G006.100-01.263_WISE_12.fits']
2025-05-20 09:50:54,673 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.100-01.263\G006.100-01.263_WISE_12.fits
2025-05-20 09:50:54,783 - data_manager - INFO - FITS数据统计: 最小值=25.1376953125, 最大值=10432.1884765625, 均值=1288.1546630859375, 中位数=1195.2789306640625
2025-05-20 09:50:54,784 - data_manager - INFO - 有效数据点数量: 5033955/5035536 (99.97%)
2025-05-20 09:50:54,792 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (2244, 2244)
2025-05-20 09:50:54,858 - visualizer - INFO - 绘制WISE图像和区域掩模，输出到: results/batch_single_folder\visualizations\G006.100-01.263_wise_regions_stars.png
2025-05-20 09:50:54,973 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:50:57,145 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:50:57,153 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:51:04,616 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G006.100-01.263_wise_regions_stars.png
2025-05-20 09:51:04,617 - main_single_folder - INFO - 成功保存WISE图像和区域掩模到: results/batch_single_folder\visualizations\G006.100-01.263_wise_regions_stars.png
2025-05-20 09:51:04,617 - main_single_folder - INFO - 加载WISE多波段数据用于RGB图像生成
2025-05-20 09:51:04,617 - data_manager - INFO - 为源G006.100-01.263加载多波段WISE数据: ('3.4', '12', '22')
2025-05-20 09:51:04,622 - data_manager - INFO - 为源G006.100-01.263加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.100-01.263
2025-05-20 09:51:04,622 - data_manager - INFO - 目录中的所有文件: ['G006.100-01.263_ATLASGAL_870um.fits', 'G006.100-01.263_IRIS_100.fits', 'G006.100-01.263_MIPSGAL_24um.fits', 'G006.100-01.263_NVSS.fits', 'G006.100-01.263_WISE_12.fits', 'G006.100-01.263_WISE_22.fits', 'G006.100-01.263_WISE_3.4.fits', 'G006.100-01.263_WISE_4.6.fits']
2025-05-20 09:51:04,622 - data_manager - INFO - 查找WISE 3.4μm波段的FITS文件
2025-05-20 09:51:04,622 - data_manager - INFO - 第一次匹配结果: ['G006.100-01.263_WISE_3.4.fits']
2025-05-20 09:51:04,623 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.100-01.263\G006.100-01.263_WISE_3.4.fits
2025-05-20 09:51:05,008 - data_manager - INFO - FITS数据统计: 最小值=13.160171508789062, 最大值=4909.9228515625, 均值=185.06553649902344, 中位数=128.21849060058594
2025-05-20 09:51:05,008 - data_manager - INFO - 有效数据点数量: 5716821/5716881 (100.00%)
2025-05-20 09:51:05,017 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (2391, 2391)
2025-05-20 09:51:05,020 - data_manager - INFO - 使用3.4μm波段的WCS作为参考
2025-05-20 09:51:05,020 - data_manager - INFO - 成功加载WISE 3.4μm波段数据，尺寸: (2391, 2391)
2025-05-20 09:51:05,025 - data_manager - INFO - 为源G006.100-01.263加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.100-01.263
2025-05-20 09:51:05,025 - data_manager - INFO - 目录中的所有文件: ['G006.100-01.263_ATLASGAL_870um.fits', 'G006.100-01.263_IRIS_100.fits', 'G006.100-01.263_MIPSGAL_24um.fits', 'G006.100-01.263_NVSS.fits', 'G006.100-01.263_WISE_12.fits', 'G006.100-01.263_WISE_22.fits', 'G006.100-01.263_WISE_3.4.fits', 'G006.100-01.263_WISE_4.6.fits']
2025-05-20 09:51:05,026 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:51:05,026 - data_manager - INFO - 第一次匹配结果: ['G006.100-01.263_WISE_12.fits']
2025-05-20 09:51:05,026 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.100-01.263\G006.100-01.263_WISE_12.fits
2025-05-20 09:51:05,133 - data_manager - INFO - FITS数据统计: 最小值=25.1376953125, 最大值=10432.1884765625, 均值=1288.1546630859375, 中位数=1195.2789306640625
2025-05-20 09:51:05,133 - data_manager - INFO - 有效数据点数量: 5033955/5035536 (99.97%)
2025-05-20 09:51:05,142 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (2244, 2244)
2025-05-20 09:51:05,145 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (2244, 2244)
2025-05-20 09:51:05,150 - data_manager - INFO - 为源G006.100-01.263加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.100-01.263
2025-05-20 09:51:05,150 - data_manager - INFO - 目录中的所有文件: ['G006.100-01.263_ATLASGAL_870um.fits', 'G006.100-01.263_IRIS_100.fits', 'G006.100-01.263_MIPSGAL_24um.fits', 'G006.100-01.263_NVSS.fits', 'G006.100-01.263_WISE_12.fits', 'G006.100-01.263_WISE_22.fits', 'G006.100-01.263_WISE_3.4.fits', 'G006.100-01.263_WISE_4.6.fits']
2025-05-20 09:51:05,150 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:51:05,151 - data_manager - INFO - 第一次匹配结果: ['G006.100-01.263_WISE_22.fits']
2025-05-20 09:51:05,151 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.100-01.263\G006.100-01.263_WISE_22.fits
2025-05-20 09:51:05,182 - data_manager - INFO - FITS数据统计: 最小值=311.2532653808594, 最大值=2687.279052734375, 均值=337.4683532714844, 中位数=328.2086181640625
2025-05-20 09:51:05,182 - data_manager - INFO - 有效数据点数量: 1475905/1476225 (99.98%)
2025-05-20 09:51:05,191 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (1215, 1215)
2025-05-20 09:51:05,192 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (1215, 1215)
2025-05-20 09:51:05,192 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w1', 'w3', 'w4']
2025-05-20 09:51:05,193 - visualizer - INFO - 创建WISE三波段RGB图像，输出到: results/batch_single_folder\visualizations\G006.100-01.263_wise_rgb.png
2025-05-20 09:51:05,324 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:51:05,325 - visualizer - INFO - 波段 3.4μm 的尺寸: (2391, 2391)
2025-05-20 09:51:05,325 - visualizer - INFO - 波段 12μm 的尺寸: (2244, 2244)
2025-05-20 09:51:05,325 - visualizer - INFO - 波段 22μm 的尺寸: (1215, 1215)
2025-05-20 09:51:05,325 - visualizer - INFO - Using 12μm band size as target: (2244, 2244)
2025-05-20 09:51:05,325 - visualizer - INFO - Using target image size: (2244, 2244), original sizes: {'w1': (2391, 2391), 'w3': (2244, 2244), 'w4': (1215, 1215)}
2025-05-20 09:51:06,005 - visualizer - INFO - 调整w4波段数据从(1215, 1215)到(2244, 2244)
2025-05-20 09:51:06,034 - visualizer - INFO - 红色通道(22μm)数据范围: 0.007026300765573978-2687.279052734375
2025-05-20 09:51:06,096 - visualizer - INFO - 绿色通道(12μm)数据范围: 25.1376953125-10432.1884765625
2025-05-20 09:51:07,199 - visualizer - INFO - 调整w1波段数据从(2391, 2391)到(2244, 2244)
2025-05-20 09:51:07,226 - visualizer - INFO - 蓝色通道(3.4μm)数据范围: 0.054983485490083694-4697.55517578125
2025-05-20 09:51:07,452 - visualizer - INFO - 使用Lupton RGB方法，stretch=1529.89, Q=10
2025-05-20 09:51:07,453 - visualizer - INFO - 各波段99.5%百分位数: R=571.58, G=3059.78, B=1343.23
2025-05-20 09:51:08,519 - visualizer - INFO - RGB数据形状: (2244, 2244, 3), 类型: float32
2025-05-20 09:51:08,568 - visualizer - INFO - Red通道数据范围：0.0-0.7921568751335144，均值：0.1314
2025-05-20 09:51:08,603 - visualizer - INFO - Green通道数据范围：0.0-1.0，均值：0.4952
2025-05-20 09:51:08,639 - visualizer - INFO - Blue通道数据范围：0.0-0.9529411792755127，均值：0.0661
2025-05-20 09:51:10,681 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:51:10,687 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:51:11,675 - visualizer - INFO - 按照要求不显示Gaia星
2025-05-20 09:51:11,677 - visualizer - INFO - 设置坐标刻度间隔: RA=100.000度, Dec=100.000度
2025-05-20 09:51:16,040 - visualizer - INFO - 成功保存RGB图像到: results/batch_single_folder\visualizations\G006.100-01.263_wise_rgb.png
2025-05-20 09:51:16,053 - main_single_folder - INFO - 成功保存WISE RGB图像到: results/batch_single_folder\visualizations\G006.100-01.263_wise_rgb.png
2025-05-20 09:51:16,056 - main_single_folder - WARNING - 没有足够的数据绘制消光-距离散点图
2025-05-20 09:51:16,073 - main_single_folder - INFO - 成功保存处理报告到: results/batch_single_folder\reports\G006.100-01.263_report.txt
2025-05-20 09:51:16,073 - main_single_folder - INFO - 处理G006.100-01.263完成
