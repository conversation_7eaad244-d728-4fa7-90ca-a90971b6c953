"""
批量测试标准天文流程PDR区域检测方法

从源表中选择10个源进行标准天文流程PDR检测测试，
验证改进的背景估计方法的效果。
"""

import os
import sys
import pandas as pd
import numpy as np
import argparse
from astropy.coordinates import SkyCoord
import astropy.units as u
from skimage import measure
import matplotlib.pyplot as plt

from src.utils.logger import setup_logger
from src.utils.helpers import load_config, ensure_directory
from src.region_mapper.standard_astro_method import define_pdr_mask_standard_astro
from src.data_manager import load_hii_region_catalog

# 设置日志记录器
logger = setup_logger(name='test_batch_standard_astro_pdr')



def extract_coordinates_from_name(source_name):
    """
    从源名称中提取坐标

    Args:
        source_name: 源名称，格式如"GLLL.lll+BB.bbb"

    Returns:
        SkyCoord: 坐标对象
    """
    try:
        # 移除前缀'G'
        coord_str = source_name[1:] if source_name.startswith('G') else source_name

        # 分割经度和纬度
        if '+' in coord_str:
            l_str, b_str = coord_str.split('+')
            b = float(b_str)
        elif '-' in coord_str:
            parts = coord_str.split('-')
            l_str = parts[0]
            b_str = '-'.join(parts[1:])
            b = -float(b_str)
        else:
            raise ValueError(f"无法解析坐标字符串: {coord_str}")

        l = float(l_str)

        return SkyCoord(l=l*u.degree, b=b*u.degree, frame='galactic')

    except Exception as e:
        logger.error(f"从源名称{source_name}提取坐标失败: {str(e)}")
        raise

def test_single_source(source_row, config_path, output_dir):
    """
    测试单个源的PDR检测

    Args:
        source_row: 源表中的一行数据（pandas Series）
        config_path: 配置文件路径
        output_dir: 输出目录

    Returns:
        dict: 测试结果
    """
    # 从源表行中提取信息
    source_name = str(source_row['source_name'])  # 源名称
    r_eff_arcsec = float(source_row['r_eff_arcsec'])  # 有效半径（角秒）
    r_eff_deg = r_eff_arcsec / 3600.0  # 转换为度

    logger.info(f"开始测试源: {source_name}")
    logger.info(f"有效半径: {r_eff_arcsec:.2f} 角秒 = {r_eff_deg:.6f} 度")

    try:
        # 从源名称提取坐标
        center_coord = extract_coordinates_from_name(source_name)

        # 创建源输出目录
        source_output_dir = os.path.join(output_dir, source_name)
        ensure_directory(source_output_dir)

        # 运行标准天文流程PDR检测
        try:
            pdr_mask, cavity_mask, external_mask = define_pdr_mask_standard_astro(
                source_name, None, center_coord, r_eff_deg,
                config_path=config_path,
                output_dir=source_output_dir, save_plots=True
            )
        except Exception as e:
            # 如果可视化失败，尝试不保存图像
            logger.warning(f"带图像保存的PDR检测失败，尝试不保存图像: {str(e)}")
            pdr_mask, cavity_mask, external_mask = define_pdr_mask_standard_astro(
                source_name, None, center_coord, r_eff_deg,
                config_path=config_path,
                output_dir=source_output_dir, save_plots=False
            )

        # 计算统计信息
        pdr_pixels = np.sum(pdr_mask)
        cavity_pixels = np.sum(cavity_mask)
        external_pixels = np.sum(external_mask)
        total_pixels = pdr_pixels + cavity_pixels + external_pixels

        # 计算区域特征
        pdr_regions = measure.regionprops(measure.label(pdr_mask))
        cavity_regions = measure.regionprops(measure.label(cavity_mask))
        external_regions = measure.regionprops(measure.label(external_mask))

        # 验证掩模互斥性
        overlap_pdr_cavity = np.sum(pdr_mask & cavity_mask)
        overlap_pdr_external = np.sum(pdr_mask & external_mask)
        overlap_cavity_external = np.sum(cavity_mask & external_mask)

        result = {
            'source_name': source_name,
            'r_eff_arcsec': r_eff_arcsec,
            'r_eff_deg': r_eff_deg,
            'success': True,
            'pdr_pixels': pdr_pixels,
            'cavity_pixels': cavity_pixels,
            'external_pixels': external_pixels,
            'total_pixels': total_pixels,
            'pdr_regions_count': len(pdr_regions),
            'cavity_regions_count': len(cavity_regions),
            'external_regions_count': len(external_regions),
            'overlap_pdr_cavity': overlap_pdr_cavity,
            'overlap_pdr_external': overlap_pdr_external,
            'overlap_cavity_external': overlap_cavity_external,
            'masks_exclusive': (overlap_pdr_cavity == 0 and
                              overlap_pdr_external == 0 and
                              overlap_cavity_external == 0),
            'error_message': None
        }

        logger.info(f"源{source_name}测试成功:")
        logger.info(f"  PDR: {pdr_pixels} 像素, {len(pdr_regions)} 区域")
        logger.info(f"  Cavity: {cavity_pixels} 像素, {len(cavity_regions)} 区域")
        logger.info(f"  External: {external_pixels} 像素, {len(external_regions)} 区域")
        logger.info(f"  掩模互斥性: {'通过' if result['masks_exclusive'] else '失败'}")

        return result

    except Exception as e:
        logger.error(f"源{source_name}测试失败: {str(e)}")
        return {
            'source_name': source_name,
            'success': False,
            'error_message': str(e)
        }

def create_summary_report(results, output_dir):
    """
    创建测试总结报告

    Args:
        results: 测试结果列表
        output_dir: 输出目录
    """
    try:
        # 创建总结目录
        summary_dir = os.path.join(output_dir, 'summary')
        ensure_directory(summary_dir)

        # 统计成功和失败的源
        successful_sources = [r for r in results if r['success']]
        failed_sources = [r for r in results if not r['success']]

        logger.info(f"测试总结: {len(successful_sources)}/{len(results)} 个源成功")

        # 创建详细报告
        report_path = os.path.join(summary_dir, 'batch_test_report.txt')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("标准天文流程PDR检测批量测试报告\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"测试源数量: {len(results)}\n")
            f.write(f"成功源数量: {len(successful_sources)}\n")
            f.write(f"失败源数量: {len(failed_sources)}\n")
            f.write(f"成功率: {len(successful_sources)/len(results)*100:.1f}%\n\n")

            if successful_sources:
                f.write("成功源统计:\n")
                f.write("-" * 30 + "\n")

                # 计算统计量
                pdr_pixels = [r['pdr_pixels'] for r in successful_sources]
                cavity_pixels = [r['cavity_pixels'] for r in successful_sources]
                external_pixels = [r['external_pixels'] for r in successful_sources]

                f.write(f"PDR像素数: 平均={np.mean(pdr_pixels):.0f}, "
                       f"中位数={np.median(pdr_pixels):.0f}, "
                       f"范围={np.min(pdr_pixels)}-{np.max(pdr_pixels)}\n")

                f.write(f"Cavity像素数: 平均={np.mean(cavity_pixels):.0f}, "
                       f"中位数={np.median(cavity_pixels):.0f}, "
                       f"范围={np.min(cavity_pixels)}-{np.max(cavity_pixels)}\n")

                f.write(f"External像素数: 平均={np.mean(external_pixels):.0f}, "
                       f"中位数={np.median(external_pixels):.0f}, "
                       f"范围={np.min(external_pixels)}-{np.max(external_pixels)}\n\n")

                # 掩模互斥性检查
                exclusive_count = sum(1 for r in successful_sources if r['masks_exclusive'])
                f.write(f"掩模互斥性: {exclusive_count}/{len(successful_sources)} 个源通过\n\n")

                # 详细源信息
                f.write("详细源信息:\n")
                f.write("-" * 30 + "\n")
                for r in successful_sources:
                    f.write(f"{r['source_name']}: PDR={r['pdr_pixels']}, "
                           f"Cavity={r['cavity_pixels']}, External={r['external_pixels']}, "
                           f"互斥={'是' if r['masks_exclusive'] else '否'}\n")

            if failed_sources:
                f.write("\n失败源信息:\n")
                f.write("-" * 30 + "\n")
                for r in failed_sources:
                    f.write(f"{r['source_name']}: {r['error_message']}\n")

        logger.info(f"测试报告已保存到: {report_path}")

        # 创建统计图表
        if successful_sources:
            create_summary_plots(successful_sources, summary_dir)

    except Exception as e:
        logger.error(f"创建总结报告失败: {str(e)}")

def create_summary_plots(successful_results, summary_dir):
    """
    创建统计图表

    Args:
        successful_results: 成功的测试结果
        summary_dir: 总结目录
    """
    try:
        # 提取数据
        source_names = [r['source_name'] for r in successful_results]
        pdr_pixels = [r['pdr_pixels'] for r in successful_results]
        cavity_pixels = [r['cavity_pixels'] for r in successful_results]
        external_pixels = [r['external_pixels'] for r in successful_results]

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 像素数分布
        ax = axes[0, 0]
        x = np.arange(len(source_names))
        width = 0.25
        ax.bar(x - width, pdr_pixels, width, label='PDR', alpha=0.8)
        ax.bar(x, cavity_pixels, width, label='Cavity', alpha=0.8)
        ax.bar(x + width, external_pixels, width, label='External', alpha=0.8)
        ax.set_xlabel('Sources')
        ax.set_ylabel('Pixels')
        ax.set_title('Region Pixel Counts by Source')
        ax.set_xticks(x)
        ax.set_xticklabels(source_names, rotation=45, ha='right')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 像素数分布直方图
        ax = axes[0, 1]
        ax.hist([pdr_pixels, cavity_pixels, external_pixels],
               bins=10, alpha=0.7, label=['PDR', 'Cavity', 'External'])
        ax.set_xlabel('Pixels')
        ax.set_ylabel('Frequency')
        ax.set_title('Pixel Count Distribution')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 区域比例
        ax = axes[1, 0]
        total_pixels = np.array(pdr_pixels) + np.array(cavity_pixels) + np.array(external_pixels)
        pdr_ratio = np.array(pdr_pixels) / total_pixels * 100
        cavity_ratio = np.array(cavity_pixels) / total_pixels * 100
        external_ratio = np.array(external_pixels) / total_pixels * 100

        ax.bar(x, pdr_ratio, width, label='PDR', alpha=0.8)
        ax.bar(x, cavity_ratio, width, bottom=pdr_ratio, label='Cavity', alpha=0.8)
        ax.bar(x, external_ratio, width, bottom=pdr_ratio+cavity_ratio, label='External', alpha=0.8)
        ax.set_xlabel('Sources')
        ax.set_ylabel('Percentage (%)')
        ax.set_title('Region Percentage by Source')
        ax.set_xticks(x)
        ax.set_xticklabels(source_names, rotation=45, ha='right')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 统计总结
        ax = axes[1, 1]
        ax.axis('off')

        stats_text = f"""Batch Test Statistics:

Total Sources: {len(successful_results)}

PDR Pixels:
  Mean: {np.mean(pdr_pixels):.0f}
  Median: {np.median(pdr_pixels):.0f}
  Std: {np.std(pdr_pixels):.0f}

Cavity Pixels:
  Mean: {np.mean(cavity_pixels):.0f}
  Median: {np.median(cavity_pixels):.0f}
  Std: {np.std(cavity_pixels):.0f}

External Pixels:
  Mean: {np.mean(external_pixels):.0f}
  Median: {np.median(external_pixels):.0f}
  Std: {np.std(external_pixels):.0f}
        """

        ax.text(0.05, 0.95, stats_text, transform=ax.transAxes,
               fontsize=10, verticalalignment='top', fontfamily='monospace')

        plt.tight_layout()

        # 保存图表
        plot_path = os.path.join(summary_dir, 'batch_test_summary.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"统计图表已保存到: {plot_path}")

    except Exception as e:
        logger.error(f"创建统计图表失败: {str(e)}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量测试标准天文流程PDR区域检测方法')
    parser.add_argument('--source-table', type=str,
                       default='H:/Augment/Parallax distances/Parallax-based distances.dat',
                       help='源表路径')
    parser.add_argument('--config', type=str, default='config/default_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--output-dir', type=str, default='data/output/batch_test',
                       help='输出目录')
    parser.add_argument('--num-sources', type=int, default=10,
                       help='测试源数量')

    args = parser.parse_args()

    try:
        # 创建输出目录
        ensure_directory(args.output_dir)

        # 加载源表
        logger.info(f"加载源表: {args.source_table}")
        source_df = load_hii_region_catalog(args.source_table, args.config)

        # 选择前N个源进行测试
        test_sources = source_df.head(args.num_sources)
        logger.info(f"选择{len(test_sources)}个源进行测试")

        # 批量测试
        results = []
        for idx, row in test_sources.iterrows():
            result = test_single_source(row, args.config, args.output_dir)
            results.append(result)

        # 创建总结报告
        create_summary_report(results, args.output_dir)

        logger.info("批量测试完成")

    except Exception as e:
        logger.error(f"批量测试失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
