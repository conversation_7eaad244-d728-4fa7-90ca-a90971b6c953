"""
辅助函数模块

提供项目中使用的通用辅助函数。
"""

import os
import yaml
import re
import numpy as np
from astropy.coordinates import SkyCoord
import astropy.units as u

def load_config(config_path='config/default_config.yaml'):
    """
    加载配置文件

    Args:
        config_path: 配置文件路径

    Returns:
        dict: 配置字典
    """
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def ensure_directory(directory):
    """
    确保目录存在，如果不存在则创建

    Args:
        directory: 目录路径
    """
    os.makedirs(directory, exist_ok=True)

def angular_separation(ra1, dec1, ra2, dec2):
    """
    计算两点间的角距离

    Args:
        ra1: 第一个点的赤经（度）
        dec1: 第一个点的赤纬（度）
        ra2: 第二个点的赤经（度）
        dec2: 第二个点的赤纬（度）

    Returns:
        float: 角距离（度）
    """
    coord1 = SkyCoord(ra=ra1*u.degree, dec=dec1*u.degree, frame='icrs')
    coord2 = SkyCoord(ra=ra2*u.degree, dec=dec2*u.degree, frame='icrs')
    return coord1.separation(coord2).degree

def parse_galactic_coords_from_name(source_name):
    """
    从HII区域源名中解析银道坐标

    Args:
        source_name: 源名称（如G000.003+00.127）

    Returns:
        tuple: (银经, 银纬) 单位为度

    Raises:
        ValueError: 如果源名格式不正确
    """
    # 移除前缀'G'
    if not source_name.startswith('G'):
        raise ValueError(f"源名格式不正确: {source_name}，应以'G'开头")

    coords_str = source_name[1:]

    # 使用正则表达式匹配坐标格式
    pattern = r'(\d+\.\d+)([\+\-])(\d+\.\d+)'
    match = re.match(pattern, coords_str)

    if not match:
        raise ValueError(f"无法从源名中解析坐标: {source_name}")

    l = float(match.group(1))
    sign = 1 if match.group(2) == '+' else -1
    b = sign * float(match.group(3))

    return l, b

def parse_galactic_name(source_name):
    """
    从HII区域源名中解析银道坐标（别名函数）

    Args:
        source_name: 源名称（如G000.003+00.127）

    Returns:
        tuple: (银经, 银纬) 单位为度
    """
    return parse_galactic_coords_from_name(source_name)

def galactic_to_icrs(l, b):
    """
    将银道坐标转换为ICRS赤道坐标

    Args:
        l: 银经（度）
        b: 银纬（度）

    Returns:
        tuple: (赤经, 赤纬) 单位为度
    """
    galactic_coord = SkyCoord(l=l*u.degree, b=b*u.degree, frame='galactic')
    icrs_coord = galactic_coord.transform_to('icrs')

    return icrs_coord.ra.degree, icrs_coord.dec.degree

def bin_data(x, y, bin_edges):
    """
    将数据按bin分组并计算每个bin的统计量

    Args:
        x: x轴数据
        y: y轴数据
        bin_edges: bin边界

    Returns:
        tuple: (bin_centers, bin_medians, bin_counts, bin_std)
    """
    indices = np.digitize(x, bin_edges)
    bin_centers = (bin_edges[1:] + bin_edges[:-1]) / 2
    bin_medians = []
    bin_counts = []
    bin_std = []

    for i in range(1, len(bin_edges)):
        mask = indices == i
        if np.sum(mask) > 0:
            bin_medians.append(np.median(y[mask]))
            bin_counts.append(np.sum(mask))
            bin_std.append(np.std(y[mask]))
        else:
            bin_medians.append(np.nan)
            bin_counts.append(0)
            bin_std.append(np.nan)

    return bin_centers, np.array(bin_medians), np.array(bin_counts), np.array(bin_std)
