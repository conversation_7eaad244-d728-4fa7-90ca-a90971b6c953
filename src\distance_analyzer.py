"""
距离分析模块

负责分析消光-距离关系，识别消光跳变，并估计分子云距离。
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats, signal
from astropy.table import Table, Column

from src.utils.logger import setup_logger
from src.utils.helpers import load_config, bin_data
from src.utils.plotting import plot_extinction_distance, plot_comparison_jumps

# 设置日志记录器
logger = setup_logger(name='distance_analyzer')

def prepare_distance_extinction_data(star_table, region=None):
    """
    准备距离-消光数据

    Args:
        star_table: 恒星表 (astropy.table.Table)
        region: 区域名称，如果不为None则只选择该区域的源

    Returns:
        tuple: (distances, extinctions, mask)
    """
    logger.info(f"准备距离-消光数据，区域: {region if region else '全部'}")

    try:
        # 检查必要的列
        required_cols = ['distance', 'a_v']
        if not all(col in star_table.colnames for col in required_cols):
            missing = [col for col in required_cols if col not in star_table.colnames]
            logger.error(f"表中缺少必要的列: {missing}")
            raise ValueError(f"表中缺少必要的列: {missing}")

        # 检查表是否为空
        if len(star_table) == 0:
            logger.warning("星表为空")
            return np.array([]), np.array([]), np.array([], dtype=bool)

        # 提取距离和消光数据
        distances = np.array(star_table['distance'])
        extinctions = np.array(star_table['a_v'])

        # 创建有效数据掩模
        valid_mask = ~np.isnan(distances) & ~np.isnan(extinctions) & (distances > 0)

        # 如果指定了区域，只选择该区域的源
        if region and 'region' in star_table.colnames:
            region_mask = np.array(star_table['region']) == region
            valid_mask &= region_mask
            logger.info(f"区域{region}中有{np.sum(valid_mask)}个有效源")
        else:
            logger.info(f"共有{np.sum(valid_mask)}个有效源")

        # 如果没有有效数据，返回空数组
        if np.sum(valid_mask) == 0:
            logger.warning("没有有效的距离-消光数据")
            return np.array([]), np.array([]), np.array([], dtype=bool)

        return distances, extinctions, valid_mask

    except Exception as e:
        logger.error(f"准备距离-消光数据失败: {str(e)}")
        raise

def compute_running_statistics(distances, extinctions, bin_size=50, min_stars_per_bin=5):
    """
    计算消光的滑动统计量

    Args:
        distances: 距离数组
        extinctions: 消光数组
        bin_size: bin大小（单位：pc）
        min_stars_per_bin: 每个bin的最小恒星数

    Returns:
        tuple: (bin_centers, bin_medians, bin_counts, bin_std)
    """
    logger.info(f"计算消光的滑动统计量，bin大小={bin_size}pc，最小恒星数={min_stars_per_bin}")

    try:
        # 检查数据是否为空
        if len(distances) == 0 or len(extinctions) == 0:
            logger.warning("距离或消光数据为空")
            return np.array([]), np.array([]), np.array([]), np.array([])

        # 排序数据
        sort_idx = np.argsort(distances)
        sorted_distances = distances[sort_idx]
        sorted_extinctions = extinctions[sort_idx]

        # 确定bin边界
        min_dist = np.min(sorted_distances)
        max_dist = np.max(sorted_distances)

        # 如果只有一个数据点，无法计算统计量
        if min_dist == max_dist:
            logger.warning("所有距离值相同，无法计算统计量")
            return np.array([min_dist]), np.array([np.median(sorted_extinctions)]), np.array([len(sorted_distances)]), np.array([0.0])

        bin_edges = np.arange(min_dist, max_dist + bin_size, bin_size)

        # 计算每个bin的统计量
        bin_centers, bin_medians, bin_counts, bin_std = bin_data(
            sorted_distances, sorted_extinctions, bin_edges
        )

        # 筛选有足够恒星的bin
        valid_bins = bin_counts >= min_stars_per_bin

        if np.sum(valid_bins) == 0:
            logger.warning(f"没有bin包含足够的恒星（>={min_stars_per_bin}）")
            return bin_centers, bin_medians, bin_counts, bin_std

        logger.info(f"计算了{len(bin_centers)}个bin的统计量，其中{np.sum(valid_bins)}个有效")
        return bin_centers[valid_bins], bin_medians[valid_bins], bin_counts[valid_bins], bin_std[valid_bins]

    except Exception as e:
        logger.error(f"计算滑动统计量失败: {str(e)}")
        raise

def detect_extinction_jumps(bin_centers, bin_medians, min_jump_height=0.5, min_jump_significance=2.0):
    """
    检测消光跳变

    Args:
        bin_centers: bin中心位置
        bin_medians: 每个bin的中位数
        min_jump_height: 最小跳变高度（单位：mag）
        min_jump_significance: 最小跳变显著性

    Returns:
        list: 跳变信息列表，每个元素是一个字典
    """
    logger.info(f"检测消光跳变，最小跳变高度={min_jump_height}mag，最小显著性={min_jump_significance}")

    try:
        # 检查数据是否为空
        if len(bin_centers) == 0 or len(bin_medians) == 0:
            logger.warning("bin中心或中位数数据为空，无法检测跳变")
            return []

        # 确保数据有效
        valid_mask = ~np.isnan(bin_medians)
        if np.sum(valid_mask) < 3:
            logger.warning("有效数据点太少，无法检测跳变")
            return []

        valid_centers = bin_centers[valid_mask]
        valid_medians = bin_medians[valid_mask]

        # 计算一阶差分
        diff = np.diff(valid_medians)

        # 如果差分为空，返回空列表
        if len(diff) == 0:
            logger.warning("差分为空，无法检测跳变")
            return []

        diff_distances = (valid_centers[1:] + valid_centers[:-1]) / 2

        # 计算差分的标准差
        diff_std = np.std(diff)

        # 如果标准差为0，无法检测显著跳变
        if diff_std == 0:
            logger.warning("差分标准差为0，无法检测显著跳变")
            return []

        # 识别显著的正跳变
        jump_indices = np.where((diff > min_jump_height) &
                               (diff > min_jump_significance * diff_std))[0]

        # 收集跳变信息
        jumps = []
        for idx in jump_indices:
            jump_info = {
                'distance': diff_distances[idx],
                'height': diff[idx],
                'significance': diff[idx] / diff_std,
                'before_av': valid_medians[idx],
                'after_av': valid_medians[idx + 1]
            }
            jumps.append(jump_info)

        # 记录详细的跳变检测信息
        logger.info(f"跳变检测统计:")
        logger.info(f"  差分标准差: {diff_std:.4f}")
        logger.info(f"  最小跳变高度阈值: {min_jump_height:.4f}")
        logger.info(f"  最小显著性阈值: {min_jump_significance:.2f}")
        logger.info(f"  检测到的跳变数量: {len(jumps)}")

        # 如果有跳变，按距离排序并记录详细信息
        if jumps:
            # 按距离排序
            jumps.sort(key=lambda j: j['distance'])

            logger.info(f"跳变详细信息:")
            for i, jump in enumerate(jumps):
                logger.info(f"  跳变 {i+1}: 距离={jump['distance']:.1f}pc, "
                           f"高度={jump['height']:.2f}mag, "
                           f"显著性={jump['significance']:.1f}, "
                           f"前消光={jump['before_av']:.2f}mag, "
                           f"后消光={jump['after_av']:.2f}mag")

            # 记录最显著的跳变
            most_significant = max(jumps, key=lambda j: j['significance'])
            logger.info(f"  最显著的跳变: 距离={most_significant['distance']:.1f}pc, "
                       f"高度={most_significant['height']:.2f}mag, "
                       f"显著性={most_significant['significance']:.1f}")
        else:
            logger.info("  未检测到显著跳变")

        logger.info(f"共检测到{len(jumps)}个消光跳变")
        return jumps

    except Exception as e:
        logger.error(f"检测消光跳变失败: {str(e)}")
        raise

def analyze_region_extinction(star_table, region, config_path='config/default_config.yaml',
                             output_dir=None):
    """
    分析特定区域的消光-距离关系

    Args:
        star_table: 恒星表 (astropy.table.Table)
        region: 区域名称
        config_path: 配置文件路径
        output_dir: 输出目录

    Returns:
        dict: 分析结果
    """
    config = load_config(config_path)
    analysis_params = config['processing']['distance_analysis']

    logger.info(f"分析{region}区域的消光-距离关系")

    try:
        # 准备数据
        distances, extinctions, valid_mask = prepare_distance_extinction_data(star_table, region)

        # 记录详细的统计信息
        total_stars = len(star_table)
        region_stars = np.sum(star_table['region'] == region) if 'region' in star_table.colnames else 0
        valid_stars = np.sum(valid_mask)

        logger.info(f"{region}区域统计信息:")
        logger.info(f"  总恒星数: {total_stars}")
        logger.info(f"  区域内恒星数: {region_stars} ({region_stars/total_stars*100:.1f}% 的总数)")
        logger.info(f"  有效数据点: {valid_stars} ({valid_stars/region_stars*100:.1f}% 的区域内恒星)")

        if np.sum(valid_mask) < 10:
            logger.warning(f"{region}区域有效数据点太少（{np.sum(valid_mask)} < 10），跳过分析")
            return {
                'region': region,
                'valid_count': np.sum(valid_mask),
                'distances': None,
                'extinctions': None,
                'bin_centers': None,
                'bin_medians': None,
                'bin_counts': None,
                'bin_std': None,
                'jumps': []
            }

        valid_distances = distances[valid_mask]
        valid_extinctions = extinctions[valid_mask]

        # 计算滑动统计量
        bin_centers, bin_medians, bin_counts, bin_std = compute_running_statistics(
            valid_distances, valid_extinctions,
            bin_size=analysis_params['bin_size'],
            min_stars_per_bin=analysis_params['min_stars_per_bin']
        )

        # 检测消光跳变
        jumps = detect_extinction_jumps(bin_centers, bin_medians)

        # 绘制消光-距离图
        if output_dir:
            plot_path = plot_extinction_distance(
                valid_distances, valid_extinctions, region,
                bin_centers, bin_medians, bin_std, output_dir
            )
            logger.info(f"保存消光-距离图到: {plot_path}")

        # 返回结果
        result = {
            'region': region,
            'valid_count': np.sum(valid_mask),
            'distances': valid_distances,
            'extinctions': valid_extinctions,
            'bin_centers': bin_centers,
            'bin_medians': bin_medians,
            'bin_counts': bin_counts,
            'bin_std': bin_std,
            'jumps': jumps
        }

        logger.info(f"{region}区域分析完成，检测到{len(jumps)}个跳变")
        return result

    except Exception as e:
        logger.error(f"分析{region}区域失败: {str(e)}")
        raise

def analyze_all_regions(star_table, config_path='config/default_config.yaml', output_dir=None):
    """
    分析所有区域的消光-距离关系

    Args:
        star_table: 恒星表 (astropy.table.Table)
        config_path: 配置文件路径
        output_dir: 输出目录

    Returns:
        dict: 所有区域的分析结果
    """
    logger.info("分析所有区域的消光-距离关系")

    try:
        # 检查区域列
        if 'region' not in star_table.colnames:
            logger.error("表中缺少region列")
            raise ValueError("表中缺少region列")

        # 获取所有区域
        regions = np.unique(star_table['region'])
        valid_regions = [r for r in regions if r in ['cavity', 'pdr', 'external']]

        logger.info(f"将分析{len(valid_regions)}个区域: {', '.join(valid_regions)}")

        # 分析每个区域
        results = {}
        for region in valid_regions:
            results[region] = analyze_region_extinction(
                star_table, region, config_path, output_dir
            )

        # 绘制比较图
        if output_dir and len(valid_regions) > 1:
            plot_path = plot_comparison_jumps(results, output_dir)
            logger.info(f"保存区域比较图到: {plot_path}")

        return results

    except Exception as e:
        logger.error(f"分析所有区域失败: {str(e)}")
        raise

def estimate_cloud_distance(region_results):
    """
    估计分子云距离

    Args:
        region_results: 区域分析结果字典

    Returns:
        dict: 距离估计结果
    """
    logger.info("估计分子云距离")

    try:
        # 检查是否有足够的区域数据
        required_regions = ['cavity', 'pdr', 'external']
        missing_regions = [r for r in required_regions if r not in region_results]

        if missing_regions:
            logger.warning(f"缺少必要的区域数据: {', '.join(missing_regions)}")

        # 收集所有区域的跳变
        all_jumps = {}
        for region, result in region_results.items():
            if result['jumps']:
                all_jumps[region] = result['jumps']

        if not all_jumps:
            logger.warning("没有检测到任何跳变，无法估计距离")
            return {
                'cloud_distance': None,
                'distance_error': None,
                'confidence': 'none',
                'evidence': 'No jumps detected in any region'
            }

        # 寻找符合目标云特征的跳变
        # 目标云特征：PDR和External区域有大的跳变，Cavity区域跳变较小
        candidate_distances = []
        evidence = []

        # 检查每个PDR跳变
        if 'pdr' in all_jumps:
            for pdr_jump in all_jumps['pdr']:
                pdr_distance = pdr_jump['distance']
                pdr_height = pdr_jump['height']

                # 在相似距离处寻找External区域的跳变
                ext_jump = None
                if 'external' in all_jumps:
                    for jump in all_jumps['external']:
                        if abs(jump['distance'] - pdr_distance) < 100:  # 100pc阈值
                            ext_jump = jump
                            break

                # 在相似距离处寻找Cavity区域的跳变
                cav_jump = None
                if 'cavity' in all_jumps:
                    for jump in all_jumps['cavity']:
                        if abs(jump['distance'] - pdr_distance) < 100:  # 100pc阈值
                            cav_jump = jump
                            break

                # 检查是否符合目标云特征
                if ext_jump:
                    # 如果PDR和External都有跳变
                    if cav_jump:
                        # 如果Cavity也有跳变，检查是否明显小于PDR
                        if cav_jump['height'] < 0.5 * pdr_jump['height']:
                            # 符合目标云特征
                            candidate_distances.append(pdr_distance)
                            evidence.append(
                                f"Jump at {pdr_distance:.0f}pc: "
                                f"PDR ΔA_V={pdr_height:.2f}, "
                                f"External ΔA_V={ext_jump['height']:.2f}, "
                                f"Cavity ΔA_V={cav_jump['height']:.2f} (significantly smaller)"
                            )
                    else:
                        # 如果Cavity没有跳变，也符合目标云特征
                        candidate_distances.append(pdr_distance)
                        evidence.append(
                            f"Jump at {pdr_distance:.0f}pc: "
                            f"PDR ΔA_V={pdr_height:.2f}, "
                            f"External ΔA_V={ext_jump['height']:.2f}, "
                            f"No jump in Cavity"
                        )

        # 如果没有找到符合特征的跳变，尝试使用PDR区域的最显著跳变
        if not candidate_distances and 'pdr' in all_jumps:
            # 选择PDR区域中最显著的跳变
            pdr_jumps = all_jumps['pdr']
            if pdr_jumps:
                best_jump = max(pdr_jumps, key=lambda j: j['significance'])
                candidate_distances.append(best_jump['distance'])
                evidence.append(
                    f"Using most significant PDR jump at {best_jump['distance']:.0f}pc "
                    f"(ΔA_V={best_jump['height']:.2f}, significance={best_jump['significance']:.1f})"
                )

        # 如果仍然没有候选距离，返回无结果
        if not candidate_distances:
            logger.warning("没有找到符合目标云特征的跳变")
            return {
                'cloud_distance': None,
                'distance_error': None,
                'confidence': 'none',
                'evidence': 'No jumps matching target cloud characteristics'
            }

        # 计算最终距离和不确定度
        cloud_distance = np.median(candidate_distances)

        if len(candidate_distances) > 1:
            distance_error = np.std(candidate_distances)
            confidence = 'high' if len(candidate_distances) >= 3 else 'medium'
        else:
            # 单个候选距离，使用bin大小作为不确定度
            distance_error = 50  # 默认bin大小
            confidence = 'low'

        logger.info(f"估计的分子云距离: {cloud_distance:.0f} ± {distance_error:.0f} pc, "
                   f"置信度: {confidence}")

        for e in evidence:
            logger.info(f"证据: {e}")

        return {
            'cloud_distance': cloud_distance,
            'distance_error': distance_error,
            'confidence': confidence,
            'evidence': evidence
        }

    except Exception as e:
        logger.error(f"估计分子云距离失败: {str(e)}")
        raise

def save_analysis_results(results, source_name, output_dir=None, config_path='config/default_config.yaml'):
    """
    保存分析结果

    Args:
        results: 分析结果字典
        source_name: 源名称
        output_dir: 输出目录，如果为None则从配置文件读取
        config_path: 配置文件路径

    Returns:
        str: 保存的文件路径
    """
    if output_dir is None:
        config = load_config(config_path)
        output_dir = config['output_paths']['results']

    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, f"{source_name}_results.txt")

    logger.info(f"保存分析结果到: {output_path}")

    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f"分析结果: {source_name}\n")
            f.write("="*50 + "\n\n")

            # 写入云距离估计
            if 'cloud_distance' in results and results['cloud_distance'] is not None:
                f.write(f"分子云距离估计: {results['cloud_distance']:.0f} ± {results['distance_error']:.0f} pc\n")
                f.write(f"置信度: {results['confidence']}\n\n")

                f.write("证据:\n")
                for e in results['evidence']:
                    f.write(f"- {e}\n")
                f.write("\n")
            else:
                f.write("未能估计分子云距离\n\n")

            # 写入各区域的跳变信息
            f.write("各区域跳变信息:\n")
            for region in ['cavity', 'pdr', 'external']:
                if region in results['region_results']:
                    region_result = results['region_results'][region]
                    f.write(f"\n{region.upper()} 区域:\n")
                    f.write(f"- 有效恒星数: {region_result['valid_count']}\n")

                    if region_result['jumps']:
                        f.write(f"- 检测到的跳变:\n")
                        for i, jump in enumerate(region_result['jumps']):
                            f.write(f"  {i+1}. 距离: {jump['distance']:.0f} pc, "
                                   f"高度: {jump['height']:.2f} mag, "
                                   f"显著性: {jump['significance']:.1f}\n")
                    else:
                        f.write("- 未检测到显著跳变\n")

            f.write("\n" + "="*50 + "\n")
            f.write(f"分析完成时间: {os.path.basename(output_path).split('_')[0]}\n")

        logger.info(f"成功保存分析结果")
        return output_path

    except Exception as e:
        logger.error(f"保存分析结果失败: {str(e)}")
        raise
