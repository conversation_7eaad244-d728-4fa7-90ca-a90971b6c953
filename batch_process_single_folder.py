"""
批量处理HII区源到单一文件夹

处理多个HII区源，将所有输出（图像和报告）保存在同一个文件夹中。
所有图像标注使用英文以避免乱码问题。
"""

import os
import sys
import subprocess
import pandas as pd
import time
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_process_single_folder.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('batch_process_single_folder')

def load_hii_region_catalog(catalog_path):
    """
    加载HII区源表

    Args:
        catalog_path: 源表路径

    Returns:
        pandas.DataFrame: 源表数据
    """
    try:
        # 尝试读取源表
        with open(catalog_path, 'r') as f:
            lines = f.readlines()

        # 手动解析源表
        data = []
        for line in lines:
            if line.strip() and not line.startswith('#'):
                parts = line.strip().split()
                if len(parts) >= 3:
                    source_name = parts[0]  # 源名称
                    quality = parts[1]      # 质量标记 (K/Q)
                    try:
                        N = float(parts[2])  # 有效半径（角秒）
                        distance = float(parts[3])  # 距离（kpc）
                        distance_error_min = float(parts[4]) if len(parts) > 4 else 0.0  # 距离误差下限
                        distance_error_max = float(parts[5]) if len(parts) > 5 else 0.0  # 距离误差上限
                        data.append([source_name, quality, N, distance, distance_error_min, distance_error_max])
                    except ValueError:
                        continue

        # 创建DataFrame
        catalog = pd.DataFrame(data, columns=['source_name', 'quality', 'N', 'distance',
                                             'distance_error_min', 'distance_error_max'])
        logger.info(f"成功加载源表，共{len(catalog)}个源")
        return catalog
    except Exception as e:
        logger.error(f"加载源表失败: {str(e)}")
        raise

def process_source(source_name, output_dir, config_path=None):
    """
    处理单个HII区源

    Args:
        source_name: 源名称
        output_dir: 输出目录
        config_path: 配置文件路径

    Returns:
        bool: 处理是否成功
    """
    try:
        # 构建命令 - 使用main_single_folder.py而不是main.py
        cmd = [sys.executable, 'main_single_folder.py', '--source', source_name, '--output-dir', output_dir]

        if config_path:
            cmd.extend(['--config', config_path])

        # 记录开始时间
        start_time = time.time()

        # 执行命令
        logger.info(f"开始处理源 {source_name}")
        logger.info(f"执行命令: {' '.join(cmd)}")

        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        stdout, stderr = process.communicate()

        # 记录结束时间
        end_time = time.time()
        duration = end_time - start_time

        # 检查是否成功
        if process.returncode == 0:
            logger.info(f"源 {source_name} 处理成功，耗时: {duration:.2f}秒")

            # 保存输出日志
            log_file = os.path.join(output_dir, f"{source_name}_process_log.txt")
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"耗时: {duration:.2f}秒\n\n")
                f.write("标准输出:\n")
                f.write(stdout)
                f.write("\n\n标准错误:\n")
                f.write(stderr)

            return True
        else:
            logger.error(f"源 {source_name} 处理失败，返回码: {process.returncode}")
            logger.error(f"错误信息: {stderr}")

            # 保存错误日志
            error_log_file = os.path.join(output_dir, f"{source_name}_error_log.txt")
            with open(error_log_file, 'w', encoding='utf-8') as f:
                f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"耗时: {duration:.2f}秒\n\n")
                f.write(f"返回码: {process.returncode}\n\n")
                f.write("标准输出:\n")
                f.write(stdout)
                f.write("\n\n标准错误:\n")
                f.write(stderr)

            return False

    except Exception as e:
        logger.error(f"处理源 {source_name} 时发生异常: {str(e)}")
        return False

def batch_process_single_folder(catalog_path, num_sources=20, output_dir='results/batch_single_folder', config_path=None):
    """
    批量处理HII区源，将所有输出保存在同一个文件夹中

    Args:
        catalog_path: 源表路径
        num_sources: 要处理的源数量
        output_dir: 输出目录
        config_path: 配置文件路径

    Returns:
        tuple: (成功数量, 失败数量)
    """
    # 加载源表
    catalog = load_hii_region_catalog(catalog_path)

    # 限制处理的源数量
    sources_to_process = catalog.iloc[:num_sources]

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 创建子目录
    processed_dir = os.path.join(output_dir, 'processed')
    vis_dir = os.path.join(output_dir, 'visualizations')
    reports_dir = os.path.join(output_dir, 'reports')

    os.makedirs(processed_dir, exist_ok=True)
    os.makedirs(vis_dir, exist_ok=True)
    os.makedirs(reports_dir, exist_ok=True)

    # 处理结果统计
    success_count = 0
    failed_count = 0
    failed_sources = []

    # 批量处理
    logger.info(f"开始批量处理{len(sources_to_process)}个源，所有输出将保存在同一个文件夹中")

    # 记录开始时间
    batch_start_time = time.time()

    for i, (_, row) in enumerate(sources_to_process.iterrows()):
        source_name = row['source_name'].strip()
        logger.info(f"处理源 {i+1}/{len(sources_to_process)}: {source_name}")

        # 处理源
        success = process_source(source_name, output_dir, config_path)

        if success:
            success_count += 1
        else:
            failed_count += 1
            failed_sources.append(source_name)

    # 记录结束时间
    batch_end_time = time.time()
    batch_duration = batch_end_time - batch_start_time

    # 输出处理结果
    logger.info(f"批量处理完成: 成功 {success_count}, 失败 {failed_count}")
    logger.info(f"总耗时: {batch_duration:.2f}秒")

    if failed_sources:
        logger.info(f"失败的源: {', '.join(failed_sources)}")

    # 保存汇总结果
    summary_file = os.path.join(output_dir, "batch_summary.txt")
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(f"批处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总耗时: {batch_duration:.2f}秒\n")
        f.write(f"成功: {success_count}/{len(sources_to_process)}\n\n")

        f.write("处理结果:\n")
        for i, (_, row) in enumerate(sources_to_process.iterrows()):
            source_name = row['source_name'].strip()
            success_status = "成功" if source_name not in failed_sources else "失败"
            f.write(f"- {source_name}: {success_status}\n")

        if failed_sources:
            f.write("\n失败的源:\n")
            for name in failed_sources:
                f.write(f"- {name}\n")

    return success_count, failed_count

def parse_arguments():
    """
    解析命令行参数

    Returns:
        argparse.Namespace: 解析后的参数
    """
    import argparse
    parser = argparse.ArgumentParser(description='批量处理HII区源到单一文件夹')

    parser.add_argument('--catalog', type=str,
                       default='H:/Augment/Parallax distances/Parallax-based distances.dat',
                       help='HII区源表路径')

    parser.add_argument('--num-sources', type=int, default=20,
                       help='要处理的源数量')

    parser.add_argument('--output-dir', type=str, default='results/batch_single_folder',
                       help='输出目录')

    parser.add_argument('--config', type=str, default='config/default_config.yaml',
                       help='配置文件路径')

    return parser.parse_args()

if __name__ == '__main__':
    # 解析命令行参数
    args = parse_arguments()

    # 批量处理
    success_count, failed_count = batch_process_single_folder(
        args.catalog, args.num_sources, args.output_dir, args.config
    )

    # 输出处理结果
    print(f"批量处理完成: 成功 {success_count}, 失败 {failed_count}")

    # 设置退出码
    sys.exit(0 if failed_count == 0 else 1)
