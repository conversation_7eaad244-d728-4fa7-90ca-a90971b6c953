处理时间: 2025-04-24 04:51:26
耗时: 4.12秒

标准输出:

未能估计G000.519-00.051的分子云距离


标准错误:
2025-04-24 04:51:25,845 - main - INFO - 输出目录: data/output/batch_results\G000.519-00.051
2025-04-24 04:51:25,846 - main - INFO - 加载源G000.519-00.051的信息
2025-04-24 04:51:25,846 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
H:\Augment\Parallax distances\src\data_manager.py:43: FutureWarning: The 'delim_whitespace' keyword in pd.read_csv is deprecated and will be removed in a future version. Use ``sep='\s+'`` instead
  df = pd.read_csv(catalog_path, delim_whitespace=True, comment='#', header=None, names=column_names)
2025-04-24 04:51:25,852 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-04-24 04:51:25,853 - main - INFO - 目录中的所有文件: ['G000.519-00.051_ATLASGAL_870um.fits', 'G000.519-00.051_IRIS_100.fits', 'G000.519-00.051_NVSS.fits', 'G000.519-00.051_WISE_12.fits', 'G000.519-00.051_WISE_22.fits', 'G000.519-00.051_WISE_3.4.fits', 'G000.519-00.051_WISE_4.6.fits']
2025-04-24 04:51:25,853 - main - INFO - 第一次匹配结果: ['G000.519-00.051_WISE_12.fits']
2025-04-24 04:51:25,853 - main - INFO - 从FITS文件获取坐标: U:/Data/Bubbles/Wise bubbles\G000.519-00.051\G000.519-00.051_WISE_12.fits
2025-04-24 04:51:25,889 - main - INFO - 从FITS头信息获取坐标系统: fk5
2025-04-24 04:51:25,893 - main - INFO - 从FITS头信息获取坐标: RA=266.762992, Dec=-28.518405 (ICRS)
2025-04-24 04:51:25,893 - main - INFO - 使用有效半径208.0角秒 (0.057778度)
2025-04-24 04:51:25,893 - main - INFO - 从源表加载信息: RA=266.762992, Dec=-28.518405, R_eff=0.057778度
2025-04-24 04:51:25,894 - main - INFO - 步骤1：加载WISE数据
2025-04-24 04:51:25,898 - data_manager - INFO - 为源G000.519-00.051加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.519-00.051
2025-04-24 04:51:25,898 - data_manager - INFO - 目录中的所有文件: ['G000.519-00.051_ATLASGAL_870um.fits', 'G000.519-00.051_IRIS_100.fits', 'G000.519-00.051_NVSS.fits', 'G000.519-00.051_WISE_12.fits', 'G000.519-00.051_WISE_22.fits', 'G000.519-00.051_WISE_3.4.fits', 'G000.519-00.051_WISE_4.6.fits']
2025-04-24 04:51:25,898 - data_manager - INFO - 第一次匹配结果: ['G000.519-00.051_WISE_12.fits']
2025-04-24 04:51:25,898 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.519-00.051\G000.519-00.051_WISE_12.fits
2025-04-24 04:51:25,908 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (320, 320)
2025-04-24 04:51:25,908 - main - INFO - WISE数据加载完成
2025-04-24 04:51:25,908 - main - INFO - 步骤2：区域映射
2025-04-24 04:51:25,908 - region_mapper - INFO - 处理WISE图像，平滑sigma=1.0
2025-04-24 04:51:25,909 - region_mapper - INFO - 替换图像中的NaN值
2025-04-24 04:51:25,910 - region_mapper - INFO - 应用高斯平滑
2025-04-24 04:51:25,913 - region_mapper - INFO - 估计并减除背景
2025-04-24 04:51:25,926 - region_mapper - INFO - WISE图像处理完成
2025-04-24 04:51:25,926 - region_mapper - INFO - 定义PDR掩模，阈值因子=0.5，最大半径因子=3.0
2025-04-24 04:51:25,926 - region_mapper - INFO - WCS坐标系统: fk5
2025-04-24 04:51:25,926 - region_mapper - INFO - 将中心坐标从ICRS转换为fk5
2025-04-24 04:51:25,932 - region_mapper - INFO - 中心坐标 (RA=266.762992, Dec=-28.518405) 对应像素坐标 (X=159.5, Y=159.5)
2025-04-24 04:51:25,936 - region_mapper - INFO - 像素尺度: 486.64 像素/角秒
2025-04-24 04:51:25,936 - region_mapper - INFO - 最大搜索半径: 1.3 像素
2025-04-24 04:51:25,937 - region_mapper - INFO - PDR阈值: 1808.98 (峰值的50%)
2025-04-24 04:51:25,963 - region_mapper - INFO - PDR掩模创建完成，覆盖2个像素
2025-04-24 04:51:25,964 - region_mapper - INFO - 定义空腔掩模，有效半径=0.057777777777777775度
2025-04-24 04:51:25,971 - region_mapper - INFO - 有效半径: 0.4 像素
2025-04-24 04:51:25,972 - region_mapper - INFO - 空腔掩模创建完成，覆盖0个像素
2025-04-24 04:51:25,973 - region_mapper - INFO - 定义外部区域掩模，最大半径因子=5.0
2025-04-24 04:51:25,979 - region_mapper - INFO - 最大半径: 2.1 像素
2025-04-24 04:51:25,980 - region_mapper - INFO - 外部区域掩模创建完成，覆盖14个像素
2025-04-24 04:51:25,981 - region_mapper - INFO - 保存区域掩模到: data/output/batch_results\G000.519-00.051\processed\G000.519-00.051_region_masks.npz
2025-04-24 04:51:25,985 - region_mapper - INFO - 成功保存区域掩模
2025-04-24 04:51:25,985 - main - INFO - 区域映射完成
2025-04-24 04:51:25,985 - main - INFO - 由于缺少Gaia数据，跳过恒星选择、消光计算和距离分析步骤
2025-04-24 04:51:25,985 - main - INFO - 处理G000.519-00.051完成
