处理时间: 2025-05-20 09:39:02
耗时: 15.53秒

标准输出:

未能估计G000.484-00.900的分子云距离


标准错误:
2025-05-20 09:38:50,928 - main_single_folder - INFO - 输出目录: results/batch_single_folder
2025-05-20 09:38:50,929 - main_single_folder - INFO - 加载源G000.484-00.900的信息
2025-05-20 09:38:50,929 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
2025-05-20 09:38:50,935 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-05-20 09:38:50,936 - main_single_folder - INFO - 从源名解析银道坐标: l=0.484000, b=-0.900000
2025-05-20 09:38:50,942 - main_single_folder - INFO - 银道坐标转换为赤道坐标: RA=267.571483, Dec=-28.987003 (ICRS)
2025-05-20 09:38:50,942 - main_single_folder - INFO - 从源表加载信息: RA=267.571483, Dec=-28.987003, R_eff=0.007500度
2025-05-20 09:38:50,944 - main_single_folder - INFO - 步骤1：加载WISE数据
2025-05-20 09:38:50,948 - data_manager - INFO - 为源G000.484-00.900加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.484-00.900
2025-05-20 09:38:50,949 - data_manager - INFO - 目录中的所有文件: ['G000.484-00.900_ATLASGAL_870um.fits', 'G000.484-00.900_IRIS_100.fits', 'G000.484-00.900_NVSS.fits', 'G000.484-00.900_WISE_12.fits', 'G000.484-00.900_WISE_22.fits', 'G000.484-00.900_WISE_3.4.fits', 'G000.484-00.900_WISE_4.6.fits']
2025-05-20 09:38:50,949 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:38:50,949 - data_manager - INFO - 第一次匹配结果: ['G000.484-00.900_WISE_12.fits']
2025-05-20 09:38:50,949 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.484-00.900\G000.484-00.900_WISE_12.fits
2025-05-20 09:38:50,953 - data_manager - INFO - FITS数据统计: 最小值=425.390625, 最大值=9138.107421875, 均值=1763.59130859375, 中位数=1628.8748779296875
2025-05-20 09:38:50,953 - data_manager - INFO - 有效数据点数量: 33835/33856 (99.94%)
2025-05-20 09:38:50,960 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-05-20 09:38:50,960 - main_single_folder - INFO - WISE数据加载完成
2025-05-20 09:38:50,960 - main_single_folder - INFO - 步骤2：区域映射
2025-05-20 09:38:50,962 - main_single_folder - INFO - 使用W3/W4波段比值方法定义PDR区域
2025-05-20 09:38:50,962 - region_mapper - INFO - 使用W3/W4波段比值方法定义PDR掩模
2025-05-20 09:38:50,962 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:38:50,962 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:38:50,963 - region_mapper.ratio - INFO - 加载源G000.484-00.900的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:38:50,963 - region_mapper.ratio - INFO - 加载源G000.484-00.900的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:38:50,963 - data_manager - INFO - 为源G000.484-00.900加载多波段WISE数据: ('12', '22')
2025-05-20 09:38:50,967 - data_manager - INFO - 为源G000.484-00.900加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.484-00.900
2025-05-20 09:38:50,967 - data_manager - INFO - 目录中的所有文件: ['G000.484-00.900_ATLASGAL_870um.fits', 'G000.484-00.900_IRIS_100.fits', 'G000.484-00.900_NVSS.fits', 'G000.484-00.900_WISE_12.fits', 'G000.484-00.900_WISE_22.fits', 'G000.484-00.900_WISE_3.4.fits', 'G000.484-00.900_WISE_4.6.fits']
2025-05-20 09:38:50,967 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:38:50,967 - data_manager - INFO - 第一次匹配结果: ['G000.484-00.900_WISE_12.fits']
2025-05-20 09:38:50,967 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.484-00.900\G000.484-00.900_WISE_12.fits
2025-05-20 09:38:50,970 - data_manager - INFO - FITS数据统计: 最小值=425.390625, 最大值=9138.107421875, 均值=1763.59130859375, 中位数=1628.8748779296875
2025-05-20 09:38:50,970 - data_manager - INFO - 有效数据点数量: 33835/33856 (99.94%)
2025-05-20 09:38:51,054 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-05-20 09:38:51,054 - data_manager - INFO - 使用12μm波段的WCS作为参考
2025-05-20 09:38:51,054 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (184, 184)
2025-05-20 09:38:51,059 - data_manager - INFO - 为源G000.484-00.900加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.484-00.900
2025-05-20 09:38:51,059 - data_manager - INFO - 目录中的所有文件: ['G000.484-00.900_ATLASGAL_870um.fits', 'G000.484-00.900_IRIS_100.fits', 'G000.484-00.900_NVSS.fits', 'G000.484-00.900_WISE_12.fits', 'G000.484-00.900_WISE_22.fits', 'G000.484-00.900_WISE_3.4.fits', 'G000.484-00.900_WISE_4.6.fits']
2025-05-20 09:38:51,059 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:38:51,059 - data_manager - INFO - 第一次匹配结果: ['G000.484-00.900_WISE_22.fits']
2025-05-20 09:38:51,060 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.484-00.900\G000.484-00.900_WISE_22.fits
2025-05-20 09:38:51,062 - data_manager - INFO - FITS数据统计: 最小值=334.9193420410156, 最大值=1758.387451171875, 均值=388.44000244140625, 中位数=357.1945495605469
2025-05-20 09:38:51,062 - data_manager - INFO - 有效数据点数量: 9998/10000 (99.98%)
2025-05-20 09:38:51,069 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (100, 100)
2025-05-20 09:38:51,069 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (100, 100)
2025-05-20 09:38:51,070 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w3', 'w4']
2025-05-20 09:38:51,070 - region_mapper.ratio - INFO - 调整W4波段数据从(100, 100)到(184, 184)
2025-05-20 09:38:51,070 - region_mapper.ratio - INFO - 调整W4波段数据从(100, 100)到(184, 184)
2025-05-20 09:38:51,078 - region_mapper.ratio - INFO - W3/W4比值范围: 3.63-6.14，均值: 4.53
2025-05-20 09:38:51,078 - region_mapper.ratio - INFO - W3/W4比值范围: 3.63-6.14，均值: 4.53
2025-05-20 09:38:51,085 - region_mapper.ratio - INFO - 像素尺度: 482.85 像素/角秒
2025-05-20 09:38:51,085 - region_mapper.ratio - INFO - 像素尺度: 482.85 像素/角秒
2025-05-20 09:38:51,086 - region_mapper.ratio - INFO - 有效半径: 0.1 像素
2025-05-20 09:38:51,086 - region_mapper.ratio - INFO - 有效半径: 0.1 像素
2025-05-20 09:38:51,086 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:38:51,086 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:38:51,828 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.642
2025-05-20 09:38:51,828 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.642
2025-05-20 09:38:51,903 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.578
2025-05-20 09:38:51,903 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.578
2025-05-20 09:38:51,982 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.562
2025-05-20 09:38:51,982 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.562
2025-05-20 09:38:51,982 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 09:38:51,982 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 09:38:52,050 - region_mapper.ratio - INFO - 聚类 0: 平均比值=4.60±0.07, 大小=106, 平均距离=7.5, 紧凑性=6.4
2025-05-20 09:38:52,050 - region_mapper.ratio - INFO - 聚类 0: 平均比值=4.60±0.07, 大小=106, 平均距离=7.5, 紧凑性=6.4
2025-05-20 09:38:52,050 - region_mapper.ratio - INFO - 聚类 1: 平均比值=4.40±0.05, 大小=210, 平均距离=6.3, 紧凑性=6.1
2025-05-20 09:38:52,050 - region_mapper.ratio - INFO - 聚类 1: 平均比值=4.40±0.05, 大小=210, 平均距离=6.3, 紧凑性=6.1
2025-05-20 09:38:52,051 - region_mapper.ratio - INFO - 选择聚类 0 作为PDR区域
2025-05-20 09:38:52,051 - region_mapper.ratio - INFO - 选择聚类 0 作为PDR区域
2025-05-20 09:38:52,056 - region_mapper.ratio - INFO - PDR掩模覆盖117个像素
2025-05-20 09:38:52,056 - region_mapper.ratio - INFO - PDR掩模覆盖117个像素
2025-05-20 09:38:52,069 - region_mapper.ratio - INFO - 发现3个连通区域，选择包含中心或最近的区域
2025-05-20 09:38:52,069 - region_mapper.ratio - INFO - 发现3个连通区域，选择包含中心或最近的区域
2025-05-20 09:38:54,672 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G000.484-00.900_w3w4_ratio.png
2025-05-20 09:38:54,672 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G000.484-00.900_w3w4_ratio.png
2025-05-20 09:38:54,672 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖84个像素
2025-05-20 09:38:54,672 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖84个像素
2025-05-20 09:38:54,708 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G000.484-00.900_region_masks.fits
2025-05-20 09:38:54,708 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G000.484-00.900_region_masks.fits
2025-05-20 09:38:54,713 - data_manager - INFO - 为源G000.484-00.900加载Gaia数据，中心坐标: RA=267.571483, Dec=-28.987003, 半径=0.0375度
2025-05-20 09:38:54,716 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances-Data/gaia_data\gaia_G000.484-00.900_10arcmin_min10.csv
H:\Augment\Parallax distances\src\data_manager.py:87: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.
  df = pd.read_csv(gaia_file)
2025-05-20 09:38:55,340 - data_manager - INFO - 成功加载Gaia数据，共36895条记录
2025-05-20 09:38:55,360 - data_manager - INFO - 成功加载Gaia数据，共1455个源在搜索半径内
2025-05-20 09:38:55,374 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G000.484-00.900_gaia_regions.fits
2025-05-20 09:38:55,788 - data_manager - WARNING - 检测到可能导致保存问题的列: ['region']
2025-05-20 09:38:55,807 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:38:55,844 - data_manager - ERROR - 保存数据失败: The following keyword arguments to Column were invalid:
    Column format option (TFORMn) failed verification: Illegal format `P1A()`. The invalid value will be ignored for the purpose of formatting the data in this column.
2025-05-20 09:38:55,844 - main_single_folder - ERROR - 加载Gaia数据失败: The following keyword arguments to Column were invalid:
    Column format option (TFORMn) failed verification: Illegal format `P1A()`. The invalid value will be ignored for the purpose of formatting the data in this column.
2025-05-20 09:38:55,844 - main_single_folder - WARNING - 将使用空的Gaia数据继续处理
2025-05-20 09:38:55,846 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:38:55,846 - main_single_folder - INFO - 步骤3：恒星选择
2025-05-20 09:38:55,851 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-05-20 09:38:55,851 - star_selector - WARNING - 没有有效的视差数据，跳过视差信噪比筛选
2025-05-20 09:38:55,851 - star_selector - WARNING - 没有有效的RUWE数据，跳过RUWE筛选
2025-05-20 09:38:55,852 - star_selector - INFO - 质量筛选详细信息:
2025-05-20 09:38:55,852 - star_selector - INFO -   视差信噪比阈值: 5.0
2025-05-20 09:38:55,852 - star_selector - INFO -   RUWE阈值: 1.4
2025-05-20 09:38:55,853 - star_selector - INFO -   总体通过率: 0.0%
2025-05-20 09:38:55,853 - star_selector - INFO - 质量筛选完成，保留0/0个源
2025-05-20 09:38:55,853 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G000.484-00.900_filtered_data.fits
2025-05-20 09:38:55,865 - data_manager - INFO - 成功保存数据
2025-05-20 09:38:55,866 - main_single_folder - INFO - 保存质量筛选后的数据到: results/batch_single_folder\processed\G000.484-00.900_filtered_data.fits
2025-05-20 09:38:55,870 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-05-20 09:38:55,870 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-05-20 09:38:55,871 - star_selector - INFO - 筛选恒星样本，原始样本大小: 0
2025-05-20 09:38:55,871 - star_selector - INFO - 排除YSO后: 0/0个源
2025-05-20 09:38:55,871 - star_selector - INFO - 区域 cavity: 0个源
2025-05-20 09:38:55,872 - star_selector - INFO - 区域 pdr: 0个源
2025-05-20 09:38:55,872 - star_selector - INFO - 区域 external: 0个源
2025-05-20 09:38:55,872 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 0
2025-05-20 09:38:55,872 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G000.484-00.900_star_sample.fits
2025-05-20 09:38:55,884 - data_manager - INFO - 成功保存数据
2025-05-20 09:38:55,884 - main_single_folder - INFO - 恒星选择完成
2025-05-20 09:38:55,884 - main_single_folder - INFO - 步骤4：消光计算
2025-05-20 09:38:55,889 - extinction_estimator - INFO - 为0个恒星计算A_V，参数: {'rv': 3.1}
2025-05-20 09:38:55,889 - extinction_estimator - WARNING - 星表为空，无法计算A_V
2025-05-20 09:38:55,890 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G000.484-00.900_stars_with_av.fits
2025-05-20 09:38:55,901 - data_manager - INFO - 成功保存数据
2025-05-20 09:38:55,902 - main_single_folder - INFO - 消光计算完成
2025-05-20 09:38:55,902 - main_single_folder - INFO - 步骤5：距离分析
2025-05-20 09:38:55,902 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-05-20 09:38:55,902 - distance_analyzer - INFO - 将分析0个区域: 
2025-05-20 09:38:55,902 - distance_analyzer - INFO - 估计分子云距离
2025-05-20 09:38:55,902 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity, pdr, external
2025-05-20 09:38:55,903 - distance_analyzer - WARNING - 没有检测到任何跳变，无法估计距离
2025-05-20 09:38:55,903 - main_single_folder - INFO - 距离分析完成
2025-05-20 09:38:55,904 - main_single_folder - INFO - 步骤6：可视化和报告生成
2025-05-20 09:38:55,908 - data_manager - INFO - 为源G000.484-00.900加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.484-00.900
2025-05-20 09:38:55,908 - data_manager - INFO - 目录中的所有文件: ['G000.484-00.900_ATLASGAL_870um.fits', 'G000.484-00.900_IRIS_100.fits', 'G000.484-00.900_NVSS.fits', 'G000.484-00.900_WISE_12.fits', 'G000.484-00.900_WISE_22.fits', 'G000.484-00.900_WISE_3.4.fits', 'G000.484-00.900_WISE_4.6.fits']
2025-05-20 09:38:55,909 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:38:55,909 - data_manager - INFO - 第一次匹配结果: ['G000.484-00.900_WISE_12.fits']
2025-05-20 09:38:55,909 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.484-00.900\G000.484-00.900_WISE_12.fits
2025-05-20 09:38:55,912 - data_manager - INFO - FITS数据统计: 最小值=425.390625, 最大值=9138.107421875, 均值=1763.59130859375, 中位数=1628.8748779296875
2025-05-20 09:38:55,912 - data_manager - INFO - 有效数据点数量: 33835/33856 (99.94%)
2025-05-20 09:38:55,919 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-05-20 09:38:55,923 - visualizer - INFO - 绘制WISE图像和区域掩模，输出到: results/batch_single_folder\visualizations\G000.484-00.900_wise_regions_stars.png
2025-05-20 09:38:56,043 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:38:58,056 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:38:58,064 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:38:59,842 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G000.484-00.900_wise_regions_stars.png
2025-05-20 09:38:59,842 - main_single_folder - INFO - 成功保存WISE图像和区域掩模到: results/batch_single_folder\visualizations\G000.484-00.900_wise_regions_stars.png
2025-05-20 09:38:59,843 - main_single_folder - INFO - 加载WISE多波段数据用于RGB图像生成
2025-05-20 09:38:59,843 - data_manager - INFO - 为源G000.484-00.900加载多波段WISE数据: ('3.4', '12', '22')
2025-05-20 09:38:59,848 - data_manager - INFO - 为源G000.484-00.900加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.484-00.900
2025-05-20 09:38:59,848 - data_manager - INFO - 目录中的所有文件: ['G000.484-00.900_ATLASGAL_870um.fits', 'G000.484-00.900_IRIS_100.fits', 'G000.484-00.900_NVSS.fits', 'G000.484-00.900_WISE_12.fits', 'G000.484-00.900_WISE_22.fits', 'G000.484-00.900_WISE_3.4.fits', 'G000.484-00.900_WISE_4.6.fits']
2025-05-20 09:38:59,848 - data_manager - INFO - 查找WISE 3.4μm波段的FITS文件
2025-05-20 09:38:59,848 - data_manager - INFO - 第一次匹配结果: ['G000.484-00.900_WISE_3.4.fits']
2025-05-20 09:38:59,848 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.484-00.900\G000.484-00.900_WISE_3.4.fits
2025-05-20 09:38:59,899 - data_manager - INFO - FITS数据统计: 最小值=26.33490753173828, 最大值=2850.26611328125, 均值=394.62921142578125, 中位数=313.13275146484375
2025-05-20 09:38:59,899 - data_manager - INFO - 有效数据点数量: 38414/38416 (99.99%)
2025-05-20 09:38:59,906 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (196, 196)
2025-05-20 09:38:59,906 - data_manager - INFO - 使用3.4μm波段的WCS作为参考
2025-05-20 09:38:59,907 - data_manager - INFO - 成功加载WISE 3.4μm波段数据，尺寸: (196, 196)
2025-05-20 09:38:59,912 - data_manager - INFO - 为源G000.484-00.900加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.484-00.900
2025-05-20 09:38:59,912 - data_manager - INFO - 目录中的所有文件: ['G000.484-00.900_ATLASGAL_870um.fits', 'G000.484-00.900_IRIS_100.fits', 'G000.484-00.900_NVSS.fits', 'G000.484-00.900_WISE_12.fits', 'G000.484-00.900_WISE_22.fits', 'G000.484-00.900_WISE_3.4.fits', 'G000.484-00.900_WISE_4.6.fits']
2025-05-20 09:38:59,912 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:38:59,912 - data_manager - INFO - 第一次匹配结果: ['G000.484-00.900_WISE_12.fits']
2025-05-20 09:38:59,912 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.484-00.900\G000.484-00.900_WISE_12.fits
2025-05-20 09:38:59,915 - data_manager - INFO - FITS数据统计: 最小值=425.390625, 最大值=9138.107421875, 均值=1763.59130859375, 中位数=1628.8748779296875
2025-05-20 09:38:59,915 - data_manager - INFO - 有效数据点数量: 33835/33856 (99.94%)
2025-05-20 09:38:59,923 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-05-20 09:38:59,923 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (184, 184)
2025-05-20 09:38:59,928 - data_manager - INFO - 为源G000.484-00.900加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.484-00.900
2025-05-20 09:38:59,928 - data_manager - INFO - 目录中的所有文件: ['G000.484-00.900_ATLASGAL_870um.fits', 'G000.484-00.900_IRIS_100.fits', 'G000.484-00.900_NVSS.fits', 'G000.484-00.900_WISE_12.fits', 'G000.484-00.900_WISE_22.fits', 'G000.484-00.900_WISE_3.4.fits', 'G000.484-00.900_WISE_4.6.fits']
2025-05-20 09:38:59,928 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:38:59,929 - data_manager - INFO - 第一次匹配结果: ['G000.484-00.900_WISE_22.fits']
2025-05-20 09:38:59,929 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.484-00.900\G000.484-00.900_WISE_22.fits
2025-05-20 09:38:59,931 - data_manager - INFO - FITS数据统计: 最小值=334.9193420410156, 最大值=1758.387451171875, 均值=388.44000244140625, 中位数=357.1945495605469
2025-05-20 09:38:59,931 - data_manager - INFO - 有效数据点数量: 9998/10000 (99.98%)
2025-05-20 09:38:59,938 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (100, 100)
2025-05-20 09:38:59,938 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (100, 100)
2025-05-20 09:38:59,939 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w1', 'w3', 'w4']
2025-05-20 09:38:59,939 - visualizer - INFO - 创建WISE三波段RGB图像，输出到: results/batch_single_folder\visualizations\G000.484-00.900_wise_rgb.png
2025-05-20 09:39:00,070 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:39:00,070 - visualizer - INFO - 波段 3.4μm 的尺寸: (196, 196)
2025-05-20 09:39:00,070 - visualizer - INFO - 波段 12μm 的尺寸: (184, 184)
2025-05-20 09:39:00,070 - visualizer - INFO - 波段 22μm 的尺寸: (100, 100)
2025-05-20 09:39:00,070 - visualizer - INFO - Using 12μm band size as target: (184, 184)
2025-05-20 09:39:00,071 - visualizer - INFO - Using target image size: (184, 184), original sizes: {'w1': (196, 196), 'w3': (184, 184), 'w4': (100, 100)}
2025-05-20 09:39:00,076 - visualizer - INFO - 调整w4波段数据从(100, 100)到(184, 184)
2025-05-20 09:39:00,076 - visualizer - INFO - 红色通道(22μm)数据范围: 81.82850646972656-1758.387451171875
2025-05-20 09:39:00,077 - visualizer - INFO - 绿色通道(12μm)数据范围: 425.390625-9138.107421875
2025-05-20 09:39:00,083 - visualizer - INFO - 调整w1波段数据从(196, 196)到(184, 184)
2025-05-20 09:39:00,083 - visualizer - INFO - 蓝色通道(3.4μm)数据范围: 25.608816146850586-2850.26611328125
2025-05-20 09:39:00,086 - visualizer - INFO - 使用Lupton RGB方法，stretch=2930.46, Q=10
2025-05-20 09:39:00,086 - visualizer - INFO - 各波段99.5%百分位数: R=1251.85, G=5860.92, B=1696.32
2025-05-20 09:39:00,090 - visualizer - INFO - RGB数据形状: (184, 184, 3), 类型: float32
2025-05-20 09:39:00,091 - visualizer - INFO - Red通道数据范围：0.019607843831181526-0.40784314274787903，均值：0.0900
2025-05-20 09:39:00,091 - visualizer - INFO - Green通道数据范围：0.0-0.8901960849761963，均值：0.4121
2025-05-20 09:39:00,091 - visualizer - INFO - Blue通道数据范围：0.0-0.5568627715110779，均值：0.0895
2025-05-20 09:39:01,525 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:39:01,530 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:39:01,537 - visualizer - INFO - 按照要求不显示Gaia星
2025-05-20 09:39:01,539 - visualizer - INFO - 设置坐标刻度间隔: RA=10.000度, Dec=10.000度
2025-05-20 09:39:02,472 - visualizer - INFO - 成功保存RGB图像到: results/batch_single_folder\visualizations\G000.484-00.900_wise_rgb.png
2025-05-20 09:39:02,473 - main_single_folder - INFO - 成功保存WISE RGB图像到: results/batch_single_folder\visualizations\G000.484-00.900_wise_rgb.png
2025-05-20 09:39:02,473 - main_single_folder - WARNING - 没有足够的数据绘制消光-距离散点图
2025-05-20 09:39:02,489 - main_single_folder - INFO - 成功保存处理报告到: results/batch_single_folder\reports\G000.484-00.900_report.txt
2025-05-20 09:39:02,489 - main_single_folder - INFO - 处理G000.484-00.900完成
