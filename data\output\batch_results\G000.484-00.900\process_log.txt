处理时间: 2025-04-24 04:51:22
耗时: 4.22秒

标准输出:

未能估计G000.484-00.900的分子云距离


标准错误:
2025-04-24 04:51:21,724 - main - INFO - 输出目录: data/output/batch_results\G000.484-00.900
2025-04-24 04:51:21,725 - main - INFO - 加载源G000.484-00.900的信息
2025-04-24 04:51:21,725 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
H:\Augment\Parallax distances\src\data_manager.py:43: FutureWarning: The 'delim_whitespace' keyword in pd.read_csv is deprecated and will be removed in a future version. Use ``sep='\s+'`` instead
  df = pd.read_csv(catalog_path, delim_whitespace=True, comment='#', header=None, names=column_names)
2025-04-24 04:51:21,731 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-04-24 04:51:21,732 - main - INFO - 目录中的所有文件: ['G000.484-00.900_ATLASGAL_870um.fits', 'G000.484-00.900_IRIS_100.fits', 'G000.484-00.900_NVSS.fits', 'G000.484-00.900_WISE_12.fits', 'G000.484-00.900_WISE_22.fits', 'G000.484-00.900_WISE_3.4.fits', 'G000.484-00.900_WISE_4.6.fits']
2025-04-24 04:51:21,732 - main - INFO - 第一次匹配结果: ['G000.484-00.900_WISE_12.fits']
2025-04-24 04:51:21,732 - main - INFO - 从FITS文件获取坐标: U:/Data/Bubbles/Wise bubbles\G000.484-00.900\G000.484-00.900_WISE_12.fits
2025-04-24 04:51:21,780 - main - INFO - 从FITS头信息获取坐标系统: fk5
2025-04-24 04:51:21,783 - main - INFO - 从FITS头信息获取坐标: RA=267.570992, Dec=-28.986505 (ICRS)
2025-04-24 04:51:21,783 - main - INFO - 使用有效半径27.0角秒 (0.007500度)
2025-04-24 04:51:21,784 - main - INFO - 从源表加载信息: RA=267.570992, Dec=-28.986505, R_eff=0.007500度
2025-04-24 04:51:21,784 - main - INFO - 步骤1：加载WISE数据
2025-04-24 04:51:21,788 - data_manager - INFO - 为源G000.484-00.900加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.484-00.900
2025-04-24 04:51:21,788 - data_manager - INFO - 目录中的所有文件: ['G000.484-00.900_ATLASGAL_870um.fits', 'G000.484-00.900_IRIS_100.fits', 'G000.484-00.900_NVSS.fits', 'G000.484-00.900_WISE_12.fits', 'G000.484-00.900_WISE_22.fits', 'G000.484-00.900_WISE_3.4.fits', 'G000.484-00.900_WISE_4.6.fits']
2025-04-24 04:51:21,789 - data_manager - INFO - 第一次匹配结果: ['G000.484-00.900_WISE_12.fits']
2025-04-24 04:51:21,789 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.484-00.900\G000.484-00.900_WISE_12.fits
2025-04-24 04:51:21,798 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-04-24 04:51:21,798 - main - INFO - WISE数据加载完成
2025-04-24 04:51:21,798 - main - INFO - 步骤2：区域映射
2025-04-24 04:51:21,799 - region_mapper - INFO - 处理WISE图像，平滑sigma=1.0
2025-04-24 04:51:21,799 - region_mapper - INFO - 替换图像中的NaN值
2025-04-24 04:51:21,800 - region_mapper - INFO - 应用高斯平滑
2025-04-24 04:51:21,801 - region_mapper - INFO - 估计并减除背景
2025-04-24 04:51:21,812 - region_mapper - INFO - WISE图像处理完成
2025-04-24 04:51:21,812 - region_mapper - INFO - 定义PDR掩模，阈值因子=0.5，最大半径因子=3.0
2025-04-24 04:51:21,812 - region_mapper - INFO - WCS坐标系统: fk5
2025-04-24 04:51:21,812 - region_mapper - INFO - 将中心坐标从ICRS转换为fk5
2025-04-24 04:51:21,817 - region_mapper - INFO - 中心坐标 (RA=267.570992, Dec=-28.986505) 对应像素坐标 (X=91.5, Y=91.5)
2025-04-24 04:51:21,821 - region_mapper - INFO - 像素尺度: 482.85 像素/角秒
2025-04-24 04:51:21,821 - region_mapper - INFO - 最大搜索半径: 0.2 像素
2025-04-24 04:51:21,822 - region_mapper - INFO - PDR阈值: 0.00 (峰值的50%)
2025-04-24 04:51:21,838 - region_mapper - INFO - PDR掩模创建完成，覆盖33856个像素
2025-04-24 04:51:21,839 - region_mapper - INFO - 定义空腔掩模，有效半径=0.0075度
2025-04-24 04:51:21,846 - region_mapper - INFO - 有效半径: 0.1 像素
2025-04-24 04:51:21,846 - region_mapper - INFO - 空腔掩模创建完成，覆盖0个像素
2025-04-24 04:51:21,846 - region_mapper - INFO - 定义外部区域掩模，最大半径因子=5.0
2025-04-24 04:51:21,853 - region_mapper - INFO - 最大半径: 0.3 像素
2025-04-24 04:51:21,853 - region_mapper - INFO - 外部区域掩模创建完成，覆盖0个像素
2025-04-24 04:51:21,854 - region_mapper - INFO - 保存区域掩模到: data/output/batch_results\G000.484-00.900\processed\G000.484-00.900_region_masks.npz
2025-04-24 04:51:21,856 - region_mapper - INFO - 成功保存区域掩模
2025-04-24 04:51:21,856 - main - INFO - 区域映射完成
2025-04-24 04:51:21,856 - main - INFO - 由于缺少Gaia数据，跳过恒星选择、消光计算和距离分析步骤
2025-04-24 04:51:21,856 - main - INFO - 处理G000.484-00.900完成
