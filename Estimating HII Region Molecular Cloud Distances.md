
### Project Plan: Estimating HII Region Molecular Cloud Distances (ID: LZvSD)

**1. Goal:**
To estimate the distance (D_cloud) to the molecular cloud hosting a given HII region by analyzing the extinction (A_V) vs. distance (D) relationship of Gaia stars located in different zones relative to the HII region (Cavity, PDR, External).

**2. Core Hypothesis & Methodology:**
The host molecular cloud will manifest as a distinct "jump" (increase, ΔA_V) in extinction at its distance D_cloud for background stars. By comparing the magnitude of this jump (ΔA_V) for stars projected onto the HII region's central cavity, its surrounding PDR (traced by WISE 12µm), and the immediate external environment, we can distinguish the target cloud's jump from potential foreground extinction jumps. Specifically:
*   **Foreground Cloud (D_fg < D_cloud):** Expected to cause a similar ΔA_V(fg) across all three regions (Cavity, PDR, External).
*   **Target Cloud (D_cloud):** Expected to cause a large ΔA_V(PDR) and ΔA_V(External) due to dense gas/dust, but a significantly smaller ΔA_V(Cavity) if the cavity is relatively clear of dust. This differential signature is key.

**3. Workflow Overview (Text-based Flowchart):**

```
START
 |
 V
[Input HII Region Parameters (RA, Dec, R_eff)]
 |
 V
[Data Acquisition]
 |  +--> Gaia DR3 (w/ Bailer-Jones Distances)
 |  +--> 2MASS Point Source Catalog
 |  +--> WISE AllWISE Point Source Catalog
 |  +--> WISE 12µm (W3) FITS Image
 |
 V
[Data Preprocessing & Cross-Matching]
 |  +--> Gaia x 2MASS
 |  +--> Gaia x WISE
 |
 V
[Spatial Region Definition (using WISE 12µm & R_eff)]
 |  +--> Process WISE 12µm Image (smooth, background subtract)
 |  +--> Define PDR Mask (e.g., within ~3*R_eff, above ~50% peak W3 flux)
 |  +--> Define Cavity Mask (e.g., within R_eff, outside PDR Mask)
 |  +--> Define External Mask (e.g., annulus from PDR outer edge to ~5*R_eff)
 |
 V
[Star Sample Preparation]
 |  +--> Assign Gaia stars to Regions (Cavity, PDR, External) based on Masks
 |  +--> Apply Gaia Quality Filters (e.g., parallax_over_error, RUWE - *tune based on sample size*)
 |  +--> Identify & Remove YSOs (using WISE colors, e.g., [W1]-[W2] vs [W2]-[W3])
 |
 V
[Extinction Calculation (per star, per region)]
 |  +--> IF reliable 2MASS match exists:
 |  |    +--> Estimate Intrinsic Color (e.g., (H-K)0 via CMD selection - Red Giants)
 |  |    +--> Calculate A_V from E(H-K) (or E(J-K)) using extinction law (assume R_V=3.1 initially) --> *Preferred Method*
 |  +--> ELSE (no reliable 2MASS):
 |  |    +--> Use Gaia A_G or calculate A_V from Gaia colors (e.g., E(BP-RP), E(G-RP)) --> *Fallback/Comparison Method*
 |  +--> Record A_V, Distance (d), d_err, and method used for each star
 |
 V
[Extinction-Distance Analysis (for each region separately)]
 |  +--> Plot A_V vs. Distance (d) scatter plots
 |  +--> Calculate & Plot Running Median of A_V vs. d
 |  +--> Identify potential extinction jump distances (D_jump) and magnitudes (ΔA_V)
 |
 V
[Interpretation & Distance Estimation]
 |  +--> Compare D_jump and ΔA_V across Cavity, PDR, External regions
 |  +--> IF a D_jump shows ΔA_V(PDR) & ΔA_V(Ext) large, AND ΔA_V(Cav) significantly smaller:
 |  |    +--> Identify this D_jump as D_cloud (Target Cloud Distance)
 |  +--> IF a D_jump shows ΔA_V similar across all regions:
 |  |    +--> Identify this as likely Foreground Cloud (D_fg)
 |  +--> Evaluate consistency, significance, and potential ambiguities (multiple jumps, noise)
 |
 V
[Output]
 |  +--> Estimated D_cloud +/- uncertainty
 |  +--> Supporting Plots (A_V vs D for all regions, Median curves)
 |  +--> Analysis Report (methods, parameters, results, limitations)
 |
 V
END
```

**4. Key Steps & Considerations:**

*   **Data Acquisition & Prep:** Ensure accurate coordinates and sufficient sky coverage around the HII region. Handle catalog flags and uncertainties properly during cross-matching.
*   **Region Definition:** The choice of WISE 12µm threshold and radii (3R, 5R) will influence sample selection. Document these parameters clearly. Perform sensitivity tests if possible. The goal is robust *relative* separation of the zones.
*   **Star Selection:** Balancing Gaia quality cuts and sufficient sample size (especially for smaller HII regions or dense PDRs) is critical. Start with moderate cuts and test sensitivity. YSO removal is vital to avoid confusing intrinsic redness with extinction.
*   **Extinction Calculation:** Estimating intrinsic colors is a major source of uncertainty. Clearly document the method used (e.g., selecting Red Clump/Red Giant Branch stars from CMD, using a nearby control field). Be aware of the limitations of Gaia-only extinction at high A_V. Using E(H-K) is generally preferred for dusty regions.
*   **Jump Detection:** Visual inspection combined with running medians is a practical start. More sophisticated fitting (e.g., Bayesian methods modeling a step function) can provide quantitative uncertainties but requires more complex implementation.
*   **Interpretation (Crucial):** The *differential* signal (ΔA_V(Cav) << ΔA_V(PDR) ≈ ΔA_V(Ext) at D_cloud) is the strongest evidence. Carefully analyze any foreground jumps (ΔA_V similar everywhere) to avoid misidentification. Acknowledge limitations due to projection effects, incomplete cavity clearing, complex cloud structures, and data noise/biases.

**5. Modular Code Structure Plan:**

```python
# Conceptual Python Modules

# ------------------------------
# 1. data_manager.py
# ------------------------------
# Functions/Classes for:
# - Querying/Loading Gaia, 2MASS, WISE catalogs (e.g., using astroquery)
# - Loading WISE FITS images (astropy.io.fits)
# - Performing coordinate-based cross-matching (astropy.coordinates)
# - Merging catalogs and handling data types/units (astropy.table, pandas)

# Example functions:
# load_gaia(ra, dec, radius, gaia_release='dr3', ...)
# load_2mass(ra, dec, radius, ...)
# load_wise_allwise(ra, dec, radius, ...)
# load_wise_fits(filepath)
# cross_match_catalogs(cat1, cat2, separation)
# merge_bailer_jones(gaia_table, bj_table)

# ------------------------------
# 2. region_mapper.py
# ------------------------------
# Functions/Classes for:
# - Processing WISE 12µm image (smoothing, background subtraction - photutils, scipy.ndimage)
# - Defining PDR contour/mask based on flux threshold (skimage.measure, photutils)
# - Defining Cavity and External masks based on geometry and PDR mask (numpy, shapely/regions)
# - Converting sky coordinates to pixel coordinates and vice-versa (astropy.wcs)
# - Saving/Loading region masks

# Example functions:
# process_wise_image(fits_data, header)
# define_pdr_mask(image, wcs, center_coord, R_eff, threshold, max_radius_factor=3)
# define_cavity_mask(image_shape, wcs, center_coord, R_eff, pdr_mask)
# define_external_mask(image_shape, pdr_mask, max_radius_factor=5)
# assign_region_tags(star_coords, wcs, cavity_mask, pdr_mask, external_mask)

# ------------------------------
# 3. star_selector.py
# ------------------------------
# Functions/Classes for:
# - Applying Gaia quality cuts based on config parameters
# - Identifying YSOs based on WISE colors (using predefined criteria/boxes)
# - Filtering star tables based on quality cuts and YSO flags

# Example functions:
# apply_quality_cuts(star_table, config) # config = {'parallax_snr_min': 5, 'ruwe_max': 1.4, ...}
# flag_ysos_wise(star_table_wise_matched, config) # config = {'w1w2_min': 0.8, ...}
# filter_star_sample(star_table, quality_flags, yso_flags)

# ------------------------------
# 4. extinction_estimator.py
# ------------------------------
# Functions/Classes for:
# - Estimating intrinsic colors (e.g., (H-K)0) for selected star types (e.g., from CMD)
# - Calculating A_V from near-IR color excess (E(H-K), E(J-K))
# - Calculating/Retrieving A_V/A_G from Gaia data
# - Applying the chosen method based on data availability (2MASS match)

# Example functions:
# estimate_intrinsic_hk(star_table, method='select_giants', ...)
# calculate_av_nir(H, K, intrinsic_hk, Rv=3.1)
# get_av_gaia(G, BP, RP, ...) # Or retrieve A_G from catalog
# compute_av_per_star(star_data, intrinsic_colors) # Chooses method

# ------------------------------
# 5. distance_analyzer.py
# ------------------------------
# Functions/Classes for:
# - Plotting A_V vs. Distance for different regions (matplotlib, seaborn)
# - Calculating running statistics (median, percentiles) (scipy.stats, pandas)
# - Implementing jump detection algorithm (e.g., gradient analysis on median, step function fit)
# - Comparing jump characteristics (D_jump, ΔA_V) across regions

# Example functions:
# plot_extinction_distance(region_data, output_path)
# compute_running_median(distances, extinctions, bins)
# find_jumps(distances, median_extinctions, ...)
# compare_jumps_across_regions(jump_results_pdr, jump_results_cavity, jump_results_ext)

# ------------------------------
# 6. main_pipeline.py
# ------------------------------
# - Main script to orchestrate the workflow.
# - Handles configuration (input HII region, file paths, analysis parameters).
# - Calls functions from other modules in sequence.
# - Collects results and generates final output (plots, tables, report summary).

# Example structure:
# parse_config()
# hii_region = load_hii_region_info()
# gaia_data, twomass_data, wise_data, wise_image = data_manager.load_all_data(...)
# matched_data = data_manager.preprocess_and_match(...)
# masks = region_mapper.define_all_regions(...)
# tagged_stars = region_mapper.assign_region_tags(...)
# filtered_stars = star_selector.filter_star_sample(...)
# stars_with_av = extinction_estimator.compute_av_for_sample(...)
# analysis_results = distance_analyzer.analyze_all_regions(...)
# final_distance, report = interpret_results(analysis_results)
# save_output(final_distance, report, plots)

```

**6. Expected Output:**
*   An estimated distance D_cloud to the molecular cloud, with an uncertainty estimate.
*   A set of plots showing A_V vs. Distance for Cavity, PDR, and External regions, highlighting the running medians and identified jumps.
*   A summary report detailing the HII region studied, data used, parameters chosen (filters, thresholds), methods applied, the final distance estimate, and a discussion of the evidence, confidence level, and limitations.

This refined plan emphasizes the differential analysis strategy and provides a clear roadmap for implementation.