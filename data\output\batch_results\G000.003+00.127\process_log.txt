处理时间: 2025-04-24 05:03:45
耗时: 12.26秒

标准输出:

未能估计G000.003+00.127的分子云距离


标准错误:
2025-04-24 05:03:37,118 - main - INFO - 输出目录: data/output/batch_results\G000.003+00.127
2025-04-24 05:03:37,118 - main - INFO - 加载源G000.003+00.127的信息
2025-04-24 05:03:37,118 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
H:\Augment\Parallax distances\src\data_manager.py:43: FutureWarning: The 'delim_whitespace' keyword in pd.read_csv is deprecated and will be removed in a future version. Use ``sep='\s+'`` instead
  df = pd.read_csv(catalog_path, delim_whitespace=True, comment='#', header=None, names=column_names)
2025-04-24 05:03:37,124 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-04-24 05:03:37,125 - main - INFO - 目录中的所有文件: ['G000.003+00.127_ATLASGAL_870um.fits', 'G000.003+00.127_IRIS_100.fits', 'G000.003+00.127_NVSS.fits', 'G000.003+00.127_WISE_12.fits', 'G000.003+00.127_WISE_22.fits', 'G000.003+00.127_WISE_3.4.fits', 'G000.003+00.127_WISE_4.6.fits']
2025-04-24 05:03:37,125 - main - INFO - 第一次匹配结果: ['G000.003+00.127_WISE_12.fits']
2025-04-24 05:03:37,125 - main - INFO - 从FITS文件获取坐标: U:/Data/Bubbles/Wise bubbles\G000.003+00.127\G000.003+00.127_WISE_12.fits
2025-04-24 05:03:37,128 - main - INFO - 从FITS头信息获取坐标系统: fk5
2025-04-24 05:03:37,131 - main - INFO - 从FITS头信息获取坐标: RA=266.282992, Dec=-28.866005 (ICRS)
2025-04-24 05:03:37,131 - main - INFO - 使用有效半径206.0角秒 (0.057222度)
2025-04-24 05:03:37,131 - main - INFO - 从源表加载信息: RA=266.282992, Dec=-28.866005, R_eff=0.057222度
2025-04-24 05:03:37,132 - main - INFO - 步骤1：加载WISE数据
2025-04-24 05:03:37,135 - data_manager - INFO - 为源G000.003+00.127加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.003+00.127
2025-04-24 05:03:37,136 - data_manager - INFO - 目录中的所有文件: ['G000.003+00.127_ATLASGAL_870um.fits', 'G000.003+00.127_IRIS_100.fits', 'G000.003+00.127_NVSS.fits', 'G000.003+00.127_WISE_12.fits', 'G000.003+00.127_WISE_22.fits', 'G000.003+00.127_WISE_3.4.fits', 'G000.003+00.127_WISE_4.6.fits']
2025-04-24 05:03:37,136 - data_manager - INFO - 第一次匹配结果: ['G000.003+00.127_WISE_12.fits']
2025-04-24 05:03:37,136 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.003+00.127\G000.003+00.127_WISE_12.fits
2025-04-24 05:03:37,145 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (316, 316)
2025-04-24 05:03:37,145 - main - INFO - WISE数据加载完成
2025-04-24 05:03:37,145 - main - INFO - 步骤2：区域映射
2025-04-24 05:03:37,145 - region_mapper - INFO - 处理WISE图像，平滑sigma=1.0
2025-04-24 05:03:37,146 - region_mapper - INFO - 替换图像中的NaN值
2025-04-24 05:03:37,147 - region_mapper - INFO - 应用高斯平滑
2025-04-24 05:03:37,150 - region_mapper - INFO - 估计并减除背景
2025-04-24 05:03:37,162 - region_mapper - INFO - WISE图像处理完成
2025-04-24 05:03:37,162 - region_mapper - INFO - 定义PDR掩模，阈值因子=0.5，最大半径因子=3.0
2025-04-24 05:03:37,162 - region_mapper - INFO - WCS坐标系统: fk5
2025-04-24 05:03:37,162 - region_mapper - INFO - 将中心坐标从ICRS转换为fk5
2025-04-24 05:03:37,168 - region_mapper - INFO - 中心坐标 (RA=266.282992, Dec=-28.866005) 对应像素坐标 (X=157.5, Y=157.5)
2025-04-24 05:03:37,171 - region_mapper - INFO - 像素尺度: 483.62 像素/角秒
2025-04-24 05:03:37,172 - region_mapper - INFO - 最大搜索半径: 1.3 像素
2025-04-24 05:03:37,173 - region_mapper - INFO - PDR阈值: 361.57 (峰值的50%)
2025-04-24 05:03:37,198 - region_mapper - INFO - PDR掩模创建完成，覆盖1个像素
2025-04-24 05:03:37,198 - region_mapper - INFO - 定义空腔掩模，有效半径=0.05722222222222222度
2025-04-24 05:03:37,205 - region_mapper - INFO - 有效半径: 0.4 像素
2025-04-24 05:03:37,206 - region_mapper - INFO - 空腔掩模创建完成，覆盖0个像素
2025-04-24 05:03:37,207 - region_mapper - INFO - 定义外部区域掩模，最大半径因子=5.0
2025-04-24 05:03:37,213 - region_mapper - INFO - 最大半径: 2.1 像素
2025-04-24 05:03:37,214 - region_mapper - INFO - 外部区域掩模创建完成，覆盖15个像素
2025-04-24 05:03:37,215 - region_mapper - INFO - 保存区域掩模到: data/output/batch_results\G000.003+00.127\processed\G000.003+00.127_region_masks.npz
2025-04-24 05:03:37,218 - region_mapper - INFO - 成功保存区域掩模
2025-04-24 05:03:37,222 - data_manager - INFO - 为源G000.003+00.127加载Gaia数据，中心坐标: RA=266.282992, Dec=-28.866005, 半径=0.2861度
2025-04-24 05:03:37,225 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances/gaia_data\gaia_G000.003+00.127_17arcmin_5R.csv
2025-04-24 05:03:37,594 - data_manager - INFO - 成功加载Gaia数据，共19876条记录
2025-04-24 05:03:37,626 - data_manager - INFO - 成功加载Gaia数据，共19794个源在搜索半径内
2025-04-24 05:03:37,633 - region_mapper - INFO - 为19794个恒星分配区域标签
2025-04-24 05:03:37,633 - region_mapper - INFO - WCS坐标系统: fk5
2025-04-24 05:03:37,633 - region_mapper - INFO - 将恒星坐标从ICRS转换为fk5
2025-04-24 05:03:37,746 - region_mapper - INFO - 区域标签分配完成: 空腔=0, PDR=0, 外部=5, 区域外=19789
2025-04-24 05:03:37,747 - data_manager - INFO - 保存处理后的数据到: data/output/batch_results\G000.003+00.127\processed\G000.003+00.127_gaia_regions.fits
2025-04-24 05:03:42,845 - data_manager - WARNING - 检测到可能导致保存问题的列: ['TYC2', 'region']
2025-04-24 05:03:42,876 - data_manager - INFO - 已移除有问题的列，继续保存
2025-04-24 05:03:43,286 - data_manager - INFO - 成功保存数据
2025-04-24 05:03:43,289 - main - INFO - 区域映射完成
2025-04-24 05:03:43,289 - main - INFO - 步骤3：恒星选择
2025-04-24 05:03:43,293 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-04-24 05:03:43,297 - star_selector - INFO - 视差信噪比筛选: 保留3894/19794个源
2025-04-24 05:03:43,298 - star_selector - INFO - RUWE筛选: 保留19296/19794个源
2025-04-24 05:03:43,330 - star_selector - INFO - 质量筛选完成，保留3704/19794个源
2025-04-24 05:03:43,334 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-04-24 05:03:43,334 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-04-24 05:03:43,335 - star_selector - INFO - 筛选恒星样本，原始样本大小: 3704
2025-04-24 05:03:43,335 - star_selector - INFO - 排除YSO后: 3704/3704个源
2025-04-24 05:03:43,347 - star_selector - INFO - 区域 cavity: 0个源
2025-04-24 05:03:43,347 - star_selector - INFO - 区域 pdr: 0个源
2025-04-24 05:03:43,347 - star_selector - INFO - 区域 external: 1个源
2025-04-24 05:03:43,348 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 3704
2025-04-24 05:03:43,348 - data_manager - INFO - 保存处理后的数据到: data/output/batch_results\G000.003+00.127\processed\G000.003+00.127_star_sample.fits
2025-04-24 05:03:44,108 - data_manager - WARNING - 检测到可能导致保存问题的列: ['TYC2', 'region']
2025-04-24 05:03:44,130 - data_manager - INFO - 已移除有问题的列，继续保存
2025-04-24 05:03:44,274 - data_manager - INFO - 成功保存数据
2025-04-24 05:03:44,276 - main - INFO - 恒星选择完成
2025-04-24 05:03:44,276 - main - INFO - 步骤4：消光计算
2025-04-24 05:03:44,279 - extinction_estimator - INFO - 为3704个恒星计算A_V，参数: {'rv': 3.1}
2025-04-24 05:03:44,300 - extinction_estimator - INFO - 使用2MASS H-K颜色计算A_V
2025-04-24 05:03:44,300 - extinction_estimator - INFO - 估计本征H-K颜色，方法: select_giants
2025-04-24 05:03:44,300 - extinction_estimator - INFO - 使用红巨星的典型H-K颜色: 0.15
2025-04-24 05:03:44,300 - extinction_estimator - INFO - 基于H-K颜色过量计算A_V，R_V=3.1
2025-04-24 05:03:44,302 - extinction_estimator - INFO - 计算得到3309个A_V值，范围: 0.00-59.66
2025-04-24 05:03:44,302 - extinction_estimator - INFO - 使用Gaia BP-RP颜色计算A_V
2025-04-24 05:03:44,303 - extinction_estimator - INFO - 估计本征BP-RP颜色，方法: fixed_value
2025-04-24 05:03:44,303 - extinction_estimator - INFO - 使用固定的BP-RP颜色: 0.8
2025-04-24 05:03:44,303 - extinction_estimator - INFO - 基于Gaia BP-RP颜色过量计算A_V，R_V=3.1
2025-04-24 05:03:44,304 - extinction_estimator - INFO - 计算得到3664个A_V值，范围: 0.00-10.13
2025-04-24 05:03:44,306 - extinction_estimator - INFO - 方法 2MASS_HK: 3309个源
2025-04-24 05:03:44,306 - extinction_estimator - INFO - 方法 Gaia_BPRP: 369个源
2025-04-24 05:03:44,306 - extinction_estimator - INFO - 方法 unknown: 26个源
2025-04-24 05:03:44,306 - extinction_estimator - INFO - A_V计算完成，范围: 0.00-59.66
2025-04-24 05:03:44,307 - data_manager - INFO - 保存处理后的数据到: data/output/batch_results\G000.003+00.127\processed\G000.003+00.127_stars_with_av.fits
2025-04-24 05:03:45,112 - data_manager - WARNING - 检测到可能导致保存问题的列: ['TYC2', 'region', 'av_method']
2025-04-24 05:03:45,132 - data_manager - INFO - 已移除有问题的列，继续保存
2025-04-24 05:03:45,273 - data_manager - INFO - 成功保存数据
2025-04-24 05:03:45,273 - main - INFO - 消光计算完成
2025-04-24 05:03:45,274 - main - INFO - 步骤5：距离分析
2025-04-24 05:03:45,274 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-04-24 05:03:45,275 - distance_analyzer - INFO - 将分析1个区域: external
2025-04-24 05:03:45,279 - distance_analyzer - INFO - 分析external区域的消光-距离关系
2025-04-24 05:03:45,279 - distance_analyzer - INFO - 准备距离-消光数据，区域: external
2025-04-24 05:03:45,279 - distance_analyzer - INFO - 区域external中有1个有效源
2025-04-24 05:03:45,279 - distance_analyzer - WARNING - external区域有效数据点太少（1），跳过分析
2025-04-24 05:03:45,280 - distance_analyzer - INFO - 估计分子云距离
2025-04-24 05:03:45,280 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity, pdr
2025-04-24 05:03:45,280 - distance_analyzer - WARNING - 没有检测到任何跳变，无法估计距离
2025-04-24 05:03:45,280 - distance_analyzer - INFO - 保存分析结果到: data/output/batch_results\G000.003+00.127\G000.003+00.127_results.txt
2025-04-24 05:03:45,281 - distance_analyzer - INFO - 成功保存分析结果
2025-04-24 05:03:45,281 - main - INFO - 距离分析完成
2025-04-24 05:03:45,281 - main - INFO - 处理G000.003+00.127完成
