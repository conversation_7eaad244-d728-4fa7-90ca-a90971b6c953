"""
阈值法PDR检测模块

使用阈值法检测PDR区域。
"""

import numpy as np
from scipy import ndimage as ndi
from skimage import morphology
from astropy.coordinates import SkyCoord
import astropy.units as u

from src.utils.logger import setup_logger

# 设置日志记录器
logger = setup_logger(name='region_mapper.threshold')

def define_pdr_mask_threshold(processed_image, wcs, center_coord, r_eff,
                            threshold_factor=0.3, max_radius_factor=5.0, search_radius_factor=3.0):
    """
    使用阈值法定义PDR区域掩模

    Args:
        processed_image: 处理后的WISE图像
        wcs: 世界坐标系
        center_coord: 中心坐标 (SkyCoord对象)
        r_eff: 有效半径（度）
        threshold_factor: 阈值因子（相对于百分位数或峰值）
        max_radius_factor: 最大半径因子（相对于r_eff）
        search_radius_factor: PDR搜索半径因子（相对于r_eff），默认为3.0

    Returns:
        numpy.ndarray: PDR掩模（布尔数组）
    """
    try:
        # 获取中心坐标在图像中的像素位置
        center_x, center_y = wcs.world_to_pixel(center_coord)

        # 计算像素尺度
        test_offset = 1.0 / 3600.0  # 1角秒
        # 根据坐标系统类型创建测试坐标
        if center_coord.frame.name == 'galactic':
            test_coord = SkyCoord(l=center_coord.l + test_offset * u.degree,
                                 b=center_coord.b,
                                 frame='galactic')
        else:
            test_coord = SkyCoord(ra=center_coord.ra + test_offset * u.degree,
                                 dec=center_coord.dec,
                                 frame=center_coord.frame)
        test_x, test_y = wcs.world_to_pixel(test_coord)
        pixel_scale = np.sqrt((test_x - center_x)**2 + (test_y - center_y)**2) * 3600  # 像素/角秒

        # 计算有效半径和最大半径（像素）
        r_eff_pixels = r_eff * 3600 / pixel_scale
        max_radius_pixels = r_eff * max_radius_factor * 3600 / pixel_scale
        pdr_search_radius_pixels = r_eff * search_radius_factor * 3600 / pixel_scale

        # 创建距离图像
        y, x = np.ogrid[:processed_image.shape[0], :processed_image.shape[1]]
        distance_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)

        # 限制PDR搜索区域在指定半径内
        pdr_search_mask = distance_from_center <= pdr_search_radius_pixels

        # 计算阈值
        # 使用低强度区域的第10百分位数作为背景估计
        background_mask = (distance_from_center <= max_radius_pixels) & (processed_image > 0)
        if np.sum(background_mask) > 0:
            background = np.percentile(processed_image[background_mask], 10)
        else:
            background = 0

        # 计算阈值
        threshold = background + threshold_factor * (np.max(processed_image) - background)

        # 创建PDR掩模
        pdr_mask = (processed_image >= threshold) & pdr_search_mask

        # 应用形态学操作，填充孔洞并平滑边界
        pdr_mask = morphology.binary_closing(pdr_mask, morphology.disk(5))
        pdr_mask = ndi.binary_fill_holes(pdr_mask)

        logger.info(f"阈值法PDR掩模创建完成，覆盖{np.sum(pdr_mask)}个像素")
        return pdr_mask

    except Exception as e:
        logger.error(f"使用阈值法定义PDR掩模失败: {str(e)}")
        # 出错时返回空掩模
        return np.zeros_like(processed_image, dtype=bool)
