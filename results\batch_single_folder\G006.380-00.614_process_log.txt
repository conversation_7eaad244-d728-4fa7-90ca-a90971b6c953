处理时间: 2025-05-20 09:51:54
耗时: 19.04秒

标准输出:

未能估计G006.380-00.614的分子云距离


标准错误:
2025-05-20 09:51:39,022 - main_single_folder - INFO - 输出目录: results/batch_single_folder
2025-05-20 09:51:39,023 - main_single_folder - INFO - 加载源G006.380-00.614的信息
2025-05-20 09:51:39,023 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
2025-05-20 09:51:39,029 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-05-20 09:51:39,030 - main_single_folder - INFO - 从源名解析银道坐标: l=6.380000, b=-0.614000
2025-05-20 09:51:39,036 - main_single_folder - INFO - 银道坐标转换为赤道坐标: RA=270.605224, Dec=-23.746822 (ICRS)
2025-05-20 09:51:39,036 - main_single_folder - INFO - 从源表加载信息: RA=270.605224, Dec=-23.746822, R_eff=0.021667度
2025-05-20 09:51:39,037 - main_single_folder - INFO - 步骤1：加载WISE数据
2025-05-20 09:51:39,042 - data_manager - INFO - 为源G006.380-00.614加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.380-00.614
2025-05-20 09:51:39,060 - data_manager - INFO - 目录中的所有文件: ['G006.380-00.614_ATLASGAL_870um.fits', 'G006.380-00.614_IRIS_100.fits', 'G006.380-00.614_MIPSGAL_24um.fits', 'G006.380-00.614_NVSS.fits', 'G006.380-00.614_WISE_12.fits', 'G006.380-00.614_WISE_22.fits', 'G006.380-00.614_WISE_3.4.fits', 'G006.380-00.614_WISE_4.6.fits']
2025-05-20 09:51:39,061 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:51:39,061 - data_manager - INFO - 第一次匹配结果: ['G006.380-00.614_WISE_12.fits']
2025-05-20 09:51:39,061 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.380-00.614\G006.380-00.614_WISE_12.fits
2025-05-20 09:51:39,107 - data_manager - INFO - FITS数据统计: 最小值=1382.20654296875, 最大值=7715.8427734375, 均值=1953.4688720703125, 中位数=1889.681884765625
2025-05-20 09:51:39,107 - data_manager - INFO - 有效数据点数量: 33837/33856 (99.94%)
2025-05-20 09:51:39,114 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-05-20 09:51:39,114 - main_single_folder - INFO - WISE数据加载完成
2025-05-20 09:51:39,115 - main_single_folder - INFO - 步骤2：区域映射
2025-05-20 09:51:39,116 - main_single_folder - INFO - 使用W3/W4波段比值方法定义PDR区域
2025-05-20 09:51:39,116 - region_mapper - INFO - 使用W3/W4波段比值方法定义PDR掩模
2025-05-20 09:51:39,116 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:51:39,116 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:51:39,117 - region_mapper.ratio - INFO - 加载源G006.380-00.614的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:51:39,117 - region_mapper.ratio - INFO - 加载源G006.380-00.614的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:51:39,117 - data_manager - INFO - 为源G006.380-00.614加载多波段WISE数据: ('12', '22')
2025-05-20 09:51:39,121 - data_manager - INFO - 为源G006.380-00.614加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.380-00.614
2025-05-20 09:51:39,122 - data_manager - INFO - 目录中的所有文件: ['G006.380-00.614_ATLASGAL_870um.fits', 'G006.380-00.614_IRIS_100.fits', 'G006.380-00.614_MIPSGAL_24um.fits', 'G006.380-00.614_NVSS.fits', 'G006.380-00.614_WISE_12.fits', 'G006.380-00.614_WISE_22.fits', 'G006.380-00.614_WISE_3.4.fits', 'G006.380-00.614_WISE_4.6.fits']
2025-05-20 09:51:39,122 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:51:39,122 - data_manager - INFO - 第一次匹配结果: ['G006.380-00.614_WISE_12.fits']
2025-05-20 09:51:39,122 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.380-00.614\G006.380-00.614_WISE_12.fits
2025-05-20 09:51:39,125 - data_manager - INFO - FITS数据统计: 最小值=1382.20654296875, 最大值=7715.8427734375, 均值=1953.4688720703125, 中位数=1889.681884765625
2025-05-20 09:51:39,125 - data_manager - INFO - 有效数据点数量: 33837/33856 (99.94%)
2025-05-20 09:51:39,197 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-05-20 09:51:39,198 - data_manager - INFO - 使用12μm波段的WCS作为参考
2025-05-20 09:51:39,198 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (184, 184)
2025-05-20 09:51:39,203 - data_manager - INFO - 为源G006.380-00.614加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.380-00.614
2025-05-20 09:51:39,204 - data_manager - INFO - 目录中的所有文件: ['G006.380-00.614_ATLASGAL_870um.fits', 'G006.380-00.614_IRIS_100.fits', 'G006.380-00.614_MIPSGAL_24um.fits', 'G006.380-00.614_NVSS.fits', 'G006.380-00.614_WISE_12.fits', 'G006.380-00.614_WISE_22.fits', 'G006.380-00.614_WISE_3.4.fits', 'G006.380-00.614_WISE_4.6.fits']
2025-05-20 09:51:39,204 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:51:39,204 - data_manager - INFO - 第一次匹配结果: ['G006.380-00.614_WISE_22.fits']
2025-05-20 09:51:39,204 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.380-00.614\G006.380-00.614_WISE_22.fits
2025-05-20 09:51:39,243 - data_manager - INFO - FITS数据统计: 最小值=339.3858337402344, 最大值=1626.656005859375, 均值=393.0650634765625, 中位数=379.1375732421875
2025-05-20 09:51:39,243 - data_manager - INFO - 有效数据点数量: 10000/10000 (100.00%)
2025-05-20 09:51:39,250 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (100, 100)
2025-05-20 09:51:39,250 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (100, 100)
2025-05-20 09:51:39,250 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w3', 'w4']
2025-05-20 09:51:39,251 - region_mapper.ratio - INFO - 调整W4波段数据从(100, 100)到(184, 184)
2025-05-20 09:51:39,251 - region_mapper.ratio - INFO - 调整W4波段数据从(100, 100)到(184, 184)
2025-05-20 09:51:39,259 - region_mapper.ratio - INFO - W3/W4比值范围: 3.95-7.27，均值: 4.96
2025-05-20 09:51:39,259 - region_mapper.ratio - INFO - W3/W4比值范围: 3.95-7.27，均值: 4.96
2025-05-20 09:51:39,265 - region_mapper.ratio - INFO - 像素尺度: 505.26 像素/角秒
2025-05-20 09:51:39,265 - region_mapper.ratio - INFO - 像素尺度: 505.26 像素/角秒
2025-05-20 09:51:39,266 - region_mapper.ratio - INFO - 有效半径: 0.2 像素
2025-05-20 09:51:39,266 - region_mapper.ratio - INFO - 有效半径: 0.2 像素
2025-05-20 09:51:39,266 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:51:39,266 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:51:39,993 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.588
2025-05-20 09:51:39,993 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.588
2025-05-20 09:51:40,075 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.587
2025-05-20 09:51:40,075 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.587
2025-05-20 09:51:40,156 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.591
2025-05-20 09:51:40,156 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.591
2025-05-20 09:51:40,157 - region_mapper.ratio - INFO - 使用最佳聚类数: 4
2025-05-20 09:51:40,157 - region_mapper.ratio - INFO - 使用最佳聚类数: 4
2025-05-20 09:51:40,232 - region_mapper.ratio - INFO - 聚类 0: 平均比值=4.59±0.05, 大小=107, 平均距离=7.1, 紧凑性=6.8
2025-05-20 09:51:40,232 - region_mapper.ratio - INFO - 聚类 0: 平均比值=4.59±0.05, 大小=107, 平均距离=7.1, 紧凑性=6.8
2025-05-20 09:51:40,233 - region_mapper.ratio - INFO - 聚类 1: 平均比值=4.81±0.06, 大小=49, 平均距离=8.1, 紧凑性=3.3
2025-05-20 09:51:40,233 - region_mapper.ratio - INFO - 聚类 1: 平均比值=4.81±0.06, 大小=49, 平均距离=8.1, 紧凑性=3.3
2025-05-20 09:51:40,233 - region_mapper.ratio - INFO - 聚类 2: 平均比值=4.45±0.03, 大小=113, 平均距离=6.8, 紧凑性=5.6
2025-05-20 09:51:40,233 - region_mapper.ratio - INFO - 聚类 2: 平均比值=4.45±0.03, 大小=113, 平均距离=6.8, 紧凑性=5.6
2025-05-20 09:51:40,234 - region_mapper.ratio - INFO - 聚类 3: 平均比值=4.32±0.05, 大小=44, 平均距离=3.4, 紧凑性=2.8
2025-05-20 09:51:40,234 - region_mapper.ratio - INFO - 聚类 3: 平均比值=4.32±0.05, 大小=44, 平均距离=3.4, 紧凑性=2.8
2025-05-20 09:51:40,234 - region_mapper.ratio - INFO - 选择聚类 2 作为PDR区域
2025-05-20 09:51:40,234 - region_mapper.ratio - INFO - 选择聚类 2 作为PDR区域
2025-05-20 09:51:40,239 - region_mapper.ratio - INFO - PDR掩模覆盖180个像素
2025-05-20 09:51:40,239 - region_mapper.ratio - INFO - PDR掩模覆盖180个像素
2025-05-20 09:51:42,880 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G006.380-00.614_w3w4_ratio.png
2025-05-20 09:51:42,880 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G006.380-00.614_w3w4_ratio.png
2025-05-20 09:51:42,880 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖180个像素
2025-05-20 09:51:42,880 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖180个像素
2025-05-20 09:51:42,902 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G006.380-00.614_region_masks.fits
2025-05-20 09:51:42,902 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G006.380-00.614_region_masks.fits
2025-05-20 09:51:42,907 - data_manager - INFO - 为源G006.380-00.614加载Gaia数据，中心坐标: RA=270.605224, Dec=-23.746822, 半径=0.1083度
2025-05-20 09:51:42,910 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances-Data/gaia_data\gaia_G006.380-00.614_10arcmin_min10.csv
2025-05-20 09:51:43,241 - data_manager - INFO - 成功加载Gaia数据，共14525条记录
2025-05-20 09:51:43,261 - data_manager - INFO - 成功加载Gaia数据，共5976个源在搜索半径内
2025-05-20 09:51:43,280 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.380-00.614_gaia_regions.fits
2025-05-20 09:51:45,150 - data_manager - WARNING - 检测到可能导致保存问题的列: ['region']
2025-05-20 09:51:45,171 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:51:45,342 - data_manager - INFO - 成功保存数据
2025-05-20 09:51:45,343 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:51:45,344 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:51:45,344 - main_single_folder - INFO - 步骤3：恒星选择
2025-05-20 09:51:45,349 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-05-20 09:51:45,351 - star_selector - INFO - 视差信噪比筛选: 保留726/5976个源
2025-05-20 09:51:45,352 - star_selector - INFO - RUWE筛选: 保留5814/5976个源
2025-05-20 09:51:45,365 - star_selector - INFO - 质量筛选详细信息:
2025-05-20 09:51:45,365 - star_selector - INFO -   视差信噪比阈值: 5.0
2025-05-20 09:51:45,365 - star_selector - INFO -   RUWE阈值: 1.4
2025-05-20 09:51:45,368 - star_selector - INFO -   视差信噪比筛选通过率: 14.5%
2025-05-20 09:51:45,369 - star_selector - INFO -   RUWE筛选通过率: 116.1%
2025-05-20 09:51:45,370 - star_selector - INFO -   总体通过率: 11.4%
2025-05-20 09:51:45,370 - star_selector - INFO - 质量筛选完成，保留682/5976个源
2025-05-20 09:51:45,370 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.380-00.614_filtered_data.fits
2025-05-20 09:51:45,530 - data_manager - WARNING - 检测到可能导致保存问题的列: ['region']
2025-05-20 09:51:45,548 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:51:45,644 - data_manager - INFO - 成功保存数据
2025-05-20 09:51:45,645 - main_single_folder - INFO - 保存质量筛选后的数据到: results/batch_single_folder\processed\G006.380-00.614_filtered_data.fits
2025-05-20 09:51:45,650 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-05-20 09:51:45,650 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-05-20 09:51:45,650 - star_selector - INFO - 筛选恒星样本，原始样本大小: 682
2025-05-20 09:51:45,650 - star_selector - INFO - 排除YSO后: 682/682个源
2025-05-20 09:51:45,659 - star_selector - INFO - 区域 cavity: 0个源
2025-05-20 09:51:45,659 - star_selector - INFO - 区域 pdr: 8个源
2025-05-20 09:51:45,659 - star_selector - INFO - 区域 external: 0个源
2025-05-20 09:51:45,659 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 682
2025-05-20 09:51:45,660 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.380-00.614_star_sample.fits
2025-05-20 09:51:45,817 - data_manager - WARNING - 检测到可能导致保存问题的列: ['region']
2025-05-20 09:51:45,836 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:51:45,931 - data_manager - INFO - 成功保存数据
2025-05-20 09:51:45,932 - main_single_folder - INFO - 恒星选择完成
2025-05-20 09:51:45,932 - main_single_folder - INFO - 步骤4：消光计算
2025-05-20 09:51:45,937 - extinction_estimator - INFO - 为682个恒星计算A_V，参数: {'rv': 3.1}
2025-05-20 09:51:45,955 - extinction_estimator - INFO - 使用2MASS H-K颜色计算A_V
2025-05-20 09:51:45,955 - extinction_estimator - INFO - 估计本征H-K颜色，方法: select_giants
2025-05-20 09:51:45,955 - extinction_estimator - INFO - 使用红巨星的典型H-K颜色: 0.15
2025-05-20 09:51:45,956 - extinction_estimator - INFO - 基于H-K颜色过量计算A_V，R_V=3.1
2025-05-20 09:51:45,957 - extinction_estimator - INFO - 计算得到616个A_V值，范围: 0.00-43.02
2025-05-20 09:51:45,957 - extinction_estimator - INFO - 使用Gaia BP-RP颜色计算A_V
2025-05-20 09:51:45,958 - extinction_estimator - INFO - 估计本征BP-RP颜色，方法: fixed_value
2025-05-20 09:51:45,958 - extinction_estimator - INFO - 使用固定的BP-RP颜色: 0.8
2025-05-20 09:51:45,958 - extinction_estimator - INFO - 基于Gaia BP-RP颜色过量计算A_V，R_V=3.1
2025-05-20 09:51:45,959 - extinction_estimator - INFO - 计算得到668个A_V值，范围: 0.00-7.94
2025-05-20 09:51:45,960 - extinction_estimator - INFO - 方法 2MASS_HK: 616个源
2025-05-20 09:51:45,960 - extinction_estimator - INFO - 方法 Gaia_BPRP: 57个源
2025-05-20 09:51:45,960 - extinction_estimator - INFO - 方法 unknown: 9个源
2025-05-20 09:51:45,961 - extinction_estimator - INFO - A_V计算完成，统计信息:
2025-05-20 09:51:45,961 - extinction_estimator - INFO -   范围: 0.00-43.02 mag
2025-05-20 09:51:45,961 - extinction_estimator - INFO -   均值: 5.05 mag
2025-05-20 09:51:45,962 - extinction_estimator - INFO -   中位数: 2.59 mag
2025-05-20 09:51:45,962 - extinction_estimator - INFO -   标准差: 6.60 mag
2025-05-20 09:51:45,962 - extinction_estimator - INFO - 消光计算方法统计:
2025-05-20 09:51:45,962 - extinction_estimator - INFO -   2MASS_HK: 616颗恒星 (90.3%)
2025-05-20 09:51:45,963 - extinction_estimator - INFO -   Gaia_BPRP: 57颗恒星 (8.4%)
2025-05-20 09:51:45,963 - extinction_estimator - INFO -   unknown: 9颗恒星 (1.3%)
2025-05-20 09:51:45,963 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.380-00.614_stars_with_av.fits
2025-05-20 09:51:46,124 - data_manager - WARNING - 检测到可能导致保存问题的列: ['region', 'av_method']
2025-05-20 09:51:46,141 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:51:46,247 - data_manager - INFO - 成功保存数据
2025-05-20 09:51:46,247 - main_single_folder - INFO - 消光计算完成
2025-05-20 09:51:46,247 - main_single_folder - INFO - 步骤5：距离分析
2025-05-20 09:51:46,247 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-05-20 09:51:46,248 - distance_analyzer - INFO - 将分析1个区域: pdr
2025-05-20 09:51:46,253 - distance_analyzer - INFO - 分析pdr区域的消光-距离关系
2025-05-20 09:51:46,253 - distance_analyzer - INFO - 准备距离-消光数据，区域: pdr
2025-05-20 09:51:46,253 - distance_analyzer - INFO - 区域pdr中有8个有效源
2025-05-20 09:51:46,254 - distance_analyzer - INFO - pdr区域统计信息:
2025-05-20 09:51:46,254 - distance_analyzer - INFO -   总恒星数: 682
2025-05-20 09:51:46,254 - distance_analyzer - INFO -   区域内恒星数: 8 (1.2% 的总数)
2025-05-20 09:51:46,254 - distance_analyzer - INFO -   有效数据点: 8 (100.0% 的区域内恒星)
2025-05-20 09:51:46,254 - distance_analyzer - WARNING - pdr区域有效数据点太少（8 < 10），跳过分析
2025-05-20 09:51:46,255 - distance_analyzer - INFO - 估计分子云距离
2025-05-20 09:51:46,255 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity, external
2025-05-20 09:51:46,255 - distance_analyzer - WARNING - 没有检测到任何跳变，无法估计距离
2025-05-20 09:51:46,255 - main_single_folder - INFO - 距离分析完成
2025-05-20 09:51:46,256 - main_single_folder - INFO - 步骤6：可视化和报告生成
2025-05-20 09:51:46,260 - data_manager - INFO - 为源G006.380-00.614加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.380-00.614
2025-05-20 09:51:46,261 - data_manager - INFO - 目录中的所有文件: ['G006.380-00.614_ATLASGAL_870um.fits', 'G006.380-00.614_IRIS_100.fits', 'G006.380-00.614_MIPSGAL_24um.fits', 'G006.380-00.614_NVSS.fits', 'G006.380-00.614_WISE_12.fits', 'G006.380-00.614_WISE_22.fits', 'G006.380-00.614_WISE_3.4.fits', 'G006.380-00.614_WISE_4.6.fits']
2025-05-20 09:51:46,261 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:51:46,261 - data_manager - INFO - 第一次匹配结果: ['G006.380-00.614_WISE_12.fits']
2025-05-20 09:51:46,261 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.380-00.614\G006.380-00.614_WISE_12.fits
2025-05-20 09:51:46,264 - data_manager - INFO - FITS数据统计: 最小值=1382.20654296875, 最大值=7715.8427734375, 均值=1953.4688720703125, 中位数=1889.681884765625
2025-05-20 09:51:46,264 - data_manager - INFO - 有效数据点数量: 33837/33856 (99.94%)
2025-05-20 09:51:46,271 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-05-20 09:51:46,275 - visualizer - INFO - 绘制WISE图像和区域掩模，输出到: results/batch_single_folder\visualizations\G006.380-00.614_wise_regions_stars.png
2025-05-20 09:51:46,386 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:51:48,382 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:51:48,390 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:51:50,198 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G006.380-00.614_wise_regions_stars.png
2025-05-20 09:51:50,199 - main_single_folder - INFO - 成功保存WISE图像和区域掩模到: results/batch_single_folder\visualizations\G006.380-00.614_wise_regions_stars.png
2025-05-20 09:51:50,199 - main_single_folder - INFO - 加载WISE多波段数据用于RGB图像生成
2025-05-20 09:51:50,199 - data_manager - INFO - 为源G006.380-00.614加载多波段WISE数据: ('3.4', '12', '22')
2025-05-20 09:51:50,204 - data_manager - INFO - 为源G006.380-00.614加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.380-00.614
2025-05-20 09:51:50,204 - data_manager - INFO - 目录中的所有文件: ['G006.380-00.614_ATLASGAL_870um.fits', 'G006.380-00.614_IRIS_100.fits', 'G006.380-00.614_MIPSGAL_24um.fits', 'G006.380-00.614_NVSS.fits', 'G006.380-00.614_WISE_12.fits', 'G006.380-00.614_WISE_22.fits', 'G006.380-00.614_WISE_3.4.fits', 'G006.380-00.614_WISE_4.6.fits']
2025-05-20 09:51:50,205 - data_manager - INFO - 查找WISE 3.4μm波段的FITS文件
2025-05-20 09:51:50,205 - data_manager - INFO - 第一次匹配结果: ['G006.380-00.614_WISE_3.4.fits']
2025-05-20 09:51:50,205 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.380-00.614\G006.380-00.614_WISE_3.4.fits
2025-05-20 09:51:50,237 - data_manager - INFO - FITS数据统计: 最小值=32.832855224609375, 最大值=4188.72314453125, 均值=220.01651000976562, 中位数=156.01043701171875
2025-05-20 09:51:50,237 - data_manager - INFO - 有效数据点数量: 38406/38416 (99.97%)
2025-05-20 09:51:50,244 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (196, 196)
2025-05-20 09:51:50,244 - data_manager - INFO - 使用3.4μm波段的WCS作为参考
2025-05-20 09:51:50,244 - data_manager - INFO - 成功加载WISE 3.4μm波段数据，尺寸: (196, 196)
2025-05-20 09:51:50,250 - data_manager - INFO - 为源G006.380-00.614加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.380-00.614
2025-05-20 09:51:50,250 - data_manager - INFO - 目录中的所有文件: ['G006.380-00.614_ATLASGAL_870um.fits', 'G006.380-00.614_IRIS_100.fits', 'G006.380-00.614_MIPSGAL_24um.fits', 'G006.380-00.614_NVSS.fits', 'G006.380-00.614_WISE_12.fits', 'G006.380-00.614_WISE_22.fits', 'G006.380-00.614_WISE_3.4.fits', 'G006.380-00.614_WISE_4.6.fits']
2025-05-20 09:51:50,250 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:51:50,250 - data_manager - INFO - 第一次匹配结果: ['G006.380-00.614_WISE_12.fits']
2025-05-20 09:51:50,250 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.380-00.614\G006.380-00.614_WISE_12.fits
2025-05-20 09:51:50,253 - data_manager - INFO - FITS数据统计: 最小值=1382.20654296875, 最大值=7715.8427734375, 均值=1953.4688720703125, 中位数=1889.681884765625
2025-05-20 09:51:50,253 - data_manager - INFO - 有效数据点数量: 33837/33856 (99.94%)
2025-05-20 09:51:50,260 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (184, 184)
2025-05-20 09:51:50,261 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (184, 184)
2025-05-20 09:51:50,266 - data_manager - INFO - 为源G006.380-00.614加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.380-00.614
2025-05-20 09:51:50,266 - data_manager - INFO - 目录中的所有文件: ['G006.380-00.614_ATLASGAL_870um.fits', 'G006.380-00.614_IRIS_100.fits', 'G006.380-00.614_MIPSGAL_24um.fits', 'G006.380-00.614_NVSS.fits', 'G006.380-00.614_WISE_12.fits', 'G006.380-00.614_WISE_22.fits', 'G006.380-00.614_WISE_3.4.fits', 'G006.380-00.614_WISE_4.6.fits']
2025-05-20 09:51:50,266 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:51:50,266 - data_manager - INFO - 第一次匹配结果: ['G006.380-00.614_WISE_22.fits']
2025-05-20 09:51:50,267 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.380-00.614\G006.380-00.614_WISE_22.fits
2025-05-20 09:51:50,269 - data_manager - INFO - FITS数据统计: 最小值=339.3858337402344, 最大值=1626.656005859375, 均值=393.0650634765625, 中位数=379.1375732421875
2025-05-20 09:51:50,269 - data_manager - INFO - 有效数据点数量: 10000/10000 (100.00%)
2025-05-20 09:51:50,276 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (100, 100)
2025-05-20 09:51:50,276 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (100, 100)
2025-05-20 09:51:50,276 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w1', 'w3', 'w4']
2025-05-20 09:51:50,277 - visualizer - INFO - 创建WISE三波段RGB图像，输出到: results/batch_single_folder\visualizations\G006.380-00.614_wise_rgb.png
2025-05-20 09:51:50,409 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:51:50,410 - visualizer - INFO - 波段 3.4μm 的尺寸: (196, 196)
2025-05-20 09:51:50,410 - visualizer - INFO - 波段 12μm 的尺寸: (184, 184)
2025-05-20 09:51:50,410 - visualizer - INFO - 波段 22μm 的尺寸: (100, 100)
2025-05-20 09:51:50,410 - visualizer - INFO - Using 12μm band size as target: (184, 184)
2025-05-20 09:51:50,410 - visualizer - INFO - Using target image size: (184, 184), original sizes: {'w1': (196, 196), 'w3': (184, 184), 'w4': (100, 100)}
2025-05-20 09:51:50,415 - visualizer - INFO - 调整w4波段数据从(100, 100)到(184, 184)
2025-05-20 09:51:50,416 - visualizer - INFO - 红色通道(22μm)数据范围: 339.3858337402344-1622.0687255859375
2025-05-20 09:51:50,416 - visualizer - INFO - 绿色通道(12μm)数据范围: 1382.20654296875-7715.8427734375
2025-05-20 09:51:50,423 - visualizer - INFO - 调整w1波段数据从(196, 196)到(184, 184)
2025-05-20 09:51:50,423 - visualizer - INFO - 蓝色通道(3.4μm)数据范围: 24.078672409057617-4188.72314453125
2025-05-20 09:51:50,425 - visualizer - INFO - 使用Lupton RGB方法，stretch=1899.62, Q=10
2025-05-20 09:51:50,425 - visualizer - INFO - 各波段99.5%百分位数: R=646.07, G=3799.24, B=1499.64
2025-05-20 09:51:50,430 - visualizer - INFO - RGB数据形状: (184, 184, 3), 类型: float32
2025-05-20 09:51:50,430 - visualizer - INFO - Red通道数据范围：0.062745101749897-0.49803921580314636，均值：0.1137
2025-05-20 09:51:50,431 - visualizer - INFO - Green通道数据范围：0.0-0.9686274528503418，均值：0.5668
2025-05-20 09:51:50,431 - visualizer - INFO - Blue通道数据范围：0.0-0.7921568751335144，均值：0.0597
2025-05-20 09:51:51,867 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:51:51,873 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:51:51,879 - visualizer - INFO - 按照要求不显示Gaia星
2025-05-20 09:51:51,880 - visualizer - INFO - 设置坐标刻度间隔: RA=10.000度, Dec=10.000度
2025-05-20 09:51:52,783 - visualizer - INFO - 成功保存RGB图像到: results/batch_single_folder\visualizations\G006.380-00.614_wise_rgb.png
2025-05-20 09:51:52,783 - main_single_folder - INFO - 成功保存WISE RGB图像到: results/batch_single_folder\visualizations\G006.380-00.614_wise_rgb.png
2025-05-20 09:51:52,783 - visualizer - INFO - 绘制消光-距离散点图，输出到: results/batch_single_folder\visualizations\G006.380-00.614_extinction_distance.png
2025-05-20 09:51:54,203 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G006.380-00.614_extinction_distance.png
2025-05-20 09:51:54,204 - main_single_folder - INFO - 成功保存消光-距离散点图到: results/batch_single_folder\visualizations\G006.380-00.614_extinction_distance.png
2025-05-20 09:51:54,279 - main_single_folder - INFO - 成功保存处理报告到: results/batch_single_folder\reports\G006.380-00.614_report.txt
2025-05-20 09:51:54,280 - main_single_folder - INFO - 处理G006.380-00.614完成
