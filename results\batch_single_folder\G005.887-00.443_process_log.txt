处理时间: 2025-05-20 09:44:14
耗时: 77.23秒

标准输出:

未能估计G005.887-00.443的分子云距离


标准错误:
2025-05-20 09:43:00,332 - main_single_folder - INFO - 输出目录: results/batch_single_folder
2025-05-20 09:43:00,333 - main_single_folder - INFO - 加载源G005.887-00.443的信息
2025-05-20 09:43:00,333 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
2025-05-20 09:43:00,339 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-05-20 09:43:00,340 - main_single_folder - INFO - 从源名解析银道坐标: l=5.887000, b=-0.443000
2025-05-20 09:43:00,346 - main_single_folder - INFO - 银道坐标转换为赤道坐标: RA=270.175840, Dec=-24.090633 (ICRS)
2025-05-20 09:43:00,346 - main_single_folder - INFO - 从源表加载信息: RA=270.175840, Dec=-24.090633, R_eff=0.121111度
2025-05-20 09:43:00,347 - main_single_folder - INFO - 步骤1：加载WISE数据
2025-05-20 09:43:00,352 - data_manager - INFO - 为源G005.887-00.443加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G005.887-00.443
2025-05-20 09:43:00,369 - data_manager - INFO - 目录中的所有文件: ['G005.887-00.443_ATLASGAL_870um.fits', 'G005.887-00.443_IRIS_100.fits', 'G005.887-00.443_MIPSGAL_24um.fits', 'G005.887-00.443_NVSS.fits', 'G005.887-00.443_WISE_12.fits', 'G005.887-00.443_WISE_22.fits', 'G005.887-00.443_WISE_3.4.fits', 'G005.887-00.443_WISE_4.6.fits']
2025-05-20 09:43:00,369 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:43:00,369 - data_manager - INFO - 第一次匹配结果: ['G005.887-00.443_WISE_12.fits']
2025-05-20 09:43:00,370 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G005.887-00.443\G005.887-00.443_WISE_12.fits
2025-05-20 09:43:00,441 - data_manager - INFO - FITS数据统计: 最小值=1115.14111328125, 最大值=9913.275390625, 均值=1737.1341552734375, 中位数=1638.1685791015625
2025-05-20 09:43:00,441 - data_manager - INFO - 有效数据点数量: 448679/448900 (99.95%)
2025-05-20 09:43:00,449 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (670, 670)
2025-05-20 09:43:00,449 - main_single_folder - INFO - WISE数据加载完成
2025-05-20 09:43:00,449 - main_single_folder - INFO - 步骤2：区域映射
2025-05-20 09:43:00,465 - main_single_folder - INFO - 使用W3/W4波段比值方法定义PDR区域
2025-05-20 09:43:00,465 - region_mapper - INFO - 使用W3/W4波段比值方法定义PDR掩模
2025-05-20 09:43:00,465 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:43:00,465 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:43:00,465 - region_mapper.ratio - INFO - 加载源G005.887-00.443的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:43:00,465 - region_mapper.ratio - INFO - 加载源G005.887-00.443的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:43:00,465 - data_manager - INFO - 为源G005.887-00.443加载多波段WISE数据: ('12', '22')
2025-05-20 09:43:00,470 - data_manager - INFO - 为源G005.887-00.443加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G005.887-00.443
2025-05-20 09:43:00,471 - data_manager - INFO - 目录中的所有文件: ['G005.887-00.443_ATLASGAL_870um.fits', 'G005.887-00.443_IRIS_100.fits', 'G005.887-00.443_MIPSGAL_24um.fits', 'G005.887-00.443_NVSS.fits', 'G005.887-00.443_WISE_12.fits', 'G005.887-00.443_WISE_22.fits', 'G005.887-00.443_WISE_3.4.fits', 'G005.887-00.443_WISE_4.6.fits']
2025-05-20 09:43:00,471 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:43:00,471 - data_manager - INFO - 第一次匹配结果: ['G005.887-00.443_WISE_12.fits']
2025-05-20 09:43:00,471 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G005.887-00.443\G005.887-00.443_WISE_12.fits
2025-05-20 09:43:00,482 - data_manager - INFO - FITS数据统计: 最小值=1115.14111328125, 最大值=9913.275390625, 均值=1737.1341552734375, 中位数=1638.1685791015625
2025-05-20 09:43:00,482 - data_manager - INFO - 有效数据点数量: 448679/448900 (99.95%)
2025-05-20 09:43:00,556 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (670, 670)
2025-05-20 09:43:00,556 - data_manager - INFO - 使用12μm波段的WCS作为参考
2025-05-20 09:43:00,556 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (670, 670)
2025-05-20 09:43:00,562 - data_manager - INFO - 为源G005.887-00.443加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G005.887-00.443
2025-05-20 09:43:00,562 - data_manager - INFO - 目录中的所有文件: ['G005.887-00.443_ATLASGAL_870um.fits', 'G005.887-00.443_IRIS_100.fits', 'G005.887-00.443_MIPSGAL_24um.fits', 'G005.887-00.443_NVSS.fits', 'G005.887-00.443_WISE_12.fits', 'G005.887-00.443_WISE_22.fits', 'G005.887-00.443_WISE_3.4.fits', 'G005.887-00.443_WISE_4.6.fits']
2025-05-20 09:43:00,562 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:43:00,562 - data_manager - INFO - 第一次匹配结果: ['G005.887-00.443_WISE_22.fits']
2025-05-20 09:43:00,562 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G005.887-00.443\G005.887-00.443_WISE_22.fits
2025-05-20 09:43:00,609 - data_manager - INFO - FITS数据统计: 最小值=327.60797119140625, 最大值=1989.322021484375, 均值=366.20172119140625, 中位数=351.9062194824219
2025-05-20 09:43:00,610 - data_manager - INFO - 有效数据点数量: 131720/131769 (99.96%)
2025-05-20 09:43:00,617 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (363, 363)
2025-05-20 09:43:00,617 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (363, 363)
2025-05-20 09:43:00,617 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w3', 'w4']
2025-05-20 09:43:00,622 - region_mapper.ratio - INFO - 调整W4波段数据从(363, 363)到(670, 670)
2025-05-20 09:43:00,622 - region_mapper.ratio - INFO - 调整W4波段数据从(363, 363)到(670, 670)
2025-05-20 09:43:00,709 - region_mapper.ratio - INFO - W3/W4比值范围: 3.59-6.44，均值: 4.72
2025-05-20 09:43:00,709 - region_mapper.ratio - INFO - W3/W4比值范围: 3.59-6.44，均值: 4.72
2025-05-20 09:43:00,717 - region_mapper.ratio - INFO - 像素尺度: 505.03 像素/角秒
2025-05-20 09:43:00,717 - region_mapper.ratio - INFO - 像素尺度: 505.03 像素/角秒
2025-05-20 09:43:00,717 - region_mapper.ratio - INFO - 有效半径: 0.9 像素
2025-05-20 09:43:00,717 - region_mapper.ratio - INFO - 有效半径: 0.9 像素
2025-05-20 09:43:00,717 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:43:00,717 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:43:01,454 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.675
2025-05-20 09:43:01,454 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.675
2025-05-20 09:43:01,531 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.725
2025-05-20 09:43:01,531 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.725
2025-05-20 09:43:01,612 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.680
2025-05-20 09:43:01,612 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.680
2025-05-20 09:43:01,612 - region_mapper.ratio - INFO - 使用最佳聚类数: 3
2025-05-20 09:43:01,612 - region_mapper.ratio - INFO - 使用最佳聚类数: 3
2025-05-20 09:43:01,683 - region_mapper.ratio - INFO - 聚类 0: 平均比值=5.38±0.24, 大小=138, 平均距离=6.4, 紧凑性=6.1
2025-05-20 09:43:01,683 - region_mapper.ratio - INFO - 聚类 0: 平均比值=5.38±0.24, 大小=138, 平均距离=6.4, 紧凑性=6.1
2025-05-20 09:43:01,684 - region_mapper.ratio - INFO - 聚类 1: 平均比值=3.60±0.07, 大小=86, 平均距离=6.6, 紧凑性=3.7
2025-05-20 09:43:01,684 - region_mapper.ratio - INFO - 聚类 1: 平均比值=3.60±0.07, 大小=86, 平均距离=6.6, 紧凑性=3.7
2025-05-20 09:43:01,684 - region_mapper.ratio - INFO - 聚类 2: 平均比值=4.51±0.22, 大小=89, 平均距离=7.0, 紧凑性=5.3
2025-05-20 09:43:01,684 - region_mapper.ratio - INFO - 聚类 2: 平均比值=4.51±0.22, 大小=89, 平均距离=7.0, 紧凑性=5.3
2025-05-20 09:43:01,684 - region_mapper.ratio - INFO - 选择聚类 1 作为PDR区域
2025-05-20 09:43:01,684 - region_mapper.ratio - INFO - 选择聚类 1 作为PDR区域
2025-05-20 09:43:01,735 - region_mapper.ratio - INFO - PDR掩模覆盖86个像素
2025-05-20 09:43:01,735 - region_mapper.ratio - INFO - PDR掩模覆盖86个像素
2025-05-20 09:43:01,736 - region_mapper.ratio - WARNING - PDR区域太小 (86 < 100)，使用备用方法
2025-05-20 09:43:01,736 - region_mapper.ratio - WARNING - PDR区域太小 (86 < 100)，使用备用方法
2025-05-20 09:43:01,823 - region_mapper.ratio - INFO - 备用方法后PDR掩模覆盖258个像素
2025-05-20 09:43:01,823 - region_mapper.ratio - INFO - 备用方法后PDR掩模覆盖258个像素
2025-05-20 09:43:06,435 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G005.887-00.443_w3w4_ratio.png
2025-05-20 09:43:06,435 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G005.887-00.443_w3w4_ratio.png
2025-05-20 09:43:06,437 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖258个像素
2025-05-20 09:43:06,437 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖258个像素
2025-05-20 09:43:06,477 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G005.887-00.443_region_masks.fits
2025-05-20 09:43:06,477 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G005.887-00.443_region_masks.fits
2025-05-20 09:43:06,483 - data_manager - INFO - 为源G005.887-00.443加载Gaia数据，中心坐标: RA=270.175840, Dec=-24.090633, 半径=0.6056度
2025-05-20 09:43:06,485 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances-Data/gaia_data\gaia_G005.887-00.443_36arcmin_5R.csv
H:\Augment\Parallax distances\src\data_manager.py:87: DtypeWarning: Columns (47) have mixed types. Specify dtype option on import or set low_memory=False.
  df = pd.read_csv(gaia_file)
2025-05-20 09:43:09,440 - data_manager - INFO - 成功加载Gaia数据，共182136条记录
2025-05-20 09:43:09,608 - data_manager - INFO - 成功加载Gaia数据，共182136个源在搜索半径内
2025-05-20 09:43:10,050 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G005.887-00.443_gaia_regions.fits
2025-05-20 09:43:57,680 - data_manager - WARNING - 检测到可能导致保存问题的列: ['_2MASS', 'twomass__2MASS', 'region']
2025-05-20 09:43:57,763 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:44:01,372 - data_manager - ERROR - 保存数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U10'), dtype('<U9'), dtype('<U11'), dtype('<U1')}
2025-05-20 09:44:01,372 - main_single_folder - ERROR - 加载Gaia数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U10'), dtype('<U9'), dtype('<U11'), dtype('<U1')}
2025-05-20 09:44:01,372 - main_single_folder - WARNING - 将使用空的Gaia数据继续处理
2025-05-20 09:44:01,472 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:44:01,473 - main_single_folder - INFO - 步骤3：恒星选择
2025-05-20 09:44:01,478 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-05-20 09:44:01,479 - star_selector - WARNING - 没有有效的视差数据，跳过视差信噪比筛选
2025-05-20 09:44:01,479 - star_selector - WARNING - 没有有效的RUWE数据，跳过RUWE筛选
2025-05-20 09:44:01,479 - star_selector - INFO - 质量筛选详细信息:
2025-05-20 09:44:01,479 - star_selector - INFO -   视差信噪比阈值: 5.0
2025-05-20 09:44:01,480 - star_selector - INFO -   RUWE阈值: 1.4
2025-05-20 09:44:01,480 - star_selector - INFO -   总体通过率: 0.0%
2025-05-20 09:44:01,480 - star_selector - INFO - 质量筛选完成，保留0/0个源
2025-05-20 09:44:01,480 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G005.887-00.443_filtered_data.fits
2025-05-20 09:44:01,493 - data_manager - INFO - 成功保存数据
2025-05-20 09:44:01,494 - main_single_folder - INFO - 保存质量筛选后的数据到: results/batch_single_folder\processed\G005.887-00.443_filtered_data.fits
2025-05-20 09:44:01,498 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-05-20 09:44:01,498 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-05-20 09:44:01,499 - star_selector - INFO - 筛选恒星样本，原始样本大小: 0
2025-05-20 09:44:01,499 - star_selector - INFO - 排除YSO后: 0/0个源
2025-05-20 09:44:01,499 - star_selector - INFO - 区域 cavity: 0个源
2025-05-20 09:44:01,500 - star_selector - INFO - 区域 pdr: 0个源
2025-05-20 09:44:01,500 - star_selector - INFO - 区域 external: 0个源
2025-05-20 09:44:01,500 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 0
2025-05-20 09:44:01,500 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G005.887-00.443_star_sample.fits
2025-05-20 09:44:01,538 - data_manager - INFO - 成功保存数据
2025-05-20 09:44:01,538 - main_single_folder - INFO - 恒星选择完成
2025-05-20 09:44:01,539 - main_single_folder - INFO - 步骤4：消光计算
2025-05-20 09:44:01,543 - extinction_estimator - INFO - 为0个恒星计算A_V，参数: {'rv': 3.1}
2025-05-20 09:44:01,543 - extinction_estimator - WARNING - 星表为空，无法计算A_V
2025-05-20 09:44:01,544 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G005.887-00.443_stars_with_av.fits
2025-05-20 09:44:01,556 - data_manager - INFO - 成功保存数据
2025-05-20 09:44:01,556 - main_single_folder - INFO - 消光计算完成
2025-05-20 09:44:01,556 - main_single_folder - INFO - 步骤5：距离分析
2025-05-20 09:44:01,556 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-05-20 09:44:01,556 - distance_analyzer - INFO - 将分析0个区域: 
2025-05-20 09:44:01,557 - distance_analyzer - INFO - 估计分子云距离
2025-05-20 09:44:01,557 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity, pdr, external
2025-05-20 09:44:01,557 - distance_analyzer - WARNING - 没有检测到任何跳变，无法估计距离
2025-05-20 09:44:01,558 - main_single_folder - INFO - 距离分析完成
2025-05-20 09:44:01,558 - main_single_folder - INFO - 步骤6：可视化和报告生成
2025-05-20 09:44:01,563 - data_manager - INFO - 为源G005.887-00.443加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G005.887-00.443
2025-05-20 09:44:01,563 - data_manager - INFO - 目录中的所有文件: ['G005.887-00.443_ATLASGAL_870um.fits', 'G005.887-00.443_IRIS_100.fits', 'G005.887-00.443_MIPSGAL_24um.fits', 'G005.887-00.443_NVSS.fits', 'G005.887-00.443_WISE_12.fits', 'G005.887-00.443_WISE_22.fits', 'G005.887-00.443_WISE_3.4.fits', 'G005.887-00.443_WISE_4.6.fits']
2025-05-20 09:44:01,563 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:44:01,563 - data_manager - INFO - 第一次匹配结果: ['G005.887-00.443_WISE_12.fits']
2025-05-20 09:44:01,563 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G005.887-00.443\G005.887-00.443_WISE_12.fits
2025-05-20 09:44:01,576 - data_manager - INFO - FITS数据统计: 最小值=1115.14111328125, 最大值=9913.275390625, 均值=1737.1341552734375, 中位数=1638.1685791015625
2025-05-20 09:44:01,576 - data_manager - INFO - 有效数据点数量: 448679/448900 (99.95%)
2025-05-20 09:44:01,584 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (670, 670)
2025-05-20 09:44:01,605 - visualizer - INFO - 绘制WISE图像和区域掩模，输出到: results/batch_single_folder\visualizations\G005.887-00.443_wise_regions_stars.png
2025-05-20 09:44:01,719 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:44:03,786 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:44:03,793 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:44:07,461 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G005.887-00.443_wise_regions_stars.png
2025-05-20 09:44:07,461 - main_single_folder - INFO - 成功保存WISE图像和区域掩模到: results/batch_single_folder\visualizations\G005.887-00.443_wise_regions_stars.png
2025-05-20 09:44:07,462 - main_single_folder - INFO - 加载WISE多波段数据用于RGB图像生成
2025-05-20 09:44:07,462 - data_manager - INFO - 为源G005.887-00.443加载多波段WISE数据: ('3.4', '12', '22')
2025-05-20 09:44:07,467 - data_manager - INFO - 为源G005.887-00.443加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G005.887-00.443
2025-05-20 09:44:07,467 - data_manager - INFO - 目录中的所有文件: ['G005.887-00.443_ATLASGAL_870um.fits', 'G005.887-00.443_IRIS_100.fits', 'G005.887-00.443_MIPSGAL_24um.fits', 'G005.887-00.443_NVSS.fits', 'G005.887-00.443_WISE_12.fits', 'G005.887-00.443_WISE_22.fits', 'G005.887-00.443_WISE_3.4.fits', 'G005.887-00.443_WISE_4.6.fits']
2025-05-20 09:44:07,468 - data_manager - INFO - 查找WISE 3.4μm波段的FITS文件
2025-05-20 09:44:07,468 - data_manager - INFO - 第一次匹配结果: ['G005.887-00.443_WISE_3.4.fits']
2025-05-20 09:44:07,468 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G005.887-00.443\G005.887-00.443_WISE_3.4.fits
2025-05-20 09:44:07,565 - data_manager - INFO - FITS数据统计: 最小值=27.471908569335938, 最大值=4049.48681640625, 均值=232.5420379638672, 中位数=167.74859619140625
2025-05-20 09:44:07,565 - data_manager - INFO - 有效数据点数量: 509777/509796 (100.00%)
2025-05-20 09:44:07,573 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (714, 714)
2025-05-20 09:44:07,574 - data_manager - INFO - 使用3.4μm波段的WCS作为参考
2025-05-20 09:44:07,574 - data_manager - INFO - 成功加载WISE 3.4μm波段数据，尺寸: (714, 714)
2025-05-20 09:44:07,579 - data_manager - INFO - 为源G005.887-00.443加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G005.887-00.443
2025-05-20 09:44:07,579 - data_manager - INFO - 目录中的所有文件: ['G005.887-00.443_ATLASGAL_870um.fits', 'G005.887-00.443_IRIS_100.fits', 'G005.887-00.443_MIPSGAL_24um.fits', 'G005.887-00.443_NVSS.fits', 'G005.887-00.443_WISE_12.fits', 'G005.887-00.443_WISE_22.fits', 'G005.887-00.443_WISE_3.4.fits', 'G005.887-00.443_WISE_4.6.fits']
2025-05-20 09:44:07,579 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:44:07,580 - data_manager - INFO - 第一次匹配结果: ['G005.887-00.443_WISE_12.fits']
2025-05-20 09:44:07,580 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G005.887-00.443\G005.887-00.443_WISE_12.fits
2025-05-20 09:44:07,591 - data_manager - INFO - FITS数据统计: 最小值=1115.14111328125, 最大值=9913.275390625, 均值=1737.1341552734375, 中位数=1638.1685791015625
2025-05-20 09:44:07,591 - data_manager - INFO - 有效数据点数量: 448679/448900 (99.95%)
2025-05-20 09:44:07,599 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (670, 670)
2025-05-20 09:44:07,600 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (670, 670)
2025-05-20 09:44:07,605 - data_manager - INFO - 为源G005.887-00.443加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G005.887-00.443
2025-05-20 09:44:07,605 - data_manager - INFO - 目录中的所有文件: ['G005.887-00.443_ATLASGAL_870um.fits', 'G005.887-00.443_IRIS_100.fits', 'G005.887-00.443_MIPSGAL_24um.fits', 'G005.887-00.443_NVSS.fits', 'G005.887-00.443_WISE_12.fits', 'G005.887-00.443_WISE_22.fits', 'G005.887-00.443_WISE_3.4.fits', 'G005.887-00.443_WISE_4.6.fits']
2025-05-20 09:44:07,605 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:44:07,605 - data_manager - INFO - 第一次匹配结果: ['G005.887-00.443_WISE_22.fits']
2025-05-20 09:44:07,605 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G005.887-00.443\G005.887-00.443_WISE_22.fits
2025-05-20 09:44:07,609 - data_manager - INFO - FITS数据统计: 最小值=327.60797119140625, 最大值=1989.322021484375, 均值=366.20172119140625, 中位数=351.9062194824219
2025-05-20 09:44:07,610 - data_manager - INFO - 有效数据点数量: 131720/131769 (99.96%)
2025-05-20 09:44:07,617 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (363, 363)
2025-05-20 09:44:07,617 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (363, 363)
2025-05-20 09:44:07,618 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w1', 'w3', 'w4']
2025-05-20 09:44:07,618 - visualizer - INFO - 创建WISE三波段RGB图像，输出到: results/batch_single_folder\visualizations\G005.887-00.443_wise_rgb.png
2025-05-20 09:44:07,749 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:44:07,749 - visualizer - INFO - 波段 3.4μm 的尺寸: (714, 714)
2025-05-20 09:44:07,749 - visualizer - INFO - 波段 12μm 的尺寸: (670, 670)
2025-05-20 09:44:07,749 - visualizer - INFO - 波段 22μm 的尺寸: (363, 363)
2025-05-20 09:44:07,749 - visualizer - INFO - Using 12μm band size as target: (670, 670)
2025-05-20 09:44:07,750 - visualizer - INFO - Using target image size: (670, 670), original sizes: {'w1': (714, 714), 'w3': (670, 670), 'w4': (363, 363)}
2025-05-20 09:44:07,808 - visualizer - INFO - 调整w4波段数据从(363, 363)到(670, 670)
2025-05-20 09:44:07,809 - visualizer - INFO - 红色通道(22μm)数据范围: 1.1767855882644653-1989.322021484375
2025-05-20 09:44:07,814 - visualizer - INFO - 绿色通道(12μm)数据范围: 1115.14111328125-9913.275390625
2025-05-20 09:44:07,897 - visualizer - INFO - 调整w1波段数据从(714, 714)到(670, 670)
2025-05-20 09:44:07,898 - visualizer - INFO - 蓝色通道(3.4μm)数据范围: 27.73345947265625-3855.055908203125
2025-05-20 09:44:07,916 - visualizer - INFO - 使用Lupton RGB方法，stretch=2025.87, Q=10
2025-05-20 09:44:07,916 - visualizer - INFO - 各波段99.5%百分位数: R=723.88, G=4051.74, B=1430.96
2025-05-20 09:44:07,998 - visualizer - INFO - RGB数据形状: (670, 670, 3), 类型: float32
2025-05-20 09:44:08,001 - visualizer - INFO - Red通道数据范围：0.0-0.572549045085907，均值：0.1081
2025-05-20 09:44:08,003 - visualizer - INFO - Green通道数据范围：0.0-1.0，均值：0.5139
2025-05-20 09:44:08,005 - visualizer - INFO - Blue通道数据范围：0.0-0.7803921699523926，均值：0.0652
2025-05-20 09:44:09,500 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:44:09,507 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:44:09,572 - visualizer - INFO - 按照要求不显示Gaia星
2025-05-20 09:44:09,573 - visualizer - INFO - 设置坐标刻度间隔: RA=10.000度, Dec=10.000度
2025-05-20 09:44:13,758 - visualizer - INFO - 成功保存RGB图像到: results/batch_single_folder\visualizations\G005.887-00.443_wise_rgb.png
2025-05-20 09:44:13,759 - main_single_folder - INFO - 成功保存WISE RGB图像到: results/batch_single_folder\visualizations\G005.887-00.443_wise_rgb.png
2025-05-20 09:44:13,760 - main_single_folder - WARNING - 没有足够的数据绘制消光-距离散点图
2025-05-20 09:44:13,775 - main_single_folder - INFO - 成功保存处理报告到: results/batch_single_folder\reports\G005.887-00.443_report.txt
2025-05-20 09:44:13,775 - main_single_folder - INFO - 处理G005.887-00.443完成
