处理时间: 2025-05-20 09:42:56
耗时: 33.50秒

标准输出:

未能估计G003.826-01.048的分子云距离


标准错误:
2025-05-20 09:42:26,909 - main_single_folder - INFO - 输出目录: results/batch_single_folder
2025-05-20 09:42:26,910 - main_single_folder - INFO - 加载源G003.826-01.048的信息
2025-05-20 09:42:26,910 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
2025-05-20 09:42:26,915 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-05-20 09:42:26,917 - main_single_folder - INFO - 从源名解析银道坐标: l=3.826000, b=-1.048000
2025-05-20 09:42:26,923 - main_single_folder - INFO - 银道坐标转换为赤道坐标: RA=269.622686, Dec=-26.179235 (ICRS)
2025-05-20 09:42:26,923 - main_single_folder - INFO - 从源表加载信息: RA=269.622686, Dec=-26.179235, R_eff=0.044722度
2025-05-20 09:42:26,924 - main_single_folder - INFO - 步骤1：加载WISE数据
2025-05-20 09:42:26,928 - data_manager - INFO - 为源G003.826-01.048加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G003.826-01.048
2025-05-20 09:42:26,941 - data_manager - INFO - 目录中的所有文件: ['G003.826-01.048_ATLASGAL_870um.fits', 'G003.826-01.048_IRIS_100.fits', 'G003.826-01.048_NVSS.fits', 'G003.826-01.048_WISE_12.fits', 'G003.826-01.048_WISE_22.fits', 'G003.826-01.048_WISE_3.4.fits', 'G003.826-01.048_WISE_4.6.fits']
2025-05-20 09:42:26,941 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:42:26,941 - data_manager - INFO - 第一次匹配结果: ['G003.826-01.048_WISE_12.fits']
2025-05-20 09:42:26,942 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G003.826-01.048\G003.826-01.048_WISE_12.fits
2025-05-20 09:42:26,997 - data_manager - INFO - FITS数据统计: 最小值=1117.8555908203125, 最大值=5286.00390625, 均值=1306.8106689453125, 中位数=1282.0223388671875
2025-05-20 09:42:26,997 - data_manager - INFO - 有效数据点数量: 61005/61009 (99.99%)
2025-05-20 09:42:27,004 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (247, 247)
2025-05-20 09:42:27,005 - main_single_folder - INFO - WISE数据加载完成
2025-05-20 09:42:27,005 - main_single_folder - INFO - 步骤2：区域映射
2025-05-20 09:42:27,007 - main_single_folder - INFO - 使用W3/W4波段比值方法定义PDR区域
2025-05-20 09:42:27,007 - region_mapper - INFO - 使用W3/W4波段比值方法定义PDR掩模
2025-05-20 09:42:27,007 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:42:27,007 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:42:27,008 - region_mapper.ratio - INFO - 加载源G003.826-01.048的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:42:27,008 - region_mapper.ratio - INFO - 加载源G003.826-01.048的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:42:27,008 - data_manager - INFO - 为源G003.826-01.048加载多波段WISE数据: ('12', '22')
2025-05-20 09:42:27,012 - data_manager - INFO - 为源G003.826-01.048加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G003.826-01.048
2025-05-20 09:42:27,013 - data_manager - INFO - 目录中的所有文件: ['G003.826-01.048_ATLASGAL_870um.fits', 'G003.826-01.048_IRIS_100.fits', 'G003.826-01.048_NVSS.fits', 'G003.826-01.048_WISE_12.fits', 'G003.826-01.048_WISE_22.fits', 'G003.826-01.048_WISE_3.4.fits', 'G003.826-01.048_WISE_4.6.fits']
2025-05-20 09:42:27,013 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:42:27,013 - data_manager - INFO - 第一次匹配结果: ['G003.826-01.048_WISE_12.fits']
2025-05-20 09:42:27,013 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G003.826-01.048\G003.826-01.048_WISE_12.fits
2025-05-20 09:42:27,016 - data_manager - INFO - FITS数据统计: 最小值=1117.8555908203125, 最大值=5286.00390625, 均值=1306.8106689453125, 中位数=1282.0223388671875
2025-05-20 09:42:27,016 - data_manager - INFO - 有效数据点数量: 61005/61009 (99.99%)
2025-05-20 09:42:27,087 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (247, 247)
2025-05-20 09:42:27,087 - data_manager - INFO - 使用12μm波段的WCS作为参考
2025-05-20 09:42:27,088 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (247, 247)
2025-05-20 09:42:27,092 - data_manager - INFO - 为源G003.826-01.048加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G003.826-01.048
2025-05-20 09:42:27,092 - data_manager - INFO - 目录中的所有文件: ['G003.826-01.048_ATLASGAL_870um.fits', 'G003.826-01.048_IRIS_100.fits', 'G003.826-01.048_NVSS.fits', 'G003.826-01.048_WISE_12.fits', 'G003.826-01.048_WISE_22.fits', 'G003.826-01.048_WISE_3.4.fits', 'G003.826-01.048_WISE_4.6.fits']
2025-05-20 09:42:27,092 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:42:27,093 - data_manager - INFO - 第一次匹配结果: ['G003.826-01.048_WISE_22.fits']
2025-05-20 09:42:27,093 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G003.826-01.048\G003.826-01.048_WISE_22.fits
2025-05-20 09:42:27,109 - data_manager - INFO - FITS数据统计: 最小值=321.346923828125, 最大值=845.8046264648438, 均值=326.62322998046875, 中位数=325.3765869140625
2025-05-20 09:42:27,109 - data_manager - INFO - 有效数据点数量: 17956/17956 (100.00%)
2025-05-20 09:42:27,116 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (134, 134)
2025-05-20 09:42:27,117 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (134, 134)
2025-05-20 09:42:27,117 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w3', 'w4']
2025-05-20 09:42:27,118 - region_mapper.ratio - INFO - 调整W4波段数据从(134, 134)到(247, 247)
2025-05-20 09:42:27,118 - region_mapper.ratio - INFO - 调整W4波段数据从(134, 134)到(247, 247)
2025-05-20 09:42:27,130 - region_mapper.ratio - INFO - W3/W4比值范围: 3.69-5.20，均值: 4.00
2025-05-20 09:42:27,130 - region_mapper.ratio - INFO - W3/W4比值范围: 3.69-5.20，均值: 4.00
2025-05-20 09:42:27,137 - region_mapper.ratio - INFO - 像素尺度: 495.64 像素/角秒
2025-05-20 09:42:27,137 - region_mapper.ratio - INFO - 像素尺度: 495.64 像素/角秒
2025-05-20 09:42:27,137 - region_mapper.ratio - INFO - 有效半径: 0.3 像素
2025-05-20 09:42:27,137 - region_mapper.ratio - INFO - 有效半径: 0.3 像素
2025-05-20 09:42:27,138 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:42:27,138 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:42:27,879 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.680
2025-05-20 09:42:27,879 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.680
2025-05-20 09:42:27,955 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.655
2025-05-20 09:42:27,955 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.655
2025-05-20 09:42:28,034 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.636
2025-05-20 09:42:28,034 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.636
2025-05-20 09:42:28,035 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 09:42:28,035 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 09:42:28,101 - region_mapper.ratio - INFO - 聚类 0: 平均比值=4.32±0.17, 大小=241, 平均距离=6.6, 紧凑性=6.2
2025-05-20 09:42:28,101 - region_mapper.ratio - INFO - 聚类 0: 平均比值=4.32±0.17, 大小=241, 平均距离=6.6, 紧凑性=6.2
2025-05-20 09:42:28,101 - region_mapper.ratio - INFO - 聚类 1: 平均比值=5.02±0.19, 大小=76, 平均距离=7.1, 紧凑性=3.5
2025-05-20 09:42:28,101 - region_mapper.ratio - INFO - 聚类 1: 平均比值=5.02±0.19, 大小=76, 平均距离=7.1, 紧凑性=3.5
2025-05-20 09:42:28,102 - region_mapper.ratio - INFO - 选择聚类 1 作为PDR区域
2025-05-20 09:42:28,102 - region_mapper.ratio - INFO - 选择聚类 1 作为PDR区域
2025-05-20 09:42:28,110 - region_mapper.ratio - INFO - PDR掩模覆盖77个像素
2025-05-20 09:42:28,110 - region_mapper.ratio - INFO - PDR掩模覆盖77个像素
2025-05-20 09:42:28,110 - region_mapper.ratio - WARNING - PDR区域太小 (77 < 100)，使用备用方法
2025-05-20 09:42:28,110 - region_mapper.ratio - WARNING - PDR区域太小 (77 < 100)，使用备用方法
2025-05-20 09:42:28,123 - region_mapper.ratio - INFO - 备用方法后PDR掩模覆盖269个像素
2025-05-20 09:42:28,123 - region_mapper.ratio - INFO - 备用方法后PDR掩模覆盖269个像素
2025-05-20 09:42:30,693 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G003.826-01.048_w3w4_ratio.png
2025-05-20 09:42:30,693 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G003.826-01.048_w3w4_ratio.png
2025-05-20 09:42:30,693 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖269个像素
2025-05-20 09:42:30,693 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖269个像素
2025-05-20 09:42:30,719 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G003.826-01.048_region_masks.fits
2025-05-20 09:42:30,719 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G003.826-01.048_region_masks.fits
2025-05-20 09:42:30,724 - data_manager - INFO - 为源G003.826-01.048加载Gaia数据，中心坐标: RA=269.622686, Dec=-26.179235, 半径=0.2236度
2025-05-20 09:42:30,727 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances-Data/gaia_data\gaia_G003.826-01.048_13arcmin_5R.csv
2025-05-20 09:42:31,423 - data_manager - INFO - 成功加载Gaia数据，共39199条记录
2025-05-20 09:42:31,471 - data_manager - INFO - 成功加载Gaia数据，共39198个源在搜索半径内
2025-05-20 09:42:31,558 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G003.826-01.048_gaia_regions.fits
2025-05-20 09:42:44,595 - data_manager - WARNING - 检测到可能导致保存问题的列: ['TYC2', 'region']
2025-05-20 09:42:44,633 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:42:45,439 - data_manager - INFO - 成功保存数据
2025-05-20 09:42:45,444 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:42:45,444 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:42:45,445 - main_single_folder - INFO - 步骤3：恒星选择
2025-05-20 09:42:45,450 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-05-20 09:42:45,456 - star_selector - INFO - 视差信噪比筛选: 保留2472/39198个源
2025-05-20 09:42:45,459 - star_selector - INFO - RUWE筛选: 保留37941/39198个源
2025-05-20 09:42:45,487 - star_selector - INFO - 质量筛选详细信息:
2025-05-20 09:42:45,487 - star_selector - INFO -   视差信噪比阈值: 5.0
2025-05-20 09:42:45,487 - star_selector - INFO -   RUWE阈值: 1.4
2025-05-20 09:42:45,493 - star_selector - INFO -   视差信噪比筛选通过率: 8.2%
2025-05-20 09:42:45,496 - star_selector - INFO -   RUWE筛选通过率: 126.3%
2025-05-20 09:42:45,496 - star_selector - INFO -   总体通过率: 6.0%
2025-05-20 09:42:45,496 - star_selector - INFO - 质量筛选完成，保留2334/39198个源
2025-05-20 09:42:45,497 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G003.826-01.048_filtered_data.fits
2025-05-20 09:42:46,121 - data_manager - WARNING - 检测到可能导致保存问题的列: ['TYC2', 'region']
2025-05-20 09:42:46,139 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:42:46,265 - data_manager - INFO - 成功保存数据
2025-05-20 09:42:46,265 - main_single_folder - INFO - 保存质量筛选后的数据到: results/batch_single_folder\processed\G003.826-01.048_filtered_data.fits
2025-05-20 09:42:46,270 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-05-20 09:42:46,271 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-05-20 09:42:46,271 - star_selector - INFO - 筛选恒星样本，原始样本大小: 2334
2025-05-20 09:42:46,271 - star_selector - INFO - 排除YSO后: 2334/2334个源
2025-05-20 09:42:46,280 - star_selector - INFO - 区域 cavity: 0个源
2025-05-20 09:42:46,281 - star_selector - INFO - 区域 pdr: 8个源
2025-05-20 09:42:46,281 - star_selector - INFO - 区域 external: 0个源
2025-05-20 09:42:46,281 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 2334
2025-05-20 09:42:46,281 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G003.826-01.048_star_sample.fits
2025-05-20 09:42:46,915 - data_manager - WARNING - 检测到可能导致保存问题的列: ['TYC2', 'region']
2025-05-20 09:42:46,933 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:42:47,048 - data_manager - INFO - 成功保存数据
2025-05-20 09:42:47,048 - main_single_folder - INFO - 恒星选择完成
2025-05-20 09:42:47,048 - main_single_folder - INFO - 步骤4：消光计算
2025-05-20 09:42:47,053 - extinction_estimator - INFO - 为2334个恒星计算A_V，参数: {'rv': 3.1}
2025-05-20 09:42:47,071 - extinction_estimator - INFO - 使用2MASS H-K颜色计算A_V
2025-05-20 09:42:47,071 - extinction_estimator - INFO - 估计本征H-K颜色，方法: select_giants
2025-05-20 09:42:47,071 - extinction_estimator - INFO - 使用红巨星的典型H-K颜色: 0.15
2025-05-20 09:42:47,072 - extinction_estimator - INFO - 基于H-K颜色过量计算A_V，R_V=3.1
2025-05-20 09:42:47,073 - extinction_estimator - INFO - 计算得到1611个A_V值，范围: 0.00-42.26
2025-05-20 09:42:47,074 - extinction_estimator - INFO - 使用Gaia BP-RP颜色计算A_V
2025-05-20 09:42:47,074 - extinction_estimator - INFO - 估计本征BP-RP颜色，方法: fixed_value
2025-05-20 09:42:47,074 - extinction_estimator - INFO - 使用固定的BP-RP颜色: 0.8
2025-05-20 09:42:47,075 - extinction_estimator - INFO - 基于Gaia BP-RP颜色过量计算A_V，R_V=3.1
2025-05-20 09:42:47,076 - extinction_estimator - INFO - 计算得到2298个A_V值，范围: 0.00-11.20
2025-05-20 09:42:47,077 - extinction_estimator - INFO - 方法 2MASS_HK: 1611个源
2025-05-20 09:42:47,077 - extinction_estimator - INFO - 方法 Gaia_BPRP: 694个源
2025-05-20 09:42:47,077 - extinction_estimator - INFO - 方法 unknown: 29个源
2025-05-20 09:42:47,078 - extinction_estimator - INFO - A_V计算完成，统计信息:
2025-05-20 09:42:47,078 - extinction_estimator - INFO -   范围: 0.00-42.26 mag
2025-05-20 09:42:47,078 - extinction_estimator - INFO -   均值: 3.59 mag
2025-05-20 09:42:47,078 - extinction_estimator - INFO -   中位数: 2.55 mag
2025-05-20 09:42:47,079 - extinction_estimator - INFO -   标准差: 4.00 mag
2025-05-20 09:42:47,079 - extinction_estimator - INFO - 消光计算方法统计:
2025-05-20 09:42:47,080 - extinction_estimator - INFO -   2MASS_HK: 1611颗恒星 (69.0%)
2025-05-20 09:42:47,080 - extinction_estimator - INFO -   Gaia_BPRP: 694颗恒星 (29.7%)
2025-05-20 09:42:47,080 - extinction_estimator - INFO -   unknown: 29颗恒星 (1.2%)
2025-05-20 09:42:47,080 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G003.826-01.048_stars_with_av.fits
2025-05-20 09:42:47,702 - data_manager - WARNING - 检测到可能导致保存问题的列: ['TYC2', 'region', 'av_method']
2025-05-20 09:42:47,721 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:42:47,840 - data_manager - INFO - 成功保存数据
2025-05-20 09:42:47,840 - main_single_folder - INFO - 消光计算完成
2025-05-20 09:42:47,840 - main_single_folder - INFO - 步骤5：距离分析
2025-05-20 09:42:47,840 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-05-20 09:42:47,841 - distance_analyzer - INFO - 将分析1个区域: pdr
2025-05-20 09:42:47,846 - distance_analyzer - INFO - 分析pdr区域的消光-距离关系
2025-05-20 09:42:47,847 - distance_analyzer - INFO - 准备距离-消光数据，区域: pdr
2025-05-20 09:42:47,847 - distance_analyzer - INFO - 区域pdr中有8个有效源
2025-05-20 09:42:47,847 - distance_analyzer - INFO - pdr区域统计信息:
2025-05-20 09:42:47,847 - distance_analyzer - INFO -   总恒星数: 2334
2025-05-20 09:42:47,847 - distance_analyzer - INFO -   区域内恒星数: 8 (0.3% 的总数)
2025-05-20 09:42:47,848 - distance_analyzer - INFO -   有效数据点: 8 (100.0% 的区域内恒星)
2025-05-20 09:42:47,848 - distance_analyzer - WARNING - pdr区域有效数据点太少（8 < 10），跳过分析
2025-05-20 09:42:47,848 - distance_analyzer - INFO - 估计分子云距离
2025-05-20 09:42:47,848 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity, external
2025-05-20 09:42:47,848 - distance_analyzer - WARNING - 没有检测到任何跳变，无法估计距离
2025-05-20 09:42:47,849 - main_single_folder - INFO - 距离分析完成
2025-05-20 09:42:47,849 - main_single_folder - INFO - 步骤6：可视化和报告生成
2025-05-20 09:42:47,854 - data_manager - INFO - 为源G003.826-01.048加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G003.826-01.048
2025-05-20 09:42:47,854 - data_manager - INFO - 目录中的所有文件: ['G003.826-01.048_ATLASGAL_870um.fits', 'G003.826-01.048_IRIS_100.fits', 'G003.826-01.048_NVSS.fits', 'G003.826-01.048_WISE_12.fits', 'G003.826-01.048_WISE_22.fits', 'G003.826-01.048_WISE_3.4.fits', 'G003.826-01.048_WISE_4.6.fits']
2025-05-20 09:42:47,854 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:42:47,854 - data_manager - INFO - 第一次匹配结果: ['G003.826-01.048_WISE_12.fits']
2025-05-20 09:42:47,854 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G003.826-01.048\G003.826-01.048_WISE_12.fits
2025-05-20 09:42:47,857 - data_manager - INFO - FITS数据统计: 最小值=1117.8555908203125, 最大值=5286.00390625, 均值=1306.8106689453125, 中位数=1282.0223388671875
2025-05-20 09:42:47,858 - data_manager - INFO - 有效数据点数量: 61005/61009 (99.99%)
2025-05-20 09:42:47,865 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (247, 247)
2025-05-20 09:42:47,870 - visualizer - INFO - 绘制WISE图像和区域掩模，输出到: results/batch_single_folder\visualizations\G003.826-01.048_wise_regions_stars.png
2025-05-20 09:42:47,981 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:42:49,970 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:42:49,977 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:42:51,892 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G003.826-01.048_wise_regions_stars.png
2025-05-20 09:42:51,893 - main_single_folder - INFO - 成功保存WISE图像和区域掩模到: results/batch_single_folder\visualizations\G003.826-01.048_wise_regions_stars.png
2025-05-20 09:42:51,893 - main_single_folder - INFO - 加载WISE多波段数据用于RGB图像生成
2025-05-20 09:42:51,893 - data_manager - INFO - 为源G003.826-01.048加载多波段WISE数据: ('3.4', '12', '22')
2025-05-20 09:42:51,898 - data_manager - INFO - 为源G003.826-01.048加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G003.826-01.048
2025-05-20 09:42:51,898 - data_manager - INFO - 目录中的所有文件: ['G003.826-01.048_ATLASGAL_870um.fits', 'G003.826-01.048_IRIS_100.fits', 'G003.826-01.048_NVSS.fits', 'G003.826-01.048_WISE_12.fits', 'G003.826-01.048_WISE_22.fits', 'G003.826-01.048_WISE_3.4.fits', 'G003.826-01.048_WISE_4.6.fits']
2025-05-20 09:42:51,899 - data_manager - INFO - 查找WISE 3.4μm波段的FITS文件
2025-05-20 09:42:51,899 - data_manager - INFO - 第一次匹配结果: ['G003.826-01.048_WISE_3.4.fits']
2025-05-20 09:42:51,899 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G003.826-01.048\G003.826-01.048_WISE_3.4.fits
2025-05-20 09:42:51,945 - data_manager - INFO - FITS数据统计: 最小值=23.049026489257812, 最大值=3129.869140625, 均值=222.0908660888672, 中位数=156.2067108154297
2025-05-20 09:42:51,945 - data_manager - INFO - 有效数据点数量: 69169/69169 (100.00%)
2025-05-20 09:42:51,954 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (263, 263)
2025-05-20 09:42:51,954 - data_manager - INFO - 使用3.4μm波段的WCS作为参考
2025-05-20 09:42:51,954 - data_manager - INFO - 成功加载WISE 3.4μm波段数据，尺寸: (263, 263)
2025-05-20 09:42:51,960 - data_manager - INFO - 为源G003.826-01.048加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G003.826-01.048
2025-05-20 09:42:51,960 - data_manager - INFO - 目录中的所有文件: ['G003.826-01.048_ATLASGAL_870um.fits', 'G003.826-01.048_IRIS_100.fits', 'G003.826-01.048_NVSS.fits', 'G003.826-01.048_WISE_12.fits', 'G003.826-01.048_WISE_22.fits', 'G003.826-01.048_WISE_3.4.fits', 'G003.826-01.048_WISE_4.6.fits']
2025-05-20 09:42:51,960 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:42:51,960 - data_manager - INFO - 第一次匹配结果: ['G003.826-01.048_WISE_12.fits']
2025-05-20 09:42:51,960 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G003.826-01.048\G003.826-01.048_WISE_12.fits
2025-05-20 09:42:51,963 - data_manager - INFO - FITS数据统计: 最小值=1117.8555908203125, 最大值=5286.00390625, 均值=1306.8106689453125, 中位数=1282.0223388671875
2025-05-20 09:42:51,964 - data_manager - INFO - 有效数据点数量: 61005/61009 (99.99%)
2025-05-20 09:42:51,972 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (247, 247)
2025-05-20 09:42:51,973 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (247, 247)
2025-05-20 09:42:51,978 - data_manager - INFO - 为源G003.826-01.048加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G003.826-01.048
2025-05-20 09:42:51,978 - data_manager - INFO - 目录中的所有文件: ['G003.826-01.048_ATLASGAL_870um.fits', 'G003.826-01.048_IRIS_100.fits', 'G003.826-01.048_NVSS.fits', 'G003.826-01.048_WISE_12.fits', 'G003.826-01.048_WISE_22.fits', 'G003.826-01.048_WISE_3.4.fits', 'G003.826-01.048_WISE_4.6.fits']
2025-05-20 09:42:51,978 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:42:51,978 - data_manager - INFO - 第一次匹配结果: ['G003.826-01.048_WISE_22.fits']
2025-05-20 09:42:51,979 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G003.826-01.048\G003.826-01.048_WISE_22.fits
2025-05-20 09:42:51,981 - data_manager - INFO - FITS数据统计: 最小值=321.346923828125, 最大值=845.8046264648438, 均值=326.62322998046875, 中位数=325.3765869140625
2025-05-20 09:42:51,981 - data_manager - INFO - 有效数据点数量: 17956/17956 (100.00%)
2025-05-20 09:42:51,990 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (134, 134)
2025-05-20 09:42:51,990 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (134, 134)
2025-05-20 09:42:51,990 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w1', 'w3', 'w4']
2025-05-20 09:42:51,990 - visualizer - INFO - 创建WISE三波段RGB图像，输出到: results/batch_single_folder\visualizations\G003.826-01.048_wise_rgb.png
2025-05-20 09:42:52,120 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:42:52,120 - visualizer - INFO - 波段 3.4μm 的尺寸: (263, 263)
2025-05-20 09:42:52,120 - visualizer - INFO - 波段 12μm 的尺寸: (247, 247)
2025-05-20 09:42:52,120 - visualizer - INFO - 波段 22μm 的尺寸: (134, 134)
2025-05-20 09:42:52,120 - visualizer - INFO - Using 12μm band size as target: (247, 247)
2025-05-20 09:42:52,121 - visualizer - INFO - Using target image size: (247, 247), original sizes: {'w1': (263, 263), 'w3': (247, 247), 'w4': (134, 134)}
2025-05-20 09:42:52,130 - visualizer - INFO - 调整w4波段数据从(134, 134)到(247, 247)
2025-05-20 09:42:52,130 - visualizer - INFO - 红色通道(22μm)数据范围: 321.3717956542969-845.8046264648438
2025-05-20 09:42:52,131 - visualizer - INFO - 绿色通道(12μm)数据范围: 1117.8555908203125-5286.00390625
2025-05-20 09:42:52,141 - visualizer - INFO - 调整w1波段数据从(263, 263)到(247, 247)
2025-05-20 09:42:52,142 - visualizer - INFO - 蓝色通道(3.4μm)数据范围: 23.049026489257812-2858.57080078125
2025-05-20 09:42:52,144 - visualizer - INFO - 使用Lupton RGB方法，stretch=1112.65, Q=10
2025-05-20 09:42:52,144 - visualizer - INFO - 各波段99.5%百分位数: R=369.89, G=2225.29, B=1423.49
2025-05-20 09:42:52,152 - visualizer - INFO - RGB数据形状: (247, 247, 3), 类型: float32
2025-05-20 09:42:52,152 - visualizer - INFO - Red通道数据范围：0.06666667014360428-0.35686275362968445，均值：0.1438
2025-05-20 09:42:52,153 - visualizer - INFO - Green通道数据范围：0.0-0.9882352948188782，均值：0.5784
2025-05-20 09:42:52,153 - visualizer - INFO - Blue通道数据范围：0.007843137718737125-0.8117647171020508，均值：0.0913
2025-05-20 09:42:53,542 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:42:53,547 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:42:53,556 - visualizer - INFO - 按照要求不显示Gaia星
2025-05-20 09:42:53,557 - visualizer - INFO - 设置坐标刻度间隔: RA=10.000度, Dec=10.000度
2025-05-20 09:42:54,482 - visualizer - INFO - 成功保存RGB图像到: results/batch_single_folder\visualizations\G003.826-01.048_wise_rgb.png
2025-05-20 09:42:54,483 - main_single_folder - INFO - 成功保存WISE RGB图像到: results/batch_single_folder\visualizations\G003.826-01.048_wise_rgb.png
2025-05-20 09:42:54,483 - visualizer - INFO - 绘制消光-距离散点图，输出到: results/batch_single_folder\visualizations\G003.826-01.048_extinction_distance.png
2025-05-20 09:42:56,392 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G003.826-01.048_extinction_distance.png
2025-05-20 09:42:56,393 - main_single_folder - INFO - 成功保存消光-距离散点图到: results/batch_single_folder\visualizations\G003.826-01.048_extinction_distance.png
2025-05-20 09:42:56,481 - main_single_folder - INFO - 成功保存处理报告到: results/batch_single_folder\reports\G003.826-01.048_report.txt
2025-05-20 09:42:56,481 - main_single_folder - INFO - 处理G003.826-01.048完成
