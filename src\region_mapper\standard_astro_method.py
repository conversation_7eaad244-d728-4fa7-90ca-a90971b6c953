"""
标准天文流程PDR检测模块

使用标准天文数据处理流程检测PDR区域，包括背景扣除、图像对齐、比值计算、
直方图分析和阈值确定等步骤。
"""

import os
import numpy as np
from scipy import ndimage, signal
from skimage import morphology, measure
import matplotlib.pyplot as plt
from astropy.coordinates import SkyCoord
from astropy.wcs import WCS
from astropy.io import fits
import astropy.units as u
from astropy.wcs.utils import proj_plane_pixel_scales
from photutils.background import Background2D, MedianBackground
from reproject import reproject_interp
from regions import CirclePixelRegion, PixCoord

from src.utils.logger import setup_logger
from src.utils.helpers import ensure_directory, load_config
from src.data_manager import load_wise_fits

# 设置日志记录器
logger = setup_logger(name='region_mapper.standard_astro')

def load_and_process_wise_images(source_name, wise_base_path=None, config_path='config/default_config.yaml'):
    """
    步骤1: 加载WISE图像并进行初步处理

    Args:
        source_name: 源名称
        wise_base_path: WISE数据基础路径
        config_path: 配置文件路径

    Returns:
        tuple: (w3_data, w3_header, w3_wcs, w4_data, w4_header, w4_wcs)
    """
    logger.info("步骤1: 加载WISE图像并进行初步处理")

    try:
        # 加载W3 (12μm) 数据
        w3_data, w3_header, w3_wcs = load_wise_fits(
            source_name, wise_base_path, config_path, band='12')

        # 加载W4 (22μm) 数据
        w4_data, w4_header, w4_wcs = load_wise_fits(
            source_name, wise_base_path, config_path, band='22')

        if w3_data is None or w4_data is None:
            raise ValueError("无法加载W3或W4波段数据")

        # 处理NaN值 - 用插值或峰值替代
        logger.info("处理NaN值")

        # 对于W3数据
        nan_mask_w3 = np.isnan(w3_data)
        if np.any(nan_mask_w3):
            logger.info(f"W3数据中发现{np.sum(nan_mask_w3)}个NaN值，进行插值处理")
            # 使用中值滤波进行插值
            w3_data_filled = ndimage.median_filter(w3_data, size=3)
            w3_data[nan_mask_w3] = w3_data_filled[nan_mask_w3]

        # 对于W4数据
        nan_mask_w4 = np.isnan(w4_data)
        if np.any(nan_mask_w4):
            logger.info(f"W4数据中发现{np.sum(nan_mask_w4)}个NaN值，进行插值处理")
            # 使用中值滤波进行插值
            w4_data_filled = ndimage.median_filter(w4_data, size=3)
            w4_data[nan_mask_w4] = w4_data_filled[nan_mask_w4]

        # 检查数据单位
        w3_unit = w3_header.get('BUNIT', 'Unknown')
        w4_unit = w4_header.get('BUNIT', 'Unknown')
        logger.info(f"W3数据单位: {w3_unit}")
        logger.info(f"W4数据单位: {w4_unit}")

        if w3_unit != w4_unit:
            logger.warning(f"W3和W4数据单位不一致: {w3_unit} vs {w4_unit}")

        logger.info(f"W3数据形状: {w3_data.shape}")
        logger.info(f"W4数据形状: {w4_data.shape}")

        return w3_data, w3_header, w3_wcs, w4_data, w4_header, w4_wcs

    except Exception as e:
        logger.error(f"加载WISE图像失败: {str(e)}")
        raise

def select_background_regions(data, center_coord, wcs, r_eff,
                            min_background_radius_factor=4.0,
                            background_percentile_threshold=20.0,
                            uniformity_threshold=0.3):
    """
    选择背景区域：值较小且均匀、没有明显结构的区域

    Args:
        data: 图像数据
        center_coord: 中心坐标
        wcs: 世界坐标系
        r_eff: 有效半径（度）
        min_background_radius_factor: 最小背景区域距离因子
        background_percentile_threshold: 背景像素值百分位数阈值
        uniformity_threshold: 均匀性阈值（标准差/均值）

    Returns:
        numpy.ndarray: 背景区域掩模
    """
    logger.info("选择背景区域：寻找值较小且均匀的区域")

    try:
        # 获取中心坐标在图像中的像素位置
        center_x, center_y = wcs.world_to_pixel(center_coord)

        # 计算像素尺度
        from astropy.wcs.utils import proj_plane_pixel_scales
        pixel_scales = proj_plane_pixel_scales(wcs)
        pixel_scale = np.mean(pixel_scales) * 3600  # 转换为角秒/像素

        # 计算有效半径（像素）
        r_eff_pixels = r_eff * 3600 / pixel_scale
        min_background_radius_pixels = r_eff_pixels * min_background_radius_factor

        logger.info(f"有效半径: {r_eff_pixels:.1f} 像素")
        logger.info(f"最小背景区域距离: {min_background_radius_pixels:.1f} 像素")

        # 创建距离图像
        y, x = np.ogrid[:data.shape[0], :data.shape[1]]
        distance_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)

        # 候选背景区域：距离中心足够远的区域
        candidate_background_mask = distance_from_center > min_background_radius_pixels

        # 排除无效像素
        valid_data_mask = np.isfinite(data) & (data > 0)
        candidate_background_mask = candidate_background_mask & valid_data_mask

        if np.sum(candidate_background_mask) == 0:
            logger.warning("没有找到候选背景区域，使用整个图像的低值区域")
            candidate_background_mask = valid_data_mask

        # 计算候选区域的像素值统计
        candidate_values = data[candidate_background_mask]

        # 选择低值区域（低于指定百分位数）
        value_threshold = np.percentile(candidate_values, background_percentile_threshold)
        low_value_mask = (data <= value_threshold) & candidate_background_mask

        logger.info(f"背景值阈值（{background_percentile_threshold}%分位数）: {value_threshold:.6f}")
        logger.info(f"低值候选像素数: {np.sum(low_value_mask)}")

        # 使用滑动窗口检查局部均匀性
        from scipy import ndimage
        window_size = max(5, int(r_eff_pixels / 4))  # 窗口大小约为有效半径的1/4

        # 计算局部均值和标准差
        local_mean = ndimage.uniform_filter(data.astype(float), size=window_size)
        local_var = ndimage.uniform_filter(data.astype(float)**2, size=window_size) - local_mean**2
        local_std = np.sqrt(np.maximum(local_var, 0))

        # 计算局部变异系数（标准差/均值）
        local_cv = np.divide(local_std, local_mean,
                           out=np.full_like(local_std, np.inf),
                           where=local_mean > 0)

        # 选择均匀的区域（低变异系数）
        uniform_mask = (local_cv <= uniformity_threshold) & low_value_mask

        logger.info(f"均匀性阈值（变异系数）: {uniformity_threshold}")
        logger.info(f"均匀背景候选像素数: {np.sum(uniform_mask)}")

        # 如果均匀区域太少，放宽条件
        if np.sum(uniform_mask) < 100:  # 至少需要100个像素
            logger.warning("均匀背景区域太少，放宽均匀性条件")
            uniformity_threshold *= 2
            uniform_mask = (local_cv <= uniformity_threshold) & low_value_mask
            logger.info(f"放宽后均匀背景像素数: {np.sum(uniform_mask)}")

        # 如果还是太少，只使用低值条件
        if np.sum(uniform_mask) < 50:
            logger.warning("均匀背景区域仍然太少，仅使用低值条件")
            uniform_mask = low_value_mask

        # 进一步清理：移除孤立的像素
        from skimage import morphology
        uniform_mask = morphology.binary_opening(uniform_mask, morphology.disk(2))
        uniform_mask = morphology.remove_small_objects(uniform_mask, min_size=25)

        logger.info(f"最终背景区域像素数: {np.sum(uniform_mask)}")

        if np.sum(uniform_mask) == 0:
            logger.error("无法找到合适的背景区域")
            raise ValueError("无法找到合适的背景区域")

        return uniform_mask

    except Exception as e:
        logger.error(f"背景区域选择失败: {str(e)}")
        raise

def estimate_and_subtract_background(w3_data, w4_data, center_coord, wcs, r_eff,
                                   box_size=50, filter_size=3):
    """
    步骤2: 背景估计与扣除

    首先选择值较小且均匀、没有明显结构的区域作为背景区域，
    然后估计背景并进行扣除。

    Args:
        w3_data: W3波段数据
        w4_data: W4波段数据
        center_coord: 中心坐标
        wcs: 世界坐标系
        r_eff: 有效半径（度）
        box_size: 背景估计网格大小
        filter_size: 背景滤波核大小

    Returns:
        tuple: (w3_data_sub, w4_data_sub, w3_background, w4_background, w3_bkg_mask, w4_bkg_mask)
    """
    logger.info("步骤2: 背景估计与扣除")

    try:
        # 选择W3背景区域
        logger.info("为W3波段选择背景区域")
        w3_background_mask = select_background_regions(w3_data, center_coord, wcs, r_eff)

        # 选择W4背景区域
        logger.info("为W4波段选择背景区域")
        w4_background_mask = select_background_regions(w4_data, center_coord, wcs, r_eff)

        # 计算背景值
        w3_background_values = w3_data[w3_background_mask]
        w4_background_values = w4_data[w4_background_mask]

        # 使用中位数作为背景值（对异常值更鲁棒）
        w3_background_level = np.median(w3_background_values)
        w4_background_level = np.median(w4_background_values)

        # 也计算标准差，用于评估背景噪声
        w3_background_std = np.std(w3_background_values)
        w4_background_std = np.std(w4_background_values)

        logger.info(f"W3背景统计: 中位数={w3_background_level:.6f}, 标准差={w3_background_std:.6f}")
        logger.info(f"W4背景统计: 中位数={w4_background_level:.6f}, 标准差={w4_background_std:.6f}")
        logger.info(f"W3背景像素数: {len(w3_background_values)}")
        logger.info(f"W4背景像素数: {len(w4_background_values)}")

        # 创建平滑的背景图像（使用photutils进行空间变化的背景估计）
        from photutils.background import Background2D, MedianBackground

        # 创建排除掩模（排除非背景区域）
        w3_exclude_mask = ~w3_background_mask
        w4_exclude_mask = ~w4_background_mask

        # 使用Background2D进行空间变化的背景估计
        bkg_estimator = MedianBackground()

        try:
            w3_bkg = Background2D(w3_data, box_size, filter_size=filter_size,
                                 bkg_estimator=bkg_estimator,
                                 exclude_percentile=10.0,  # 排除高值像素
                                 edge_method='pad')
            w3_background_2d = w3_bkg.background
        except Exception as e:
            logger.warning(f"W3 Background2D失败，使用常数背景: {str(e)}")
            w3_background_2d = np.full_like(w3_data, w3_background_level)

        try:
            w4_bkg = Background2D(w4_data, box_size, filter_size=filter_size,
                                 bkg_estimator=bkg_estimator,
                                 exclude_percentile=10.0,  # 排除高值像素
                                 edge_method='pad')
            w4_background_2d = w4_bkg.background
        except Exception as e:
            logger.warning(f"W4 Background2D失败，使用常数背景: {str(e)}")
            w4_background_2d = np.full_like(w4_data, w4_background_level)

        # 调整背景水平：确保背景区域的中位数与计算的背景水平一致
        w3_bkg_median_in_region = np.median(w3_background_2d[w3_background_mask])
        w4_bkg_median_in_region = np.median(w4_background_2d[w4_background_mask])

        w3_background_2d += (w3_background_level - w3_bkg_median_in_region)
        w4_background_2d += (w4_background_level - w4_bkg_median_in_region)

        # 背景扣除
        w3_data_sub = w3_data - w3_background_2d
        w4_data_sub = w4_data - w4_background_2d

        logger.info(f"背景扣除完成")
        logger.info(f"W3背景扣除后中位数: {np.median(w3_data_sub[w3_background_mask]):.6f}")
        logger.info(f"W4背景扣除后中位数: {np.median(w4_data_sub[w4_background_mask]):.6f}")

        return (w3_data_sub, w4_data_sub, w3_background_2d, w4_background_2d,
                w3_background_mask, w4_background_mask)

    except Exception as e:
        logger.error(f"背景估计失败: {str(e)}")
        raise

def align_and_resample_images(w3_data_sub, w3_wcs, w4_data_sub, w4_wcs):
    """
    步骤3: 对齐与重采样

    Args:
        w3_data_sub: 背景扣除后的W3数据
        w3_wcs: W3的WCS
        w4_data_sub: 背景扣除后的W4数据
        w4_wcs: W4的WCS

    Returns:
        tuple: (w3_reprojected, ratio_wcs, ratio_shape)
    """
    logger.info("步骤3: 对齐与重采样")

    try:
        # 使用W4的WCS和形状作为目标
        target_wcs = w4_wcs
        target_shape = w4_data_sub.shape

        logger.info(f"目标形状: {target_shape}")
        logger.info(f"W3原始形状: {w3_data_sub.shape}")

        # 将W3重采样到W4的网格
        logger.info("将W3重采样到W4网格")
        w3_reprojected, footprint = reproject_interp(
            (w3_data_sub, w3_wcs), target_wcs, target_shape)

        # 处理重采样产生的NaN值
        nan_mask = np.isnan(w3_reprojected)
        if np.any(nan_mask):
            logger.info(f"重采样产生了{np.sum(nan_mask)}个NaN值")
            w3_reprojected[nan_mask] = 0.0

        logger.info(f"重采样后W3形状: {w3_reprojected.shape}")

        return w3_reprojected, target_wcs, target_shape

    except Exception as e:
        logger.error(f"图像对齐失败: {str(e)}")
        raise

def calculate_ratio_map(w3_reprojected, w4_data_sub):
    """
    步骤4: 计算W3/W4比值图

    Args:
        w3_reprojected: 重采样后的W3数据
        w4_data_sub: 背景扣除后的W4数据

    Returns:
        numpy.ndarray: W3/W4比值图
    """
    logger.info("步骤4: 计算W3/W4比值图")

    try:
        # 创建有效像素掩模
        # 避免除以零或负值
        epsilon = 1e-10
        valid_mask = (w4_data_sub > epsilon) & (w3_reprojected >= 0) & np.isfinite(w3_reprojected) & np.isfinite(w4_data_sub)

        # 初始化比值图
        ratio_map = np.full_like(w3_reprojected, np.nan)

        # 计算比值
        ratio_map[valid_mask] = w3_reprojected[valid_mask] / w4_data_sub[valid_mask]

        # 统计信息
        valid_ratios = ratio_map[valid_mask]
        logger.info(f"有效比值像素数: {np.sum(valid_mask)}")
        logger.info(f"比值范围: {np.min(valid_ratios):.3f} - {np.max(valid_ratios):.3f}")
        logger.info(f"比值中位数: {np.median(valid_ratios):.3f}")

        return ratio_map

    except Exception as e:
        logger.error(f"比值计算失败: {str(e)}")
        raise

def extract_radial_analysis_region(ratio_map, ratio_wcs, center_coord, r_eff,
                                 radial_analysis_factor=3.0):
    """
    步骤5: 定义径向分析区域并提取比值

    Args:
        ratio_map: W3/W4比值图
        ratio_wcs: 比值图的WCS
        center_coord: HII区中心坐标
        r_eff: 有效半径（度）
        radial_analysis_factor: 径向分析区域因子

    Returns:
        tuple: (ratio_values_in_area, radial_mask, center_pixel, r_eff_pixels)
    """
    logger.info("步骤5: 定义径向分析区域并提取比值")

    try:
        # 将中心坐标转换为像素坐标
        center_pixel = ratio_wcs.world_to_pixel(center_coord)
        center_x, center_y = center_pixel

        # 计算像素尺度
        pixel_scales = proj_plane_pixel_scales(ratio_wcs)
        pixel_scale = np.mean(pixel_scales) * 3600  # 转换为角秒/像素

        # 将有效半径转换为像素
        r_eff_arcsec = r_eff * 3600  # 转换为角秒
        r_eff_pixels = r_eff_arcsec / pixel_scale

        # 定义径向分析区域
        analysis_radius_pixels = r_eff_pixels * radial_analysis_factor

        logger.info(f"中心像素坐标: ({center_x:.1f}, {center_y:.1f})")
        logger.info(f"像素尺度: {pixel_scale:.2f} 角秒/像素")
        logger.info(f"有效半径: {r_eff_pixels:.1f} 像素")
        logger.info(f"分析半径: {analysis_radius_pixels:.1f} 像素")

        # 创建圆形区域掩模
        center_pixcoord = PixCoord(center_x, center_y)
        circle_region = CirclePixelRegion(center_pixcoord, analysis_radius_pixels)
        radial_mask = circle_region.to_mask().to_image(ratio_map.shape).astype(bool)

        # 提取区域内的有效比值
        valid_mask = np.isfinite(ratio_map) & radial_mask
        ratio_values_in_area = ratio_map[valid_mask]

        logger.info(f"径向分析区域内有效像素数: {len(ratio_values_in_area)}")

        if len(ratio_values_in_area) == 0:
            raise ValueError("径向分析区域内没有有效的比值数据")

        return ratio_values_in_area, radial_mask, center_pixel, r_eff_pixels

    except Exception as e:
        logger.error(f"径向分析区域提取失败: {str(e)}")
        raise

def analyze_histogram_and_determine_threshold(ratio_values_in_area, histogram_bins=100,
                                            output_dir=None, source_name=None):
    """
    步骤6: 直方图分析与阈值确定

    Args:
        ratio_values_in_area: 径向分析区域内的比值数组
        histogram_bins: 直方图bin数
        output_dir: 输出目录
        source_name: 源名称

    Returns:
        float: 确定的PDR阈值
    """
    logger.info("步骤6: 直方图分析与阈值确定")

    try:
        # 计算直方图
        hist, bin_edges = np.histogram(ratio_values_in_area, bins=histogram_bins)
        bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2

        logger.info(f"比值范围: {np.min(ratio_values_in_area):.3f} - {np.max(ratio_values_in_area):.3f}")
        logger.info(f"比值中位数: {np.median(ratio_values_in_area):.3f}")
        logger.info(f"比值标准差: {np.std(ratio_values_in_area):.3f}")

        # 寻找峰值
        # 平滑直方图以减少噪声
        hist_smooth = ndimage.gaussian_filter1d(hist.astype(float), sigma=1.0)

        # 寻找峰值
        peaks, _ = signal.find_peaks(hist_smooth, height=np.max(hist_smooth)*0.1,
                                   distance=histogram_bins//10)

        logger.info(f"找到{len(peaks)}个峰值")

        # 可视化直方图
        if output_dir and source_name:
            plt.figure(figsize=(12, 8))

            plt.subplot(2, 2, 1)
            plt.hist(ratio_values_in_area, bins=histogram_bins, alpha=0.7, color='gray',
                    edgecolor='black', density=True)
            plt.xlabel('W3/W4 Ratio')
            plt.ylabel('Density')
            plt.title(f'{source_name} - W3/W4 Ratio Histogram')
            plt.grid(True, alpha=0.3)

            plt.subplot(2, 2, 2)
            plt.plot(bin_centers, hist, 'b-', label='Original')
            plt.plot(bin_centers, hist_smooth, 'r-', label='Smoothed')
            if len(peaks) > 0:
                plt.plot(bin_centers[peaks], hist_smooth[peaks], 'ro', markersize=8, label='Peaks')
            plt.xlabel('W3/W4 Ratio')
            plt.ylabel('Count')
            plt.title('Histogram Analysis')
            plt.legend()
            plt.grid(True, alpha=0.3)

        # 确定阈值
        pdr_threshold = None

        if len(peaks) >= 2:
            # 如果有两个或更多峰值，寻找它们之间的谷值
            logger.info("检测到多个峰值，寻找谷值作为阈值")

            # 按峰值高度排序，选择最高的两个峰
            peak_heights = hist_smooth[peaks]
            sorted_indices = np.argsort(peak_heights)[::-1]
            main_peaks = peaks[sorted_indices[:2]]
            main_peaks = np.sort(main_peaks)  # 按位置排序

            # 在两个主峰之间寻找最小值
            start_idx, end_idx = main_peaks[0], main_peaks[1]
            valley_region = hist_smooth[start_idx:end_idx+1]
            valley_idx = np.argmin(valley_region) + start_idx
            pdr_threshold = bin_centers[valley_idx]

            logger.info(f"在峰值之间找到谷值，阈值: {pdr_threshold:.3f}")

        elif len(peaks) == 1:
            # 如果只有一个峰值，使用统计方法确定阈值
            logger.info("只检测到一个峰值，使用统计方法确定阈值")

            # 使用中位数加上一个标准差作为阈值
            median_ratio = np.median(ratio_values_in_area)
            std_ratio = np.std(ratio_values_in_area)
            pdr_threshold = median_ratio + 0.5 * std_ratio

            logger.info(f"统计阈值: {pdr_threshold:.3f} (中位数 + 0.5*标准差)")

        else:
            # 如果没有明显峰值，使用百分位数方法
            logger.info("未检测到明显峰值，使用百分位数方法")
            pdr_threshold = np.percentile(ratio_values_in_area, 75)
            logger.info(f"75%百分位数阈值: {pdr_threshold:.3f}")

        # 在可视化中标记阈值
        if output_dir and source_name and pdr_threshold is not None:
            plt.subplot(2, 2, 1)
            plt.axvline(pdr_threshold, color='red', linestyle='--', linewidth=2,
                       label=f'PDR Threshold: {pdr_threshold:.3f}')
            plt.legend()

            plt.subplot(2, 2, 2)
            plt.axvline(pdr_threshold, color='red', linestyle='--', linewidth=2,
                       label=f'PDR Threshold: {pdr_threshold:.3f}')
            plt.legend()

            # 保存图像
            vis_dir = os.path.join(output_dir, 'visualizations')
            ensure_directory(vis_dir)
            plt.tight_layout()
            plt.savefig(os.path.join(vis_dir, f"{source_name}_histogram_analysis.png"), dpi=300)
            plt.close()

            logger.info(f"直方图分析图像已保存")

        if pdr_threshold is None:
            raise ValueError("无法确定合适的PDR阈值")

        return pdr_threshold

    except Exception as e:
        logger.error(f"直方图分析失败: {str(e)}")
        raise

def generate_pdr_mask(ratio_map, pdr_threshold, radial_mask,
                     remove_small_objects_size=100):
    """
    步骤7: 生成PDR区域Mask

    Args:
        ratio_map: W3/W4比值图
        pdr_threshold: PDR阈值
        radial_mask: 径向分析区域掩模
        remove_small_objects_size: 移除小对象的最小像素数

    Returns:
        numpy.ndarray: PDR区域掩模
    """
    logger.info("步骤7: 生成PDR区域Mask")

    try:
        # 应用阈值
        pdr_mask_raw = ratio_map > pdr_threshold

        # 结合径向限制
        pdr_mask_initial = pdr_mask_raw & radial_mask

        logger.info(f"初始PDR像素数: {np.sum(pdr_mask_initial)}")

        # 形态学操作
        # 移除小的噪声区域
        pdr_mask_cleaned = morphology.remove_small_objects(
            pdr_mask_initial, min_size=remove_small_objects_size)

        # 填充小空洞
        pdr_mask_filled = morphology.binary_closing(pdr_mask_cleaned,
                                                   morphology.disk(3))

        # 最终的孔洞填充
        pdr_mask_final = ndimage.binary_fill_holes(pdr_mask_filled)

        logger.info(f"清理后PDR像素数: {np.sum(pdr_mask_cleaned)}")
        logger.info(f"最终PDR像素数: {np.sum(pdr_mask_final)}")

        return pdr_mask_final

    except Exception as e:
        logger.error(f"PDR掩模生成失败: {str(e)}")
        raise

def generate_cavity_and_external_masks(pdr_mask, center_pixel, r_eff_pixels,
                                      ratio_shape, external_factor=5.0):
    """
    步骤8: 生成Cavity和External区域Mask

    Args:
        pdr_mask: PDR区域掩模
        center_pixel: 中心像素坐标
        r_eff_pixels: 有效半径（像素）
        ratio_shape: 比值图形状
        external_factor: 外部区域因子

    Returns:
        tuple: (cavity_mask, external_mask)
    """
    logger.info("步骤8: 生成Cavity和External区域Mask")

    try:
        center_x, center_y = center_pixel

        # 创建Cavity掩模
        center_pixcoord = PixCoord(center_x, center_y)
        cavity_region = CirclePixelRegion(center_pixcoord, r_eff_pixels)
        cavity_radial_mask = cavity_region.to_mask().to_image(ratio_shape).astype(bool)

        # Cavity = 中心R_eff范围内且不属于PDR的区域
        cavity_mask = cavity_radial_mask & (~pdr_mask)

        # 创建External掩模
        external_radius_pixels = r_eff_pixels * external_factor
        external_region = CirclePixelRegion(center_pixcoord, external_radius_pixels)
        external_radial_mask = external_region.to_mask().to_image(ratio_shape).astype(bool)

        # External = 在外部半径内，但不在cavity和PDR区域内
        external_mask = external_radial_mask & (~cavity_mask) & (~pdr_mask)

        logger.info(f"Cavity像素数: {np.sum(cavity_mask)}")
        logger.info(f"External像素数: {np.sum(external_mask)}")

        # 验证掩模互斥性
        overlap_cavity_pdr = np.sum(cavity_mask & pdr_mask)
        overlap_cavity_external = np.sum(cavity_mask & external_mask)
        overlap_pdr_external = np.sum(pdr_mask & external_mask)

        if overlap_cavity_pdr > 0 or overlap_cavity_external > 0 or overlap_pdr_external > 0:
            logger.warning(f"掩模重叠检测: Cavity-PDR={overlap_cavity_pdr}, "
                          f"Cavity-External={overlap_cavity_external}, "
                          f"PDR-External={overlap_pdr_external}")
        else:
            logger.info("掩模互斥性验证通过")

        return cavity_mask, external_mask

    except Exception as e:
        logger.error(f"Cavity和External掩模生成失败: {str(e)}")
        raise

def save_masks_and_info(pdr_mask, cavity_mask, external_mask, ratio_wcs,
                       output_dir, source_name):
    """
    步骤9: 保存Mask和相关信息

    Args:
        pdr_mask: PDR区域掩模
        cavity_mask: Cavity区域掩模
        external_mask: External区域掩模
        ratio_wcs: 比值图的WCS
        output_dir: 输出目录
        source_name: 源名称
    """
    logger.info("步骤9: 保存Mask和相关信息")

    try:
        # 创建掩模输出目录
        masks_dir = os.path.join(output_dir, 'masks')
        ensure_directory(masks_dir)

        # 创建WCS头文件
        header = ratio_wcs.to_header()

        # 保存PDR掩模
        pdr_hdu = fits.PrimaryHDU(pdr_mask.astype(np.uint8), header=header)
        pdr_path = os.path.join(masks_dir, f"{source_name}_pdr_mask.fits")
        pdr_hdu.writeto(pdr_path, overwrite=True)
        logger.info(f"PDR掩模已保存到: {pdr_path}")

        # 保存Cavity掩模
        cavity_hdu = fits.PrimaryHDU(cavity_mask.astype(np.uint8), header=header)
        cavity_path = os.path.join(masks_dir, f"{source_name}_cavity_mask.fits")
        cavity_hdu.writeto(cavity_path, overwrite=True)
        logger.info(f"Cavity掩模已保存到: {cavity_path}")

        # 保存External掩模
        external_hdu = fits.PrimaryHDU(external_mask.astype(np.uint8), header=header)
        external_path = os.path.join(masks_dir, f"{source_name}_external_mask.fits")
        external_hdu.writeto(external_path, overwrite=True)
        logger.info(f"External掩模已保存到: {external_path}")

        # 保存组合掩模（用于可视化）
        combined_mask = np.zeros_like(pdr_mask, dtype=np.uint8)
        combined_mask[cavity_mask] = 1  # Cavity = 1
        combined_mask[pdr_mask] = 2     # PDR = 2
        combined_mask[external_mask] = 3  # External = 3

        combined_hdu = fits.PrimaryHDU(combined_mask, header=header)
        combined_path = os.path.join(masks_dir, f"{source_name}_combined_mask.fits")
        combined_hdu.writeto(combined_path, overwrite=True)
        logger.info(f"组合掩模已保存到: {combined_path}")

    except Exception as e:
        logger.error(f"保存掩模失败: {str(e)}")
        raise

def create_visualization(w3_data_sub, w4_data_sub, ratio_map, pdr_mask, cavity_mask,
                        external_mask, center_pixel, r_eff_pixels, pdr_threshold,
                        output_dir, source_name, w3_bkg_mask=None, w4_bkg_mask=None):
    """
    创建标准天文流程的可视化图像

    Args:
        w3_data_sub: 背景扣除后的W3数据
        w4_data_sub: 背景扣除后的W4数据
        ratio_map: W3/W4比值图
        pdr_mask: PDR区域掩模
        cavity_mask: Cavity区域掩模
        external_mask: External区域掩模
        center_pixel: 中心像素坐标
        r_eff_pixels: 有效半径（像素）
        pdr_threshold: PDR阈值
        output_dir: 输出目录
        source_name: 源名称
        w3_bkg_mask: W3背景区域掩模（可选）
        w4_bkg_mask: W4背景区域掩模（可选）
    """
    try:
        # 创建可视化目录
        vis_dir = os.path.join(output_dir, 'visualizations')
        ensure_directory(vis_dir)

        # 创建主要可视化图像
        _, axes = plt.subplots(3, 3, figsize=(18, 18))

        center_x, center_y = center_pixel

        # W3数据
        ax = axes[0, 0]
        w3_display = np.log1p(np.maximum(w3_data_sub, 0))
        im1 = ax.imshow(w3_display, origin='lower', cmap='inferno')
        ax.set_title('W3 (12μm) - Background Subtracted')
        ax.plot(center_x, center_y, 'c+', markersize=10, markeredgewidth=2)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False,
                           color='cyan', linestyle='--', linewidth=2)
        ax.add_patch(circle)
        # 显示背景区域
        if w3_bkg_mask is not None:
            ax.contour(w3_bkg_mask, levels=[0.5], colors='yellow', linewidths=1, alpha=0.7)
        plt.colorbar(im1, ax=ax, label='log(W3+1)')

        # W4数据
        ax = axes[0, 1]
        w4_display = np.log1p(np.maximum(w4_data_sub, 0))
        im2 = ax.imshow(w4_display, origin='lower', cmap='inferno')
        ax.set_title('W4 (22μm) - Background Subtracted')
        ax.plot(center_x, center_y, 'c+', markersize=10, markeredgewidth=2)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False,
                           color='cyan', linestyle='--', linewidth=2)
        ax.add_patch(circle)
        # 显示背景区域
        if w4_bkg_mask is not None:
            ax.contour(w4_bkg_mask, levels=[0.5], colors='yellow', linewidths=1, alpha=0.7)
        plt.colorbar(im2, ax=ax, label='log(W4+1)')

        # 背景区域可视化
        ax = axes[0, 2]
        if w3_bkg_mask is not None and w4_bkg_mask is not None:
            # 创建背景区域组合图像
            bkg_combined = np.zeros((*w3_bkg_mask.shape, 3))
            bkg_combined[..., 0] = w3_bkg_mask  # 红色 = W3背景区域
            bkg_combined[..., 1] = w4_bkg_mask  # 绿色 = W4背景区域
            bkg_combined[..., 2] = w3_bkg_mask & w4_bkg_mask  # 蓝色 = 重叠区域
            ax.imshow(bkg_combined, origin='lower')
            ax.set_title('Background Regions')
            ax.plot(center_x, center_y, 'w+', markersize=10, markeredgewidth=2)
            circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False,
                               color='white', linestyle='--', linewidth=2)
            ax.add_patch(circle)
            # 添加图例
            from matplotlib.patches import Patch
            legend_elements = [
                Patch(facecolor='red', edgecolor='w', label='W3 Background'),
                Patch(facecolor='green', edgecolor='w', label='W4 Background'),
                Patch(facecolor='blue', edgecolor='w', label='Overlap')
            ]
            ax.legend(handles=legend_elements, loc='upper right')
        else:
            ax.text(0.5, 0.5, "Background regions\nnot available",
                   ha='center', va='center', transform=ax.transAxes)

        # 比值图
        ax = axes[1, 0]
        ratio_display = np.where(np.isfinite(ratio_map), ratio_map, 0)
        vmin, vmax = np.percentile(ratio_display[ratio_display > 0], [5, 95])
        im3 = ax.imshow(ratio_display, origin='lower', cmap='viridis', vmin=vmin, vmax=vmax)
        ax.set_title(f'W3/W4 Ratio (Threshold: {pdr_threshold:.3f})')
        ax.plot(center_x, center_y, 'r+', markersize=10, markeredgewidth=2)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False,
                           color='red', linestyle='--', linewidth=2)
        ax.add_patch(circle)
        plt.colorbar(im3, ax=ax, label='W3/W4 Ratio')

        # PDR掩模
        ax = axes[1, 1]
        ax.imshow(pdr_mask, origin='lower', cmap='Reds', alpha=0.8)
        ax.set_title(f'PDR Mask ({np.sum(pdr_mask)} pixels)')
        ax.plot(center_x, center_y, 'k+', markersize=10, markeredgewidth=2)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False,
                           color='black', linestyle='--', linewidth=2)
        ax.add_patch(circle)

        # Cavity掩模
        ax = axes[1, 2]
        ax.imshow(cavity_mask, origin='lower', cmap='Blues', alpha=0.8)
        ax.set_title(f'Cavity Mask ({np.sum(cavity_mask)} pixels)')
        ax.plot(center_x, center_y, 'k+', markersize=10, markeredgewidth=2)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False,
                           color='black', linestyle='--', linewidth=2)
        ax.add_patch(circle)

        # External掩模
        ax = axes[2, 0]
        ax.imshow(external_mask, origin='lower', cmap='Greens', alpha=0.8)
        ax.set_title(f'External Mask ({np.sum(external_mask)} pixels)')
        ax.plot(center_x, center_y, 'k+', markersize=10, markeredgewidth=2)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False,
                           color='black', linestyle='--', linewidth=2)
        ax.add_patch(circle)

        # 组合掩模
        ax = axes[2, 1]
        combined_display = np.zeros((*pdr_mask.shape, 3))
        combined_display[..., 0] = cavity_mask  # 红色 = Cavity
        combined_display[..., 1] = pdr_mask     # 绿色 = PDR
        combined_display[..., 2] = external_mask  # 蓝色 = External
        ax.imshow(combined_display, origin='lower')
        ax.set_title('Combined Regions')
        ax.plot(center_x, center_y, 'w+', markersize=10, markeredgewidth=2)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False,
                           color='white', linestyle='--', linewidth=2)
        ax.add_patch(circle)

        # 添加图例
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='red', edgecolor='w', label='Cavity'),
            Patch(facecolor='green', edgecolor='w', label='PDR'),
            Patch(facecolor='blue', edgecolor='w', label='External')
        ]
        ax.legend(handles=legend_elements, loc='upper right')

        # 处理流程总结
        ax = axes[2, 2]
        ax.axis('off')
        summary_text = f"""Standard Astronomical Processing Summary:

Source: {source_name}
Effective Radius: {r_eff_pixels:.1f} pixels
PDR Threshold: {pdr_threshold:.3f}

Region Statistics:
• PDR: {np.sum(pdr_mask)} pixels
• Cavity: {np.sum(cavity_mask)} pixels
• External: {np.sum(external_mask)} pixels

Background Regions:
• W3: {np.sum(w3_bkg_mask) if w3_bkg_mask is not None else 'N/A'} pixels
• W4: {np.sum(w4_bkg_mask) if w4_bkg_mask is not None else 'N/A'} pixels
        """
        ax.text(0.05, 0.95, summary_text, transform=ax.transAxes,
               fontsize=10, verticalalignment='top', fontfamily='monospace')

        plt.tight_layout()

        # 保存图像
        vis_path = os.path.join(vis_dir, f"{source_name}_standard_astro_analysis.png")
        plt.savefig(vis_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"标准天文流程可视化图像已保存到: {vis_path}")

    except Exception as e:
        logger.error(f"创建可视化失败: {str(e)}")
        raise

def define_pdr_mask_standard_astro(source_name, wcs, center_coord, r_eff,
                                  wise_base_path=None, config_path='config/default_config.yaml',
                                  output_dir=None, save_plots=True):
    """
    使用标准天文流程定义PDR区域掩模

    完整实现9步标准天文数据处理流程：
    1. 加载WISE图像并进行初步处理
    2. 背景估计与扣除
    3. 对齐与重采样
    4. 计算W3/W4比值图
    5. 定义径向分析区域并提取比值
    6. 直方图分析与阈值确定
    7. 生成PDR区域Mask
    8. 生成Cavity和External区域Mask
    9. 保存Mask和相关信息

    Args:
        source_name: 源名称
        wcs: 世界坐标系（用于兼容性，实际使用WISE数据的WCS）
        center_coord: 中心坐标 (SkyCoord对象)
        r_eff: 有效半径（度）
        wise_base_path: WISE数据基础路径
        config_path: 配置文件路径
        output_dir: 输出目录
        save_plots: 是否保存图像

    Returns:
        tuple: (pdr_mask, cavity_mask, external_mask)
    """
    logger.info(f"使用标准天文流程定义源{source_name}的PDR区域掩模")

    try:
        # 加载配置
        config = load_config(config_path)
        pdr_params = config.get('processing', {}).get('region_mapping', {}).get('pdr', {})

        # 获取参数
        background_box_size = pdr_params.get('background_box_size', 50)
        background_filter_size = pdr_params.get('background_filter_size', 3)
        radial_analysis_factor = pdr_params.get('radial_analysis_factor', 3.0)
        histogram_bins = pdr_params.get('histogram_bins', 100)
        remove_small_objects_size = pdr_params.get('morphology_remove_small_objects', 100)

        # 步骤1: 加载WISE图像并进行初步处理
        w3_data, w3_header, w3_wcs, w4_data, w4_header, w4_wcs = load_and_process_wise_images(
            source_name, wise_base_path, config_path)

        # 步骤2: 背景估计与扣除
        (w3_data_sub, w4_data_sub, w3_background, w4_background,
         w3_bkg_mask, w4_bkg_mask) = estimate_and_subtract_background(
            w3_data, w4_data, center_coord, w3_wcs, r_eff,
            background_box_size, background_filter_size)

        # 步骤3: 对齐与重采样
        w3_reprojected, ratio_wcs, ratio_shape = align_and_resample_images(
            w3_data_sub, w3_wcs, w4_data_sub, w4_wcs)

        # 步骤4: 计算W3/W4比值图
        ratio_map = calculate_ratio_map(w3_reprojected, w4_data_sub)

        # 步骤5: 定义径向分析区域并提取比值
        ratio_values_in_area, radial_mask, center_pixel, r_eff_pixels = extract_radial_analysis_region(
            ratio_map, ratio_wcs, center_coord, r_eff, radial_analysis_factor)

        # 步骤6: 直方图分析与阈值确定
        pdr_threshold = analyze_histogram_and_determine_threshold(
            ratio_values_in_area, histogram_bins, output_dir, source_name)

        # 步骤7: 生成PDR区域Mask
        pdr_mask = generate_pdr_mask(ratio_map, pdr_threshold, radial_mask,
                                   remove_small_objects_size)

        # 步骤8: 生成Cavity和External区域Mask
        cavity_mask, external_mask = generate_cavity_and_external_masks(
            pdr_mask, center_pixel, r_eff_pixels, ratio_shape)

        # 步骤9: 保存Mask和相关信息
        if output_dir:
            save_masks_and_info(pdr_mask, cavity_mask, external_mask, ratio_wcs,
                               output_dir, source_name)

        # 创建可视化
        if save_plots and output_dir:
            create_visualization(w3_data_sub, w4_data_sub, ratio_map, pdr_mask,
                               cavity_mask, external_mask, center_pixel,
                               r_eff_pixels, pdr_threshold, output_dir, source_name,
                               w3_bkg_mask, w4_bkg_mask)

        logger.info(f"标准天文流程PDR掩模创建完成")
        logger.info(f"PDR区域: {np.sum(pdr_mask)} 像素")
        logger.info(f"Cavity区域: {np.sum(cavity_mask)} 像素")
        logger.info(f"External区域: {np.sum(external_mask)} 像素")

        return pdr_mask, cavity_mask, external_mask

    except Exception as e:
        logger.error(f"标准天文流程PDR掩模创建失败: {str(e)}")
        # 返回空掩模
        empty_mask = np.zeros((100, 100), dtype=bool)
        return empty_mask, empty_mask, empty_mask
