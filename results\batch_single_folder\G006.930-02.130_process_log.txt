处理时间: 2025-05-20 09:56:37
耗时: 163.03秒

标准输出:

未能估计G006.930-02.130的分子云距离


标准错误:
2025-05-20 09:53:57,675 - main_single_folder - INFO - 输出目录: results/batch_single_folder
2025-05-20 09:53:57,676 - main_single_folder - INFO - 加载源G006.930-02.130的信息
2025-05-20 09:53:57,676 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
2025-05-20 09:53:57,681 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-05-20 09:53:57,683 - main_single_folder - INFO - 从源名解析银道坐标: l=6.930000, b=-2.130000
2025-05-20 09:53:57,689 - main_single_folder - INFO - 银道坐标转换为赤道坐标: RA=272.345673, Dec=-24.007104 (ICRS)
2025-05-20 09:53:57,689 - main_single_folder - INFO - 从源表加载信息: RA=272.345673, Dec=-24.007104, R_eff=0.132500度
2025-05-20 09:53:57,690 - main_single_folder - INFO - 步骤1：加载WISE数据
2025-05-20 09:53:57,695 - data_manager - INFO - 为源G006.930-02.130加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.930-02.130
2025-05-20 09:53:57,720 - data_manager - INFO - 目录中的所有文件: ['G006.930-02.130_IRIS_100.fits', 'G006.930-02.130_NVSS.fits', 'G006.930-02.130_WISE_12.fits', 'G006.930-02.130_WISE_22.fits', 'G006.930-02.130_WISE_3.4.fits', 'G006.930-02.130_WISE_4.6.fits']
2025-05-20 09:53:57,720 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:53:57,720 - data_manager - INFO - 第一次匹配结果: ['G006.930-02.130_WISE_12.fits']
2025-05-20 09:53:57,720 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.930-02.130\G006.930-02.130_WISE_12.fits
2025-05-20 09:53:57,797 - data_manager - INFO - FITS数据统计: 最小值=976.0159912109375, 最大值=5459.326171875, 均值=1171.2210693359375, 中位数=1142.5416259765625
2025-05-20 09:53:57,798 - data_manager - INFO - 有效数据点数量: 537284/537289 (100.00%)
2025-05-20 09:53:57,806 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (733, 733)
2025-05-20 09:53:57,806 - main_single_folder - INFO - WISE数据加载完成
2025-05-20 09:53:57,806 - main_single_folder - INFO - 步骤2：区域映射
2025-05-20 09:53:57,823 - main_single_folder - INFO - 使用W3/W4波段比值方法定义PDR区域
2025-05-20 09:53:57,823 - region_mapper - INFO - 使用W3/W4波段比值方法定义PDR掩模
2025-05-20 09:53:57,824 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:53:57,824 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:53:57,824 - region_mapper.ratio - INFO - 加载源G006.930-02.130的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:53:57,824 - region_mapper.ratio - INFO - 加载源G006.930-02.130的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:53:57,824 - data_manager - INFO - 为源G006.930-02.130加载多波段WISE数据: ('12', '22')
2025-05-20 09:53:57,829 - data_manager - INFO - 为源G006.930-02.130加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.930-02.130
2025-05-20 09:53:57,829 - data_manager - INFO - 目录中的所有文件: ['G006.930-02.130_IRIS_100.fits', 'G006.930-02.130_NVSS.fits', 'G006.930-02.130_WISE_12.fits', 'G006.930-02.130_WISE_22.fits', 'G006.930-02.130_WISE_3.4.fits', 'G006.930-02.130_WISE_4.6.fits']
2025-05-20 09:53:57,829 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:53:57,830 - data_manager - INFO - 第一次匹配结果: ['G006.930-02.130_WISE_12.fits']
2025-05-20 09:53:57,830 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.930-02.130\G006.930-02.130_WISE_12.fits
2025-05-20 09:53:57,843 - data_manager - INFO - FITS数据统计: 最小值=976.0159912109375, 最大值=5459.326171875, 均值=1171.2210693359375, 中位数=1142.5416259765625
2025-05-20 09:53:57,843 - data_manager - INFO - 有效数据点数量: 537284/537289 (100.00%)
2025-05-20 09:53:57,918 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (733, 733)
2025-05-20 09:53:57,919 - data_manager - INFO - 使用12μm波段的WCS作为参考
2025-05-20 09:53:57,919 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (733, 733)
2025-05-20 09:53:57,924 - data_manager - INFO - 为源G006.930-02.130加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.930-02.130
2025-05-20 09:53:57,925 - data_manager - INFO - 目录中的所有文件: ['G006.930-02.130_IRIS_100.fits', 'G006.930-02.130_NVSS.fits', 'G006.930-02.130_WISE_12.fits', 'G006.930-02.130_WISE_22.fits', 'G006.930-02.130_WISE_3.4.fits', 'G006.930-02.130_WISE_4.6.fits']
2025-05-20 09:53:57,925 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:53:57,925 - data_manager - INFO - 第一次匹配结果: ['G006.930-02.130_WISE_22.fits']
2025-05-20 09:53:57,925 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.930-02.130\G006.930-02.130_WISE_22.fits
2025-05-20 09:53:57,947 - data_manager - INFO - FITS数据统计: 最小值=320.6920166015625, 最大值=1049.365234375, 均值=330.2945556640625, 中位数=328.08502197265625
2025-05-20 09:53:57,947 - data_manager - INFO - 有效数据点数量: 157609/157609 (100.00%)
2025-05-20 09:53:57,955 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (397, 397)
2025-05-20 09:53:57,955 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (397, 397)
2025-05-20 09:53:57,956 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w3', 'w4']
2025-05-20 09:53:57,962 - region_mapper.ratio - INFO - 调整W4波段数据从(397, 397)到(733, 733)
2025-05-20 09:53:57,962 - region_mapper.ratio - INFO - 调整W4波段数据从(397, 397)到(733, 733)
2025-05-20 09:53:58,070 - region_mapper.ratio - INFO - W3/W4比值范围: 3.12-5.17，均值: 3.54
2025-05-20 09:53:58,070 - region_mapper.ratio - INFO - W3/W4比值范围: 3.12-5.17，均值: 3.54
2025-05-20 09:53:58,078 - region_mapper.ratio - INFO - 像素尺度: 505.35 像素/角秒
2025-05-20 09:53:58,078 - region_mapper.ratio - INFO - 像素尺度: 505.35 像素/角秒
2025-05-20 09:53:58,078 - region_mapper.ratio - INFO - 有效半径: 0.9 像素
2025-05-20 09:53:58,078 - region_mapper.ratio - INFO - 有效半径: 0.9 像素
2025-05-20 09:53:58,078 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:53:58,078 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:53:58,811 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.719
2025-05-20 09:53:58,811 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.719
2025-05-20 09:53:58,889 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.608
2025-05-20 09:53:58,889 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.608
2025-05-20 09:53:58,967 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.504
2025-05-20 09:53:58,967 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.504
2025-05-20 09:53:58,968 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 09:53:58,968 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 09:53:59,036 - region_mapper.ratio - INFO - 聚类 0: 平均比值=3.52±0.04, 大小=282, 平均距离=6.4, 紧凑性=6.4
2025-05-20 09:53:59,036 - region_mapper.ratio - INFO - 聚类 0: 平均比值=3.52±0.04, 大小=282, 平均距离=6.4, 紧凑性=6.4
2025-05-20 09:53:59,037 - region_mapper.ratio - INFO - 聚类 1: 平均比值=3.69±0.10, 大小=28, 平均距离=9.1, 紧凑性=7.0
2025-05-20 09:53:59,037 - region_mapper.ratio - INFO - 聚类 1: 平均比值=3.69±0.10, 大小=28, 平均距离=9.1, 紧凑性=7.0
2025-05-20 09:53:59,037 - region_mapper.ratio - INFO - 选择聚类 1 作为PDR区域
2025-05-20 09:53:59,037 - region_mapper.ratio - INFO - 选择聚类 1 作为PDR区域
2025-05-20 09:53:59,099 - region_mapper.ratio - INFO - PDR掩模覆盖28个像素
2025-05-20 09:53:59,099 - region_mapper.ratio - INFO - PDR掩模覆盖28个像素
2025-05-20 09:53:59,099 - region_mapper.ratio - WARNING - PDR区域太小 (28 < 100)，使用备用方法
2025-05-20 09:53:59,099 - region_mapper.ratio - WARNING - PDR区域太小 (28 < 100)，使用备用方法
2025-05-20 09:53:59,202 - region_mapper.ratio - INFO - 备用方法后PDR掩模覆盖310个像素
2025-05-20 09:53:59,202 - region_mapper.ratio - INFO - 备用方法后PDR掩模覆盖310个像素
2025-05-20 09:54:03,604 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G006.930-02.130_w3w4_ratio.png
2025-05-20 09:54:03,604 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G006.930-02.130_w3w4_ratio.png
2025-05-20 09:54:03,605 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖310个像素
2025-05-20 09:54:03,605 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖310个像素
2025-05-20 09:54:03,649 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G006.930-02.130_region_masks.fits
2025-05-20 09:54:03,649 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G006.930-02.130_region_masks.fits
2025-05-20 09:54:03,655 - data_manager - INFO - 为源G006.930-02.130加载Gaia数据，中心坐标: RA=272.345673, Dec=-24.007104, 半径=0.6625度
2025-05-20 09:54:03,657 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances-Data/gaia_data\gaia_G006.930-02.130_40arcmin_5R.csv
H:\Augment\Parallax distances\src\data_manager.py:87: DtypeWarning: Columns (47,52,54) have mixed types. Specify dtype option on import or set low_memory=False.
  df = pd.read_csv(gaia_file)
2025-05-20 09:54:10,582 - data_manager - INFO - 成功加载Gaia数据，共472624条记录
2025-05-20 09:54:10,940 - data_manager - INFO - 成功加载Gaia数据，共472619个源在搜索半径内
2025-05-20 09:54:12,011 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.930-02.130_gaia_regions.fits
2025-05-20 09:56:14,333 - data_manager - WARNING - 检测到可能导致保存问题的列: ['_2MASS', 'twomass__2MASS', 'region']
2025-05-20 09:56:14,479 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:56:24,152 - data_manager - ERROR - 保存数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U10'), dtype('<U9'), dtype('<U11'), dtype('<U1')}
2025-05-20 09:56:24,153 - main_single_folder - ERROR - 加载Gaia数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U10'), dtype('<U9'), dtype('<U11'), dtype('<U1')}
2025-05-20 09:56:24,153 - main_single_folder - WARNING - 将使用空的Gaia数据继续处理
2025-05-20 09:56:24,454 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:56:24,454 - main_single_folder - INFO - 步骤3：恒星选择
2025-05-20 09:56:24,459 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-05-20 09:56:24,460 - star_selector - WARNING - 没有有效的视差数据，跳过视差信噪比筛选
2025-05-20 09:56:24,460 - star_selector - WARNING - 没有有效的RUWE数据，跳过RUWE筛选
2025-05-20 09:56:24,460 - star_selector - INFO - 质量筛选详细信息:
2025-05-20 09:56:24,460 - star_selector - INFO -   视差信噪比阈值: 5.0
2025-05-20 09:56:24,461 - star_selector - INFO -   RUWE阈值: 1.4
2025-05-20 09:56:24,461 - star_selector - INFO -   总体通过率: 0.0%
2025-05-20 09:56:24,461 - star_selector - INFO - 质量筛选完成，保留0/0个源
2025-05-20 09:56:24,461 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.930-02.130_filtered_data.fits
2025-05-20 09:56:24,474 - data_manager - INFO - 成功保存数据
2025-05-20 09:56:24,474 - main_single_folder - INFO - 保存质量筛选后的数据到: results/batch_single_folder\processed\G006.930-02.130_filtered_data.fits
2025-05-20 09:56:24,479 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-05-20 09:56:24,479 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-05-20 09:56:24,479 - star_selector - INFO - 筛选恒星样本，原始样本大小: 0
2025-05-20 09:56:24,479 - star_selector - INFO - 排除YSO后: 0/0个源
2025-05-20 09:56:24,480 - star_selector - INFO - 区域 cavity: 0个源
2025-05-20 09:56:24,480 - star_selector - INFO - 区域 pdr: 0个源
2025-05-20 09:56:24,480 - star_selector - INFO - 区域 external: 0个源
2025-05-20 09:56:24,481 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 0
2025-05-20 09:56:24,481 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.930-02.130_star_sample.fits
2025-05-20 09:56:24,517 - data_manager - INFO - 成功保存数据
2025-05-20 09:56:24,517 - main_single_folder - INFO - 恒星选择完成
2025-05-20 09:56:24,517 - main_single_folder - INFO - 步骤4：消光计算
2025-05-20 09:56:24,522 - extinction_estimator - INFO - 为0个恒星计算A_V，参数: {'rv': 3.1}
2025-05-20 09:56:24,522 - extinction_estimator - WARNING - 星表为空，无法计算A_V
2025-05-20 09:56:24,522 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G006.930-02.130_stars_with_av.fits
2025-05-20 09:56:24,534 - data_manager - INFO - 成功保存数据
2025-05-20 09:56:24,534 - main_single_folder - INFO - 消光计算完成
2025-05-20 09:56:24,534 - main_single_folder - INFO - 步骤5：距离分析
2025-05-20 09:56:24,534 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-05-20 09:56:24,535 - distance_analyzer - INFO - 将分析0个区域: 
2025-05-20 09:56:24,535 - distance_analyzer - INFO - 估计分子云距离
2025-05-20 09:56:24,535 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity, pdr, external
2025-05-20 09:56:24,535 - distance_analyzer - WARNING - 没有检测到任何跳变，无法估计距离
2025-05-20 09:56:24,536 - main_single_folder - INFO - 距离分析完成
2025-05-20 09:56:24,536 - main_single_folder - INFO - 步骤6：可视化和报告生成
2025-05-20 09:56:24,541 - data_manager - INFO - 为源G006.930-02.130加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.930-02.130
2025-05-20 09:56:24,541 - data_manager - INFO - 目录中的所有文件: ['G006.930-02.130_IRIS_100.fits', 'G006.930-02.130_NVSS.fits', 'G006.930-02.130_WISE_12.fits', 'G006.930-02.130_WISE_22.fits', 'G006.930-02.130_WISE_3.4.fits', 'G006.930-02.130_WISE_4.6.fits']
2025-05-20 09:56:24,541 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:56:24,541 - data_manager - INFO - 第一次匹配结果: ['G006.930-02.130_WISE_12.fits']
2025-05-20 09:56:24,542 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.930-02.130\G006.930-02.130_WISE_12.fits
2025-05-20 09:56:24,555 - data_manager - INFO - FITS数据统计: 最小值=976.0159912109375, 最大值=5459.326171875, 均值=1171.2210693359375, 中位数=1142.5416259765625
2025-05-20 09:56:24,556 - data_manager - INFO - 有效数据点数量: 537284/537289 (100.00%)
2025-05-20 09:56:24,563 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (733, 733)
2025-05-20 09:56:24,585 - visualizer - INFO - 绘制WISE图像和区域掩模，输出到: results/batch_single_folder\visualizations\G006.930-02.130_wise_regions_stars.png
2025-05-20 09:56:24,699 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:56:26,814 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:56:26,822 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:56:30,797 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G006.930-02.130_wise_regions_stars.png
2025-05-20 09:56:30,797 - main_single_folder - INFO - 成功保存WISE图像和区域掩模到: results/batch_single_folder\visualizations\G006.930-02.130_wise_regions_stars.png
2025-05-20 09:56:30,797 - main_single_folder - INFO - 加载WISE多波段数据用于RGB图像生成
2025-05-20 09:56:30,798 - data_manager - INFO - 为源G006.930-02.130加载多波段WISE数据: ('3.4', '12', '22')
2025-05-20 09:56:30,802 - data_manager - INFO - 为源G006.930-02.130加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.930-02.130
2025-05-20 09:56:30,803 - data_manager - INFO - 目录中的所有文件: ['G006.930-02.130_IRIS_100.fits', 'G006.930-02.130_NVSS.fits', 'G006.930-02.130_WISE_12.fits', 'G006.930-02.130_WISE_22.fits', 'G006.930-02.130_WISE_3.4.fits', 'G006.930-02.130_WISE_4.6.fits']
2025-05-20 09:56:30,803 - data_manager - INFO - 查找WISE 3.4μm波段的FITS文件
2025-05-20 09:56:30,803 - data_manager - INFO - 第一次匹配结果: ['G006.930-02.130_WISE_3.4.fits']
2025-05-20 09:56:30,803 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.930-02.130\G006.930-02.130_WISE_3.4.fits
2025-05-20 09:56:30,896 - data_manager - INFO - FITS数据统计: 最小值=19.50067901611328, 最大值=2845.96923828125, 均值=144.2202911376953, 中位数=94.14313507080078
2025-05-20 09:56:30,896 - data_manager - INFO - 有效数据点数量: 609953/609961 (100.00%)
2025-05-20 09:56:30,905 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (781, 781)
2025-05-20 09:56:30,905 - data_manager - INFO - 使用3.4μm波段的WCS作为参考
2025-05-20 09:56:30,906 - data_manager - INFO - 成功加载WISE 3.4μm波段数据，尺寸: (781, 781)
2025-05-20 09:56:30,911 - data_manager - INFO - 为源G006.930-02.130加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.930-02.130
2025-05-20 09:56:30,911 - data_manager - INFO - 目录中的所有文件: ['G006.930-02.130_IRIS_100.fits', 'G006.930-02.130_NVSS.fits', 'G006.930-02.130_WISE_12.fits', 'G006.930-02.130_WISE_22.fits', 'G006.930-02.130_WISE_3.4.fits', 'G006.930-02.130_WISE_4.6.fits']
2025-05-20 09:56:30,911 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:56:30,911 - data_manager - INFO - 第一次匹配结果: ['G006.930-02.130_WISE_12.fits']
2025-05-20 09:56:30,912 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.930-02.130\G006.930-02.130_WISE_12.fits
2025-05-20 09:56:30,925 - data_manager - INFO - FITS数据统计: 最小值=976.0159912109375, 最大值=5459.326171875, 均值=1171.2210693359375, 中位数=1142.5416259765625
2025-05-20 09:56:30,925 - data_manager - INFO - 有效数据点数量: 537284/537289 (100.00%)
2025-05-20 09:56:30,933 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (733, 733)
2025-05-20 09:56:30,934 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (733, 733)
2025-05-20 09:56:30,939 - data_manager - INFO - 为源G006.930-02.130加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G006.930-02.130
2025-05-20 09:56:30,939 - data_manager - INFO - 目录中的所有文件: ['G006.930-02.130_IRIS_100.fits', 'G006.930-02.130_NVSS.fits', 'G006.930-02.130_WISE_12.fits', 'G006.930-02.130_WISE_22.fits', 'G006.930-02.130_WISE_3.4.fits', 'G006.930-02.130_WISE_4.6.fits']
2025-05-20 09:56:30,939 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:56:30,940 - data_manager - INFO - 第一次匹配结果: ['G006.930-02.130_WISE_22.fits']
2025-05-20 09:56:30,940 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G006.930-02.130\G006.930-02.130_WISE_22.fits
2025-05-20 09:56:30,944 - data_manager - INFO - FITS数据统计: 最小值=320.6920166015625, 最大值=1049.365234375, 均值=330.2945556640625, 中位数=328.08502197265625
2025-05-20 09:56:30,944 - data_manager - INFO - 有效数据点数量: 157609/157609 (100.00%)
2025-05-20 09:56:30,952 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (397, 397)
2025-05-20 09:56:30,953 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (397, 397)
2025-05-20 09:56:30,953 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w1', 'w3', 'w4']
2025-05-20 09:56:30,953 - visualizer - INFO - 创建WISE三波段RGB图像，输出到: results/batch_single_folder\visualizations\G006.930-02.130_wise_rgb.png
2025-05-20 09:56:31,085 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:56:31,085 - visualizer - INFO - 波段 3.4μm 的尺寸: (781, 781)
2025-05-20 09:56:31,085 - visualizer - INFO - 波段 12μm 的尺寸: (733, 733)
2025-05-20 09:56:31,086 - visualizer - INFO - 波段 22μm 的尺寸: (397, 397)
2025-05-20 09:56:31,086 - visualizer - INFO - Using 12μm band size as target: (733, 733)
2025-05-20 09:56:31,086 - visualizer - INFO - Using target image size: (733, 733), original sizes: {'w1': (781, 781), 'w3': (733, 733), 'w4': (397, 397)}
2025-05-20 09:56:31,155 - visualizer - INFO - 调整w4波段数据从(397, 397)到(733, 733)
2025-05-20 09:56:31,157 - visualizer - INFO - 红色通道(22μm)数据范围: 320.70562744140625-965.5274047851562
2025-05-20 09:56:31,163 - visualizer - INFO - 绿色通道(12μm)数据范围: 976.0159912109375-5459.326171875
2025-05-20 09:56:31,266 - visualizer - INFO - 调整w1波段数据从(781, 781)到(733, 733)
2025-05-20 09:56:31,269 - visualizer - INFO - 蓝色通道(3.4μm)数据范围: 1.182132363319397-2696.93359375
2025-05-20 09:56:31,291 - visualizer - INFO - 使用Lupton RGB方法，stretch=1192.22, Q=10
2025-05-20 09:56:31,291 - visualizer - INFO - 各波段99.5%百分位数: R=402.71, G=2384.44, B=1223.12
2025-05-20 09:56:31,397 - visualizer - INFO - RGB数据形状: (733, 733, 3), 类型: float32
2025-05-20 09:56:31,401 - visualizer - INFO - Red通道数据范围：0.062745101749897-0.4470588266849518，均值：0.1514
2025-05-20 09:56:31,403 - visualizer - INFO - Green通道数据范围：0.0-1.0，均值：0.5377
2025-05-20 09:56:31,405 - visualizer - INFO - Blue通道数据范围：0.0-0.6470588445663452，均值：0.0608
2025-05-20 09:56:32,872 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:56:32,878 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:56:32,916 - visualizer - INFO - 按照要求不显示Gaia星
2025-05-20 09:56:32,918 - visualizer - INFO - 设置坐标刻度间隔: RA=10.000度, Dec=10.000度
2025-05-20 09:56:36,881 - visualizer - INFO - 成功保存RGB图像到: results/batch_single_folder\visualizations\G006.930-02.130_wise_rgb.png
2025-05-20 09:56:36,883 - main_single_folder - INFO - 成功保存WISE RGB图像到: results/batch_single_folder\visualizations\G006.930-02.130_wise_rgb.png
2025-05-20 09:56:36,883 - main_single_folder - WARNING - 没有足够的数据绘制消光-距离散点图
2025-05-20 09:56:36,900 - main_single_folder - INFO - 成功保存处理报告到: results/batch_single_folder\reports\G006.930-02.130_report.txt
2025-05-20 09:56:36,900 - main_single_folder - INFO - 处理G006.930-02.130完成
