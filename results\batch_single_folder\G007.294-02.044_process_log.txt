处理时间: 2025-05-20 10:02:32
耗时: 78.42秒

标准输出:

未能估计G007.294-02.044的分子云距离


标准错误:
2025-05-20 10:01:17,732 - main_single_folder - INFO - 输出目录: results/batch_single_folder
2025-05-20 10:01:17,732 - main_single_folder - INFO - 加载源G007.294-02.044的信息
2025-05-20 10:01:17,733 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
2025-05-20 10:01:17,738 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-05-20 10:01:17,740 - main_single_folder - INFO - 从源名解析银道坐标: l=7.294000, b=-2.044000
2025-05-20 10:01:17,746 - main_single_folder - INFO - 银道坐标转换为赤道坐标: RA=272.455287, Dec=-23.647018 (ICRS)
2025-05-20 10:01:17,746 - main_single_folder - INFO - 从源表加载信息: RA=272.455287, Dec=-23.647018, R_eff=0.085000度
2025-05-20 10:01:17,747 - main_single_folder - INFO - 步骤1：加载WISE数据
2025-05-20 10:01:17,752 - data_manager - INFO - 为源G007.294-02.044加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G007.294-02.044
2025-05-20 10:01:17,764 - data_manager - INFO - 目录中的所有文件: ['G007.294-02.044_IRIS_100.fits', 'G007.294-02.044_NVSS.fits', 'G007.294-02.044_WISE_12.fits', 'G007.294-02.044_WISE_22.fits', 'G007.294-02.044_WISE_3.4.fits', 'G007.294-02.044_WISE_4.6.fits']
2025-05-20 10:01:17,764 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 10:01:17,765 - data_manager - INFO - 第一次匹配结果: ['G007.294-02.044_WISE_12.fits']
2025-05-20 10:01:17,765 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G007.294-02.044\G007.294-02.044_WISE_12.fits
2025-05-20 10:01:17,831 - data_manager - INFO - FITS数据统计: 最小值=1013.46044921875, 最大值=5148.5810546875, 均值=1214.3673095703125, 中位数=1166.866455078125
2025-05-20 10:01:17,832 - data_manager - INFO - 有效数据点数量: 220900/220900 (100.00%)
2025-05-20 10:01:17,840 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (470, 470)
2025-05-20 10:01:17,840 - main_single_folder - INFO - WISE数据加载完成
2025-05-20 10:01:17,840 - main_single_folder - INFO - 步骤2：区域映射
2025-05-20 10:01:17,847 - main_single_folder - INFO - 使用W3/W4波段比值方法定义PDR区域
2025-05-20 10:01:17,847 - region_mapper - INFO - 使用W3/W4波段比值方法定义PDR掩模
2025-05-20 10:01:17,848 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 10:01:17,848 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 10:01:17,848 - region_mapper.ratio - INFO - 加载源G007.294-02.044的W3(12μm)和W4(22μm)波段数据
2025-05-20 10:01:17,848 - region_mapper.ratio - INFO - 加载源G007.294-02.044的W3(12μm)和W4(22μm)波段数据
2025-05-20 10:01:17,848 - data_manager - INFO - 为源G007.294-02.044加载多波段WISE数据: ('12', '22')
2025-05-20 10:01:17,853 - data_manager - INFO - 为源G007.294-02.044加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G007.294-02.044
2025-05-20 10:01:17,853 - data_manager - INFO - 目录中的所有文件: ['G007.294-02.044_IRIS_100.fits', 'G007.294-02.044_NVSS.fits', 'G007.294-02.044_WISE_12.fits', 'G007.294-02.044_WISE_22.fits', 'G007.294-02.044_WISE_3.4.fits', 'G007.294-02.044_WISE_4.6.fits']
2025-05-20 10:01:17,853 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 10:01:17,853 - data_manager - INFO - 第一次匹配结果: ['G007.294-02.044_WISE_12.fits']
2025-05-20 10:01:17,853 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G007.294-02.044\G007.294-02.044_WISE_12.fits
2025-05-20 10:01:17,859 - data_manager - INFO - FITS数据统计: 最小值=1013.46044921875, 最大值=5148.5810546875, 均值=1214.3673095703125, 中位数=1166.866455078125
2025-05-20 10:01:17,859 - data_manager - INFO - 有效数据点数量: 220900/220900 (100.00%)
2025-05-20 10:01:17,939 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (470, 470)
2025-05-20 10:01:17,940 - data_manager - INFO - 使用12μm波段的WCS作为参考
2025-05-20 10:01:17,940 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (470, 470)
2025-05-20 10:01:17,945 - data_manager - INFO - 为源G007.294-02.044加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G007.294-02.044
2025-05-20 10:01:17,945 - data_manager - INFO - 目录中的所有文件: ['G007.294-02.044_IRIS_100.fits', 'G007.294-02.044_NVSS.fits', 'G007.294-02.044_WISE_12.fits', 'G007.294-02.044_WISE_22.fits', 'G007.294-02.044_WISE_3.4.fits', 'G007.294-02.044_WISE_4.6.fits']
2025-05-20 10:01:17,945 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 10:01:17,946 - data_manager - INFO - 第一次匹配结果: ['G007.294-02.044_WISE_22.fits']
2025-05-20 10:01:17,946 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G007.294-02.044\G007.294-02.044_WISE_22.fits
2025-05-20 10:01:17,977 - data_manager - INFO - FITS数据统计: 最小值=320.319580078125, 最大值=1043.189208984375, 均值=331.048828125, 中位数=327.4461669921875
2025-05-20 10:01:17,977 - data_manager - INFO - 有效数据点数量: 65025/65025 (100.00%)
2025-05-20 10:01:17,984 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (255, 255)
2025-05-20 10:01:17,985 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (255, 255)
2025-05-20 10:01:17,985 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w3', 'w4']
2025-05-20 10:01:17,988 - region_mapper.ratio - INFO - 调整W4波段数据从(255, 255)到(470, 470)
2025-05-20 10:01:17,988 - region_mapper.ratio - INFO - 调整W4波段数据从(255, 255)到(470, 470)
2025-05-20 10:01:18,033 - region_mapper.ratio - INFO - W3/W4比值范围: 3.22-5.31，均值: 3.66
2025-05-20 10:01:18,033 - region_mapper.ratio - INFO - W3/W4比值范围: 3.22-5.31，均值: 3.66
2025-05-20 10:01:18,041 - region_mapper.ratio - INFO - 像素尺度: 506.51 像素/角秒
2025-05-20 10:01:18,041 - region_mapper.ratio - INFO - 像素尺度: 506.51 像素/角秒
2025-05-20 10:01:18,041 - region_mapper.ratio - INFO - 有效半径: 0.6 像素
2025-05-20 10:01:18,041 - region_mapper.ratio - INFO - 有效半径: 0.6 像素
2025-05-20 10:01:18,042 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 10:01:18,042 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 10:01:18,783 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.722
2025-05-20 10:01:18,783 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.722
2025-05-20 10:01:18,861 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.591
2025-05-20 10:01:18,861 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.591
2025-05-20 10:01:18,938 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.604
2025-05-20 10:01:18,938 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.604
2025-05-20 10:01:18,938 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 10:01:18,938 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 10:01:19,003 - region_mapper.ratio - INFO - 聚类 0: 平均比值=4.35±0.13, 大小=278, 平均距离=6.4, 紧凑性=6.4
2025-05-20 10:01:19,003 - region_mapper.ratio - INFO - 聚类 0: 平均比值=4.35±0.13, 大小=278, 平均距离=6.4, 紧凑性=6.4
2025-05-20 10:01:19,004 - region_mapper.ratio - INFO - 聚类 1: 平均比值=4.97±0.22, 大小=35, 平均距离=8.4, 紧凑性=3.5
2025-05-20 10:01:19,004 - region_mapper.ratio - INFO - 聚类 1: 平均比值=4.97±0.22, 大小=35, 平均距离=8.4, 紧凑性=3.5
2025-05-20 10:01:19,004 - region_mapper.ratio - INFO - 选择聚类 1 作为PDR区域
2025-05-20 10:01:19,004 - region_mapper.ratio - INFO - 选择聚类 1 作为PDR区域
2025-05-20 10:01:19,030 - region_mapper.ratio - INFO - PDR掩模覆盖36个像素
2025-05-20 10:01:19,030 - region_mapper.ratio - INFO - PDR掩模覆盖36个像素
2025-05-20 10:01:19,031 - region_mapper.ratio - WARNING - PDR区域太小 (36 < 100)，使用备用方法
2025-05-20 10:01:19,031 - region_mapper.ratio - WARNING - PDR区域太小 (36 < 100)，使用备用方法
2025-05-20 10:01:19,073 - region_mapper.ratio - INFO - 备用方法后PDR掩模覆盖313个像素
2025-05-20 10:01:19,073 - region_mapper.ratio - INFO - 备用方法后PDR掩模覆盖313个像素
2025-05-20 10:01:23,151 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G007.294-02.044_w3w4_ratio.png
2025-05-20 10:01:23,151 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G007.294-02.044_w3w4_ratio.png
2025-05-20 10:01:23,152 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖313个像素
2025-05-20 10:01:23,152 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖313个像素
2025-05-20 10:01:23,185 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G007.294-02.044_region_masks.fits
2025-05-20 10:01:23,185 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G007.294-02.044_region_masks.fits
2025-05-20 10:01:23,191 - data_manager - INFO - 为源G007.294-02.044加载Gaia数据，中心坐标: RA=272.455287, Dec=-23.647018, 半径=0.4250度
2025-05-20 10:01:23,193 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances-Data/gaia_data\gaia_G007.294-02.044_26arcmin_5R.csv
H:\Augment\Parallax distances\src\data_manager.py:87: DtypeWarning: Columns (47,52,54) have mixed types. Specify dtype option on import or set low_memory=False.
  df = pd.read_csv(gaia_file)
2025-05-20 10:01:26,192 - data_manager - INFO - 成功加载Gaia数据，共191614条记录
2025-05-20 10:01:26,370 - data_manager - INFO - 成功加载Gaia数据，共191606个源在搜索半径内
2025-05-20 10:01:26,836 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G007.294-02.044_gaia_regions.fits
2025-05-20 10:02:19,652 - data_manager - WARNING - 检测到可能导致保存问题的列: ['region']
2025-05-20 10:02:19,763 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 10:02:24,484 - data_manager - ERROR - 保存数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U10'), dtype('<U9'), dtype('<U11'), dtype('<U1')}
2025-05-20 10:02:24,484 - main_single_folder - ERROR - 加载Gaia数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U10'), dtype('<U9'), dtype('<U11'), dtype('<U1')}
2025-05-20 10:02:24,485 - main_single_folder - WARNING - 将使用空的Gaia数据继续处理
2025-05-20 10:02:24,648 - main_single_folder - INFO - 区域映射完成
2025-05-20 10:02:24,648 - main_single_folder - INFO - 步骤3：恒星选择
2025-05-20 10:02:24,653 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-05-20 10:02:24,653 - star_selector - WARNING - 没有有效的视差数据，跳过视差信噪比筛选
2025-05-20 10:02:24,654 - star_selector - WARNING - 没有有效的RUWE数据，跳过RUWE筛选
2025-05-20 10:02:24,654 - star_selector - INFO - 质量筛选详细信息:
2025-05-20 10:02:24,654 - star_selector - INFO -   视差信噪比阈值: 5.0
2025-05-20 10:02:24,654 - star_selector - INFO -   RUWE阈值: 1.4
2025-05-20 10:02:24,655 - star_selector - INFO -   总体通过率: 0.0%
2025-05-20 10:02:24,655 - star_selector - INFO - 质量筛选完成，保留0/0个源
2025-05-20 10:02:24,655 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G007.294-02.044_filtered_data.fits
2025-05-20 10:02:24,669 - data_manager - INFO - 成功保存数据
2025-05-20 10:02:24,669 - main_single_folder - INFO - 保存质量筛选后的数据到: results/batch_single_folder\processed\G007.294-02.044_filtered_data.fits
2025-05-20 10:02:24,674 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-05-20 10:02:24,675 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-05-20 10:02:24,675 - star_selector - INFO - 筛选恒星样本，原始样本大小: 0
2025-05-20 10:02:24,675 - star_selector - INFO - 排除YSO后: 0/0个源
2025-05-20 10:02:24,676 - star_selector - INFO - 区域 cavity: 0个源
2025-05-20 10:02:24,676 - star_selector - INFO - 区域 pdr: 0个源
2025-05-20 10:02:24,676 - star_selector - INFO - 区域 external: 0个源
2025-05-20 10:02:24,676 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 0
2025-05-20 10:02:24,677 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G007.294-02.044_star_sample.fits
2025-05-20 10:02:24,690 - data_manager - INFO - 成功保存数据
2025-05-20 10:02:24,690 - main_single_folder - INFO - 恒星选择完成
2025-05-20 10:02:24,690 - main_single_folder - INFO - 步骤4：消光计算
2025-05-20 10:02:24,695 - extinction_estimator - INFO - 为0个恒星计算A_V，参数: {'rv': 3.1}
2025-05-20 10:02:24,695 - extinction_estimator - WARNING - 星表为空，无法计算A_V
2025-05-20 10:02:24,696 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G007.294-02.044_stars_with_av.fits
2025-05-20 10:02:24,708 - data_manager - INFO - 成功保存数据
2025-05-20 10:02:24,708 - main_single_folder - INFO - 消光计算完成
2025-05-20 10:02:24,708 - main_single_folder - INFO - 步骤5：距离分析
2025-05-20 10:02:24,708 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-05-20 10:02:24,709 - distance_analyzer - INFO - 将分析0个区域: 
2025-05-20 10:02:24,709 - distance_analyzer - INFO - 估计分子云距离
2025-05-20 10:02:24,709 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity, pdr, external
2025-05-20 10:02:24,709 - distance_analyzer - WARNING - 没有检测到任何跳变，无法估计距离
2025-05-20 10:02:24,710 - main_single_folder - INFO - 距离分析完成
2025-05-20 10:02:24,710 - main_single_folder - INFO - 步骤6：可视化和报告生成
2025-05-20 10:02:24,715 - data_manager - INFO - 为源G007.294-02.044加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G007.294-02.044
2025-05-20 10:02:24,715 - data_manager - INFO - 目录中的所有文件: ['G007.294-02.044_IRIS_100.fits', 'G007.294-02.044_NVSS.fits', 'G007.294-02.044_WISE_12.fits', 'G007.294-02.044_WISE_22.fits', 'G007.294-02.044_WISE_3.4.fits', 'G007.294-02.044_WISE_4.6.fits']
2025-05-20 10:02:24,715 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 10:02:24,715 - data_manager - INFO - 第一次匹配结果: ['G007.294-02.044_WISE_12.fits']
2025-05-20 10:02:24,716 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G007.294-02.044\G007.294-02.044_WISE_12.fits
2025-05-20 10:02:24,723 - data_manager - INFO - FITS数据统计: 最小值=1013.46044921875, 最大值=5148.5810546875, 均值=1214.3673095703125, 中位数=1166.866455078125
2025-05-20 10:02:24,723 - data_manager - INFO - 有效数据点数量: 220900/220900 (100.00%)
2025-05-20 10:02:24,732 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (470, 470)
2025-05-20 10:02:24,751 - visualizer - INFO - 绘制WISE图像和区域掩模，输出到: results/batch_single_folder\visualizations\G007.294-02.044_wise_regions_stars.png
2025-05-20 10:02:24,865 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 10:02:26,889 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 10:02:26,898 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 10:02:29,203 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G007.294-02.044_wise_regions_stars.png
2025-05-20 10:02:29,203 - main_single_folder - INFO - 成功保存WISE图像和区域掩模到: results/batch_single_folder\visualizations\G007.294-02.044_wise_regions_stars.png
2025-05-20 10:02:29,203 - main_single_folder - INFO - 加载WISE多波段数据用于RGB图像生成
2025-05-20 10:02:29,204 - data_manager - INFO - 为源G007.294-02.044加载多波段WISE数据: ('3.4', '12', '22')
2025-05-20 10:02:29,209 - data_manager - INFO - 为源G007.294-02.044加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G007.294-02.044
2025-05-20 10:02:29,209 - data_manager - INFO - 目录中的所有文件: ['G007.294-02.044_IRIS_100.fits', 'G007.294-02.044_NVSS.fits', 'G007.294-02.044_WISE_12.fits', 'G007.294-02.044_WISE_22.fits', 'G007.294-02.044_WISE_3.4.fits', 'G007.294-02.044_WISE_4.6.fits']
2025-05-20 10:02:29,209 - data_manager - INFO - 查找WISE 3.4μm波段的FITS文件
2025-05-20 10:02:29,209 - data_manager - INFO - 第一次匹配结果: ['G007.294-02.044_WISE_3.4.fits']
2025-05-20 10:02:29,209 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G007.294-02.044\G007.294-02.044_WISE_3.4.fits
2025-05-20 10:02:29,275 - data_manager - INFO - FITS数据统计: 最小值=17.75421905517578, 最大值=3092.060302734375, 均值=138.81573486328125, 中位数=90.34757995605469
2025-05-20 10:02:29,275 - data_manager - INFO - 有效数据点数量: 250998/251001 (100.00%)
2025-05-20 10:02:29,283 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (501, 501)
2025-05-20 10:02:29,284 - data_manager - INFO - 使用3.4μm波段的WCS作为参考
2025-05-20 10:02:29,284 - data_manager - INFO - 成功加载WISE 3.4μm波段数据，尺寸: (501, 501)
2025-05-20 10:02:29,289 - data_manager - INFO - 为源G007.294-02.044加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G007.294-02.044
2025-05-20 10:02:29,289 - data_manager - INFO - 目录中的所有文件: ['G007.294-02.044_IRIS_100.fits', 'G007.294-02.044_NVSS.fits', 'G007.294-02.044_WISE_12.fits', 'G007.294-02.044_WISE_22.fits', 'G007.294-02.044_WISE_3.4.fits', 'G007.294-02.044_WISE_4.6.fits']
2025-05-20 10:02:29,289 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 10:02:29,289 - data_manager - INFO - 第一次匹配结果: ['G007.294-02.044_WISE_12.fits']
2025-05-20 10:02:29,290 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G007.294-02.044\G007.294-02.044_WISE_12.fits
2025-05-20 10:02:29,295 - data_manager - INFO - FITS数据统计: 最小值=1013.46044921875, 最大值=5148.5810546875, 均值=1214.3673095703125, 中位数=1166.866455078125
2025-05-20 10:02:29,296 - data_manager - INFO - 有效数据点数量: 220900/220900 (100.00%)
2025-05-20 10:02:29,304 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (470, 470)
2025-05-20 10:02:29,304 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (470, 470)
2025-05-20 10:02:29,309 - data_manager - INFO - 为源G007.294-02.044加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G007.294-02.044
2025-05-20 10:02:29,310 - data_manager - INFO - 目录中的所有文件: ['G007.294-02.044_IRIS_100.fits', 'G007.294-02.044_NVSS.fits', 'G007.294-02.044_WISE_12.fits', 'G007.294-02.044_WISE_22.fits', 'G007.294-02.044_WISE_3.4.fits', 'G007.294-02.044_WISE_4.6.fits']
2025-05-20 10:02:29,310 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 10:02:29,310 - data_manager - INFO - 第一次匹配结果: ['G007.294-02.044_WISE_22.fits']
2025-05-20 10:02:29,310 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G007.294-02.044\G007.294-02.044_WISE_22.fits
2025-05-20 10:02:29,313 - data_manager - INFO - FITS数据统计: 最小值=320.319580078125, 最大值=1043.189208984375, 均值=331.048828125, 中位数=327.4461669921875
2025-05-20 10:02:29,314 - data_manager - INFO - 有效数据点数量: 65025/65025 (100.00%)
2025-05-20 10:02:29,321 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (255, 255)
2025-05-20 10:02:29,321 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (255, 255)
2025-05-20 10:02:29,322 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w1', 'w3', 'w4']
2025-05-20 10:02:29,322 - visualizer - INFO - 创建WISE三波段RGB图像，输出到: results/batch_single_folder\visualizations\G007.294-02.044_wise_rgb.png
2025-05-20 10:02:29,450 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 10:02:29,450 - visualizer - INFO - 波段 3.4μm 的尺寸: (501, 501)
2025-05-20 10:02:29,450 - visualizer - INFO - 波段 12μm 的尺寸: (470, 470)
2025-05-20 10:02:29,450 - visualizer - INFO - 波段 22μm 的尺寸: (255, 255)
2025-05-20 10:02:29,451 - visualizer - INFO - Using 12μm band size as target: (470, 470)
2025-05-20 10:02:29,451 - visualizer - INFO - Using target image size: (470, 470), original sizes: {'w1': (501, 501), 'w3': (470, 470), 'w4': (255, 255)}
2025-05-20 10:02:29,479 - visualizer - INFO - 调整w4波段数据从(255, 255)到(470, 470)
2025-05-20 10:02:29,480 - visualizer - INFO - 红色通道(22μm)数据范围: 320.319580078125-999.6171875
2025-05-20 10:02:29,482 - visualizer - INFO - 绿色通道(12μm)数据范围: 1013.46044921875-5148.5810546875
2025-05-20 10:02:29,520 - visualizer - INFO - 调整w1波段数据从(501, 501)到(470, 470)
2025-05-20 10:02:29,521 - visualizer - INFO - 蓝色通道(3.4μm)数据范围: 1.9852230548858643-2850.69921875
2025-05-20 10:02:29,529 - visualizer - INFO - 使用Lupton RGB方法，stretch=1210.87, Q=10
2025-05-20 10:02:29,529 - visualizer - INFO - 各波段99.5%百分位数: R=405.26, G=2421.74, B=1198.16
2025-05-20 10:02:29,559 - visualizer - INFO - RGB数据形状: (470, 470, 3), 类型: float32
2025-05-20 10:02:29,560 - visualizer - INFO - Red通道数据范围：0.06666667014360428-0.23137255012989044，均值：0.1487
2025-05-20 10:02:29,561 - visualizer - INFO - Green通道数据范围：0.3490196168422699-0.9372549057006836，均值：0.5464
2025-05-20 10:02:29,562 - visualizer - INFO - Blue通道数据范围：0.0-0.6078431606292725，均值：0.0574
2025-05-20 10:02:30,983 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 10:02:30,989 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 10:02:31,007 - visualizer - INFO - 按照要求不显示Gaia星
2025-05-20 10:02:31,009 - visualizer - INFO - 设置坐标刻度间隔: RA=10.000度, Dec=10.000度
2025-05-20 10:02:32,036 - visualizer - INFO - 成功保存RGB图像到: results/batch_single_folder\visualizations\G007.294-02.044_wise_rgb.png
2025-05-20 10:02:32,036 - main_single_folder - INFO - 成功保存WISE RGB图像到: results/batch_single_folder\visualizations\G007.294-02.044_wise_rgb.png
2025-05-20 10:02:32,037 - main_single_folder - WARNING - 没有足够的数据绘制消光-距离散点图
2025-05-20 10:02:32,053 - main_single_folder - INFO - 成功保存处理报告到: results/batch_single_folder\reports\G007.294-02.044_report.txt
2025-05-20 10:02:32,054 - main_single_folder - INFO - 处理G007.294-02.044完成
