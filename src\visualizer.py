"""
可视化模块，用于生成各种图表和图像
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LogNorm, Normalize
from matplotlib.patches import Patch
from astropy.wcs import WCS
from astropy.coordinates import SkyCoord
import astropy.units as u
from astropy.io import fits
from astropy.visualization import simple_norm, ZScaleInterval, MinMaxInterval, AsinhStretch, ImageNormalize
from skimage import exposure

from src.utils.logger import setup_logger
from src.utils.helpers import load_config, ensure_directory

# 设置日志记录器
logger = setup_logger(name='visualizer')

def plot_wise_with_regions_and_stars(wise_image, wise_header, masks, star_table,
                                    output_path, title=None, show_stars=True, r_eff=None, center_coord=None):
    """
    绘制WISE图像，叠加区域掩模和恒星位置

    Args:
        wise_image: WISE图像数据
        wise_header: FITS头信息
        masks: 区域掩模字典，包含'cavity', 'pdr', 'external'
        star_table: 恒星表，包含'ra', 'dec'列
        output_path: 输出文件路径
        title: 图像标题
        show_stars: 是否显示恒星
        r_eff: HII区的有效半径（度）
        center_coord: 中心坐标（SkyCoord对象）
    """
    logger.info(f"绘制WISE图像和区域掩模，输出到: {output_path}")

    try:
        # 创建图像
        plt.figure(figsize=(10, 8))

        # 创建WCS对象
        wcs = WCS(wise_header)

        # 获取WCS坐标系统
        wcs_frame = 'icrs'  # 默认使用ICRS
        if hasattr(wcs, 'wcs') and hasattr(wcs.wcs, 'radesys'):
            if wcs.wcs.radesys.lower() in ['fk4', 'fk5', 'galactic', 'icrs']:
                wcs_frame = wcs.wcs.radesys.lower()
                logger.info(f"使用WCS坐标系统: {wcs_frame}")

        # 设置坐标系统
        ax = plt.subplot(projection=wcs)

        # 显示WISE图像
        norm = simple_norm(wise_image, 'log', percent=99.5)
        im = ax.imshow(wise_image, origin='lower', cmap='inferno', norm=norm)

        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax, label='Flux')

        # 绘制区域轮廓
        colors = {'cavity': 'blue', 'pdr': 'green', 'external': 'red'}
        labels = {'cavity': 'Cavity', 'pdr': 'PDR', 'external': 'External'}

        legend_elements = []

        # 绘制HII区的有效半径（虚线圆）
        if r_eff is not None and center_coord is not None:
            # 确保中心坐标与WCS坐标系统一致
            if hasattr(wcs, 'wcs') and hasattr(wcs.wcs, 'radesys'):
                wcs_frame = wcs.wcs.radesys.lower()
                if wcs_frame != 'icrs' and wcs_frame in ['fk4', 'fk5', 'galactic']:
                    logger.info(f"将中心坐标从ICRS转换为{wcs_frame}以绘制HII区域圆")
                    if wcs_frame == 'fk5':
                        center_coord_wcs = center_coord.transform_to('fk5')
                    elif wcs_frame == 'fk4':
                        center_coord_wcs = center_coord.transform_to('fk4')
                    elif wcs_frame == 'galactic':
                        center_coord_wcs = center_coord.transform_to('galactic')
                else:
                    center_coord_wcs = center_coord
            else:
                center_coord_wcs = center_coord

            # 使用天球坐标直接绘制圆（更可靠的方法）
            try:
                from matplotlib import patheffects

                # 创建一个圆形的天球坐标路径
                theta = np.linspace(0, 2*np.pi, 100)

                # 根据坐标系统选择正确的方法计算圆周坐标
                if hasattr(wcs, 'wcs') and hasattr(wcs.wcs, 'radesys') and wcs.wcs.radesys.lower() == 'galactic':
                    # 对于银道坐标系，使用不同的方法
                    l0, b0 = center_coord_wcs.l.degree, center_coord_wcs.b.degree
                    r_deg = r_eff  # 半径（度）

                    # 计算圆周上的点
                    l = l0 + r_deg * np.cos(theta) / np.cos(np.radians(b0))
                    b = b0 + r_deg * np.sin(theta)

                    # 创建SkyCoord对象
                    circle_coords = SkyCoord(l=l*u.degree, b=b*u.degree, frame='galactic')
                else:
                    # 对于赤道坐标系（ICRS, FK5, FK4）
                    ra0, dec0 = center_coord_wcs.ra.degree, center_coord_wcs.dec.degree
                    r_deg = r_eff  # 半径（度）

                    # 计算圆周上的点
                    ra = ra0 + r_deg * np.cos(theta) / np.cos(np.radians(dec0))
                    dec = dec0 + r_deg * np.sin(theta)

                    # 创建SkyCoord对象
                    if hasattr(wcs, 'wcs') and hasattr(wcs.wcs, 'radesys'):
                        frame = wcs.wcs.radesys.lower()
                        if frame in ['fk4', 'fk5', 'icrs', 'galactic']:
                            circle_coords = SkyCoord(ra=ra*u.degree, dec=dec*u.degree, frame=frame)
                        else:
                            circle_coords = SkyCoord(ra=ra*u.degree, dec=dec*u.degree, frame='icrs')
                    else:
                        circle_coords = SkyCoord(ra=ra*u.degree, dec=dec*u.degree, frame='icrs')

                # 转换为像素坐标
                x, y = wcs.world_to_pixel(circle_coords)

                # 绘制路径 - 使用更明显的样式
                circle = ax.plot(x, y, '--', color='yellow', linewidth=2.5,
                               path_effects=[patheffects.withStroke(linewidth=4, foreground='black')])

                legend_elements.append(Patch(facecolor='none', edgecolor='yellow',
                                          linestyle='--', label='HII Region Radius', linewidth=2.5))

                logger.info("成功绘制HII区域圆")
            except Exception as e:
                logger.error(f"绘制HII区域圆失败: {str(e)}")

                # 尝试使用简单的像素坐标方法
                try:
                    # 将中心坐标转换为像素坐标
                    center_x, center_y = wcs.world_to_pixel(center_coord_wcs)

                    # 计算有效半径（像素）
                    test_coord = SkyCoord(ra=center_coord_wcs.ra.degree+1/3600,
                                        dec=center_coord_wcs.dec.degree, unit='deg',
                                        frame=center_coord_wcs.frame.name)
                    test_x, test_y = wcs.world_to_pixel(test_coord)
                    pixel_scale = np.sqrt((test_x - center_x)**2 + (test_y - center_y)**2)  # 像素/角秒
                    r_eff_pixels = r_eff * 3600 / pixel_scale  # 转换为像素

                    logger.info(f"使用备用方法绘制HII区域圆: 中心=({center_x:.1f}, {center_y:.1f}), 半径={r_eff_pixels:.1f}像素")

                    # 绘制虚线圆
                    from matplotlib.patches import Circle
                    circle = Circle((center_x, center_y), r_eff_pixels,
                                  fill=False, linestyle='--', color='white', linewidth=2)
                    ax.add_patch(circle)
                    legend_elements.append(Patch(facecolor='none', edgecolor='white',
                                              linestyle='--', label='HII Region Radius'))
                except Exception as e2:
                    logger.error(f"备用方法绘制HII区域圆也失败: {str(e2)}")

        # 绘制区域轮廓
        for region_name, mask in masks.items():
            if np.any(mask):
                ax.contour(mask, levels=[0.5], colors=[colors[region_name]], linewidths=2)
                legend_elements.append(Patch(facecolor=colors[region_name],
                                           edgecolor=colors[region_name],
                                           label=labels[region_name]))

        # 绘制恒星位置
        if show_stars and len(star_table) > 0:
            # 检查是否有区域列
            if 'region' in star_table.colnames:
                # 按区域绘制恒星
                for region_name in ['cavity', 'pdr', 'external']:
                    region_mask = star_table['region'] == region_name
                    if np.sum(region_mask) > 0:
                        region_stars = star_table[region_mask]
                        star_coords = SkyCoord(ra=region_stars['ra']*u.degree,
                                             dec=region_stars['dec']*u.degree)
                        star_x, star_y = wcs.world_to_pixel(star_coords)
                        ax.scatter(star_x, star_y, s=10, color=colors[region_name],
                                 alpha=0.7, marker='o', label=f"{labels[region_name]} Stars")
            else:
                # 所有恒星使用相同颜色
                star_coords = SkyCoord(ra=star_table['ra']*u.degree,
                                     dec=star_table['dec']*u.degree)
                star_x, star_y = wcs.world_to_pixel(star_coords)
                ax.scatter(star_x, star_y, s=10, color='cyan',
                         alpha=0.7, marker='o', label='Stars')

        # 设置坐标轴标签和格式
        ax.set_xlabel('RA (deg)', fontsize=12, fontname='Arial')
        ax.set_ylabel('Dec (deg)', fontsize=12, fontname='Arial')

        # 设置坐标格式为度
        try:
            # 设置坐标格式为度
            ax.coords[0].set_format_unit(u.degree)
            ax.coords[1].set_format_unit(u.degree)

            # 设置刻度标签格式
            ax.coords[0].set_major_formatter('d.dd')
            ax.coords[1].set_major_formatter('d.dd')

            # 设置刻度标签大小和颜色
            ax.coords[0].set_ticklabel(size=10, color='black')
            ax.coords[1].set_ticklabel(size=10, color='black')

            # 设置刻度标签间隔
            ax.coords[0].set_ticks(spacing=0.05 * u.degree)
            ax.coords[1].set_ticks(spacing=0.05 * u.degree)

            # 设置坐标网格
            ax.coords.grid(color='white', alpha=0.5, linestyle='dotted')

            # 设置坐标轴的颜色
            ax.spines['bottom'].set_color('white')
            ax.spines['top'].set_color('white')
            ax.spines['right'].set_color('white')
            ax.spines['left'].set_color('white')
        except Exception as e:
            logger.warning(f"设置坐标格式失败: {str(e)}")
            # 使用基本的网格
            ax.grid(color='white', ls='dotted', alpha=0.5)

        # 设置标题
        if title:
            ax.set_title(title)

        # 添加图例
        if legend_elements:
            ax.legend(handles=legend_elements, loc='upper right')

        # 保存图像
        ensure_directory(os.path.dirname(output_path))
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"成功保存图像到: {output_path}")

    except Exception as e:
        logger.error(f"绘制WISE图像失败: {str(e)}")
        raise

def plot_wise_rgb(wise_data, wcs, output_path, title=None, r_eff=None, center_coord=None,
                masks=None, star_table=None, show_stars=True):
    """
    使用WISE三波段数据创建RGB彩色图像

    Args:
        wise_data: 包含三个波段数据的字典，键为'w1'(3.4μm)、'w3'(12μm)、'w4'(22μm)
        wcs: 世界坐标系对象
        output_path: 输出文件路径
        title: 图像标题
        r_eff: HII区的有效半径（度）
        center_coord: 中心坐标（SkyCoord对象）
        masks: 区域掩模字典，包含'cavity', 'pdr', 'external'
        star_table: 恒星表，包含'ra', 'dec'列
        show_stars: 是否显示恒星
    """
    logger.info(f"创建WISE三波段RGB图像，输出到: {output_path}")

    try:
        # 创建图像，设置白色背景
        plt.figure(figsize=(10, 8), facecolor='white')

        # 创建WCS对象
        ax = plt.subplot(projection=wcs)
        # 图像区域保持黑色背景，但坐标轴区域为白色

        # 获取WCS坐标系统
        wcs_frame = 'icrs'  # 默认使用ICRS
        if hasattr(wcs, 'wcs') and hasattr(wcs.wcs, 'radesys'):
            if wcs.wcs.radesys.lower() in ['fk4', 'fk5', 'galactic', 'icrs']:
                wcs_frame = wcs.wcs.radesys.lower()
                logger.info(f"使用WCS坐标系统: {wcs_frame}")

        # 确定共同的图像尺寸（使用12μm波段的尺寸作为标准）
        all_shapes = {}
        for band, band_key in [('w1', '3.4'), ('w3', '12'), ('w4', '22')]:
            if band in wise_data and wise_data[band] is not None:
                all_shapes[band] = wise_data[band].shape
                logger.info(f"波段 {band_key}μm 的尺寸: {wise_data[band].shape}")

        if not all_shapes:
            raise ValueError("No valid band data found to determine image size")

        # 使用12μm波段(w3)的尺寸作为目标尺寸
        if 'w3' in all_shapes:
            target_shape = all_shapes['w3']
            logger.info(f"Using 12μm band size as target: {target_shape}")
        else:
            # 如果没有12μm波段，则使用可用波段中最大的尺寸
            max_pixels = 0
            max_band = None
            for band, shape in all_shapes.items():
                pixels = shape[0] * shape[1]
                if pixels > max_pixels:
                    max_pixels = pixels
                    max_band = band
                    target_shape = shape

            logger.info(f"12μm band not available, using {max_band} band size: {target_shape}")

        logger.info(f"Using target image size: {target_shape}, original sizes: {all_shapes}")

        # 预处理每个波段的数据
        rgb_data = np.zeros((target_shape[0], target_shape[1], 3), dtype=np.float32)

        # 尝试使用astropy的make_lupton_rgb函数
        try:
            from astropy.visualization import make_lupton_rgb

            # 准备三个波段的数据
            r_data = None  # 22μm (w4)
            g_data = None  # 12μm (w3)
            b_data = None  # 3.4μm (w1)

            # 处理红色通道 (22μm, w4)
            if 'w4' in wise_data and wise_data['w4'] is not None:
                r_data = wise_data['w4'].copy()
                # 替换无效值
                r_data[~np.isfinite(r_data)] = 0
                # 调整尺寸以匹配目标尺寸
                if r_data.shape != target_shape:
                    from skimage.transform import resize
                    r_data = resize(r_data, target_shape, mode='reflect', anti_aliasing=True, order=3)
                    logger.info(f"调整w4波段数据从{wise_data['w4'].shape}到{target_shape}")

                # 记录数据统计
                if np.any(r_data > 0):
                    logger.info(f"红色通道(22μm)数据范围: {np.min(r_data[r_data > 0])}-{np.max(r_data)}")
                else:
                    logger.warning("红色通道(22μm)没有有效数据")
                    r_data = np.zeros(target_shape)
            else:
                logger.warning("缺少w4波段数据，使用零数组")
                r_data = np.zeros(target_shape)

            # 处理绿色通道 (12μm, w3)
            if 'w3' in wise_data and wise_data['w3'] is not None:
                g_data = wise_data['w3'].copy()
                # 替换无效值
                g_data[~np.isfinite(g_data)] = 0
                # 调整尺寸以匹配目标尺寸
                if g_data.shape != target_shape:
                    from skimage.transform import resize
                    g_data = resize(g_data, target_shape, mode='reflect', anti_aliasing=True, order=3)
                    logger.info(f"调整w3波段数据从{wise_data['w3'].shape}到{target_shape}")

                # 记录数据统计
                if np.any(g_data > 0):
                    logger.info(f"绿色通道(12μm)数据范围: {np.min(g_data[g_data > 0])}-{np.max(g_data)}")
                else:
                    logger.warning("绿色通道(12μm)没有有效数据")
                    g_data = np.zeros(target_shape)
            else:
                logger.warning("缺少w3波段数据，使用零数组")
                g_data = np.zeros(target_shape)

            # 处理蓝色通道 (3.4μm, w1)
            if 'w1' in wise_data and wise_data['w1'] is not None:
                b_data = wise_data['w1'].copy()
                # 替换无效值
                b_data[~np.isfinite(b_data)] = 0
                # 调整尺寸以匹配目标尺寸
                if b_data.shape != target_shape:
                    from skimage.transform import resize
                    b_data = resize(b_data, target_shape, mode='reflect', anti_aliasing=True, order=3)
                    logger.info(f"调整w1波段数据从{wise_data['w1'].shape}到{target_shape}")

                # 记录数据统计
                if np.any(b_data > 0):
                    logger.info(f"蓝色通道(3.4μm)数据范围: {np.min(b_data[b_data > 0])}-{np.max(b_data)}")
                else:
                    logger.warning("蓝色通道(3.4μm)没有有效数据")
                    b_data = np.zeros(target_shape)
            else:
                logger.warning("缺少w1波段数据，使用零数组")
                b_data = np.zeros(target_shape)

            # 检查是否有足够的数据创建RGB图像
            if np.all(r_data == 0) and np.all(g_data == 0) and np.all(b_data == 0):
                logger.warning("所有波段数据均为零，无法创建有意义的RGB图像")
                # 创建一个全黑图像
                rgb_data = np.zeros((target_shape[0], target_shape[1], 3))
            else:
                # 使用Lupton方法创建RGB图像，按照Astropy文档中的示例
                # 计算每个波段的99.5%百分位数，用于设置stretch参数
                if np.any(r_data > 0):
                    r_pct = np.percentile(r_data[r_data > 0], 99.5)
                else:
                    r_pct = 1

                if np.any(g_data > 0):
                    g_pct = np.percentile(g_data[g_data > 0], 99.5)
                else:
                    g_pct = 1

                if np.any(b_data > 0):
                    b_pct = np.percentile(b_data[b_data > 0], 99.5)
                else:
                    b_pct = 1

                # 计算合适的stretch参数 - 使用最大值的一半作为stretch
                max_pct = max(r_pct, g_pct, b_pct)
                stretch = 0.5 * max_pct

                # 使用Q=10，这是Lupton et al. (2004)论文中推荐的值
                Q = 10

                logger.info(f"使用Lupton RGB方法，stretch={stretch:.2f}, Q={Q}")
                logger.info(f"各波段99.5%百分位数: R={r_pct:.2f}, G={g_pct:.2f}, B={b_pct:.2f}")

                # 使用make_lupton_rgb函数创建RGB图像
                rgb_data = make_lupton_rgb(r_data, g_data, b_data, stretch=stretch, Q=Q)

                # 确保RGB数据是浮点型并在[0,1]范围内
                if rgb_data.dtype != np.float32 and rgb_data.dtype != np.float64:
                    rgb_data = rgb_data.astype(np.float32) / 255.0

                logger.info(f"RGB数据形状: {rgb_data.shape}, 类型: {rgb_data.dtype}")

        except Exception as e:
            logger.warning(f"使用Lupton RGB方法失败: {str(e)}，回退到手动RGB合成")

            # 手动处理每个波段
            for i, (band, color) in enumerate([('w4', 0), ('w3', 1), ('w1', 2)]):  # R=22μm, G=12μm, B=3.4μm
                if band in wise_data and wise_data[band] is not None:
                    # 复制数据以避免修改原始数据
                    original_data = wise_data[band].copy()

                    # 记录波段信息
                    if np.any(np.isfinite(original_data)):
                        valid_orig = original_data[np.isfinite(original_data)]
                        if len(valid_orig) > 0:
                            logger.info(f"处理{band}波段数据，对应RGB通道{color}，原始数据范围：{np.min(valid_orig)}-{np.max(valid_orig)}")
                        else:
                            logger.warning(f"{band}波段数据没有有效值")
                    else:
                        logger.warning(f"{band}波段数据全部为NaN或无穷大")

                    # 调整尺寸以匹配目标尺寸
                    if original_data.shape != target_shape:
                        from skimage.transform import resize
                        # 使用双三次插值以获得更好的质量
                        band_data = resize(original_data, target_shape,
                                          mode='reflect', anti_aliasing=True,
                                          order=3)  # order=3表示双三次插值
                        logger.info(f"调整{band}波段数据从{original_data.shape}到{target_shape}")
                    else:
                        band_data = original_data

                    # 替换无效值
                    band_data[~np.isfinite(band_data)] = 0

                    # 确保数据有效
                    if not np.any(band_data > 0):
                        logger.warning(f"{band}波段数据全为零或负值，将不会显示在RGB图像中")
                        continue

                    # 去除极值
                    valid_data = band_data[band_data > 0]
                    data_mean = np.mean(valid_data)
                    data_std = np.std(valid_data)

                    # 设置上限为均值+5*标准差，更宽松以保留更多细节
                    upper_limit = data_mean + 5 * data_std
                    band_data[band_data > upper_limit] = upper_limit

                    # 对数缩放以增强暗部细节
                    band_data = np.log1p(band_data)

                    # 归一化到[0,1]范围
                    min_val = np.min(band_data)
                    max_val = np.max(band_data)
                    if max_val > min_val:
                        band_data = (band_data - min_val) / (max_val - min_val)

                        # 调整对比度，使用更宽松的百分位数以保留更多细节
                        p2, p98 = np.percentile(band_data[band_data > 0], (1, 99))
                        band_data = exposure.rescale_intensity(band_data, in_range=(p2, p98))

                        # 伽马校正，调整以增强每个通道的可见性
                        gamma = 0.7 if band == 'w4' else (0.8 if band == 'w3' else 0.9)
                        band_data = np.power(band_data, gamma)

                        logger.info(f"{band}波段处理后数据范围：{np.min(band_data)}-{np.max(band_data)}")
                    else:
                        logger.warning(f"{band}波段数据处理后无有效范围")
                        band_data.fill(0)

                    # 保存到RGB数组
                    rgb_data[:, :, color] = band_data

        # 确保RGB数据有效
        if not np.any(rgb_data > 0):
            logger.warning("所有波段处理后数据均为零，RGB图像将全黑")

        # 记录RGB数据统计信息
        for c, name in enumerate(['Red', 'Green', 'Blue']):
            channel = rgb_data[:,:,c]
            if np.any(channel > 0):
                logger.info(f"{name}通道数据范围：{np.min(channel)}-{np.max(channel)}，均值：{np.mean(channel):.4f}")

        # 显示RGB图像 - 确保正确显示所有通道
        # 检查RGB数据是否有效
        if np.all(rgb_data == 0):
            logger.warning("RGB数据全为零，可能是波段处理有问题")

        # 确保RGB数据在[0,1]范围内
        rgb_data = np.clip(rgb_data, 0, 1)

        # 增强对比度以确保所有通道可见
        for channel in range(3):
            if np.any(rgb_data[:,:,channel] > 0):
                # 确保每个通道都有足够的对比度
                channel_data = rgb_data[:,:,channel]
                p2, p98 = np.percentile(channel_data[channel_data > 0], (2, 98))
                rgb_data[:,:,channel] = exposure.rescale_intensity(channel_data, in_range=(p2, p98))

        # 显示RGB图像
        ax.imshow(rgb_data, origin='lower')

        # 绘制HII区的有效半径（虚线圆）
        if r_eff is not None and center_coord is not None:
            # 确保中心坐标与WCS坐标系统一致
            if hasattr(wcs, 'wcs') and hasattr(wcs.wcs, 'radesys'):
                wcs_frame = wcs.wcs.radesys.lower()
                if wcs_frame != 'icrs' and wcs_frame in ['fk4', 'fk5', 'galactic']:
                    logger.info(f"将中心坐标从ICRS转换为{wcs_frame}以绘制HII区域圆")
                    if wcs_frame == 'fk5':
                        center_coord_wcs = center_coord.transform_to('fk5')
                    elif wcs_frame == 'fk4':
                        center_coord_wcs = center_coord.transform_to('fk4')
                    elif wcs_frame == 'galactic':
                        center_coord_wcs = center_coord.transform_to('galactic')
                else:
                    center_coord_wcs = center_coord
            else:
                center_coord_wcs = center_coord

            # 使用天球坐标直接绘制圆（更可靠的方法）
            try:
                from matplotlib import patheffects

                # 创建一个圆形的天球坐标路径
                theta = np.linspace(0, 2*np.pi, 100)

                # 根据坐标系统选择正确的方法计算圆周坐标
                if hasattr(wcs, 'wcs') and hasattr(wcs.wcs, 'radesys') and wcs.wcs.radesys.lower() == 'galactic':
                    # 对于银道坐标系，使用不同的方法
                    l0, b0 = center_coord_wcs.l.degree, center_coord_wcs.b.degree
                    r_deg = r_eff  # 半径（度）

                    # 计算圆周上的点
                    l = l0 + r_deg * np.cos(theta) / np.cos(np.radians(b0))
                    b = b0 + r_deg * np.sin(theta)

                    # 创建SkyCoord对象
                    circle_coords = SkyCoord(l=l*u.degree, b=b*u.degree, frame='galactic')
                else:
                    # 对于赤道坐标系（ICRS, FK5, FK4）
                    ra0, dec0 = center_coord_wcs.ra.degree, center_coord_wcs.dec.degree
                    r_deg = r_eff  # 半径（度）

                    # 计算圆周上的点
                    ra = ra0 + r_deg * np.cos(theta) / np.cos(np.radians(dec0))
                    dec = dec0 + r_deg * np.sin(theta)

                    # 创建SkyCoord对象
                    if hasattr(wcs, 'wcs') and hasattr(wcs.wcs, 'radesys'):
                        frame = wcs.wcs.radesys.lower()
                        if frame in ['fk4', 'fk5', 'icrs', 'galactic']:
                            circle_coords = SkyCoord(ra=ra*u.degree, dec=dec*u.degree, frame=frame)
                        else:
                            circle_coords = SkyCoord(ra=ra*u.degree, dec=dec*u.degree, frame='icrs')
                    else:
                        circle_coords = SkyCoord(ra=ra*u.degree, dec=dec*u.degree, frame='icrs')

                # 转换为像素坐标
                x, y = wcs.world_to_pixel(circle_coords)

                # 绘制路径 - 使用更明显的样式
                circle = ax.plot(x, y, '--', color='yellow', linewidth=2.5,
                               path_effects=[patheffects.withStroke(linewidth=4, foreground='black')])

                logger.info("成功绘制HII区域圆")
            except Exception as e:
                logger.error(f"绘制HII区域圆失败: {str(e)}")

        # 绘制区域轮廓
        legend_elements = []
        if masks is not None:
            colors = {'cavity': 'blue', 'pdr': 'lime', 'external': 'cyan'}
            labels = {'cavity': 'Cavity', 'pdr': 'PDR Region', 'external': 'External Region'}

            for region_name, mask in masks.items():
                if np.any(mask):
                    ax.contour(mask, levels=[0.5], colors=[colors[region_name]], linewidths=2)
                    legend_elements.append(Patch(facecolor='none', edgecolor=colors[region_name],
                                               label=labels[region_name]))

        # 不显示Gaia星
        logger.info("按照要求不显示Gaia星")

        # 设置坐标轴标签和格式 - 使用黑色文本在白色背景上
        ax.set_xlabel('RA (deg)', fontsize=12, fontname='Arial', color='black')
        ax.set_ylabel('Dec (deg)', fontsize=12, fontname='Arial', color='black')

        # 设置刻度标签颜色为黑色
        ax.tick_params(axis='both', colors='black')

        # 确保坐标轴可见
        plt.rcParams['axes.linewidth'] = 1.5  # 增加坐标轴线宽
        ax.spines['top'].set_visible(True)
        ax.spines['right'].set_visible(True)
        ax.spines['bottom'].set_visible(True)
        ax.spines['left'].set_visible(True)
        ax.spines['top'].set_color('black')
        ax.spines['right'].set_color('black')
        ax.spines['bottom'].set_color('black')
        ax.spines['left'].set_color('black')

        # 设置坐标格式为度
        try:
            # 设置坐标格式为度
            ax.coords[0].set_format_unit(u.degree)
            ax.coords[1].set_format_unit(u.degree)

            # 设置刻度标签格式
            ax.coords[0].set_major_formatter('d.dd')
            ax.coords[1].set_major_formatter('d.dd')

            # 设置刻度标签大小和颜色
            ax.coords[0].set_ticklabel(size=10, color='black')
            ax.coords[1].set_ticklabel(size=10, color='black')

            # 减少刻度标签数量，只显示3-5个值
            # 计算合适的刻度间隔
            ra_range = np.ptp(ax.get_xlim())
            dec_range = np.ptp(ax.get_ylim())

            # 目标是显示更多刻度
            ra_spacing = ra_range / 8
            dec_spacing = dec_range / 8

            # 将间隔四舍五入到合理的值
            def round_to_nice(value):
                # 找到最接近的"漂亮"值：0.05, 0.1, 0.2, 0.5, 1.0等
                magnitude = 10 ** np.floor(np.log10(value))
                normalized = value / magnitude

                if normalized < 0.15:
                    return 0.1 * magnitude
                elif normalized < 0.3:
                    return 0.2 * magnitude
                elif normalized < 0.7:
                    return 0.5 * magnitude
                else:
                    return 1.0 * magnitude

            ra_spacing = round_to_nice(ra_spacing)
            dec_spacing = round_to_nice(dec_spacing)

            logger.info(f"设置坐标刻度间隔: RA={ra_spacing:.3f}度, Dec={dec_spacing:.3f}度")

            # 设置刻度标签间隔
            ax.coords[0].set_ticks(spacing=ra_spacing * u.degree)
            ax.coords[1].set_ticks(spacing=dec_spacing * u.degree)

            # 确保坐标轴可见
            ax.coords[0].set_axislabel('RA (deg)', fontsize=12, color='black')
            ax.coords[1].set_axislabel('Dec (deg)', fontsize=12, color='black')

            # 确保坐标轴刻度可见
            ax.coords[0].set_ticks_visible(True)
            ax.coords[1].set_ticks_visible(True)
            ax.coords[0].set_ticklabel_visible(True)
            ax.coords[1].set_ticklabel_visible(True)

            # 设置坐标网格
            ax.coords.grid(color='black', alpha=0.5, linestyle='dotted')

            # 确保坐标轴边框可见
            ax.coords.frame.set_color('black')
            ax.coords.frame.set_linewidth(1.5)
        except Exception as e:
            logger.warning(f"设置坐标格式失败: {str(e)}")
            # 使用基本的网格
            ax.grid(color='black', ls='dotted', alpha=0.3)

        # 添加波段信息到图内
        ax.text(0.02, 0.05, "Red: WISE 22μm\nGreen: WISE 12μm\nBlue: WISE 3.4μm",
               transform=ax.transAxes, fontsize=10, color='white',
               bbox=dict(facecolor='black', alpha=0.7, boxstyle='round,pad=0.5'))

        # 设置标题 - 只保留源名
        if title:
            # 从标题中提取源名（假设格式为"源名 - 其他内容"）
            if " - " in title:
                source_name = title.split(" - ")[0].strip()
                ax.set_title(source_name, color='black', fontsize=14, pad=10)
            else:
                ax.set_title(title, color='black', fontsize=14, pad=10)

        # 添加图例 - 使用白色背景和黑色文本
        if legend_elements:
            legend = ax.legend(handles=legend_elements, loc='upper right',
                             framealpha=0.8, facecolor='white', edgecolor='black')
            # 设置图例文本颜色为黑色
            for text in legend.get_texts():
                text.set_color('black')

        # 保存图像
        ensure_directory(os.path.dirname(output_path))
        plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        logger.info(f"成功保存RGB图像到: {output_path}")

    except Exception as e:
        logger.error(f"创建WISE三波段RGB图像失败: {str(e)}")
        raise

def plot_extinction_distance(star_table, output_path, title=None, bin_size=0.2,
                           min_stars_per_bin=5):
    """
    绘制消光-距离散点图，按区域分组

    Args:
        star_table: 恒星表，包含'distance', 'a_v', 'region'列
        output_path: 输出文件路径
        title: 图像标题
        bin_size: 距离bin的大小（kpc）
        min_stars_per_bin: 每个bin的最小恒星数
    """
    logger.info(f"绘制消光-距离散点图，输出到: {output_path}")

    try:
        # 创建图像
        plt.figure(figsize=(12, 8))

        # 检查是否有必要的列
        required_cols = ['distance', 'a_v']
        if not all(col in star_table.colnames for col in required_cols):
            logger.error(f"表中缺少必要的列: {required_cols}")
            raise ValueError(f"表中缺少必要的列: {required_cols}")

        # 设置颜色和标签
        colors = {'cavity': 'blue', 'pdr': 'green', 'external': 'red', 'unknown': 'gray'}
        labels = {'cavity': 'Cavity', 'pdr': 'PDR', 'external': 'External', 'unknown': 'Unknown'}

        # 检查是否有区域列
        if 'region' in star_table.colnames:
            # 按区域绘制散点图
            for region_name in np.unique(star_table['region']):
                region_mask = star_table['region'] == region_name
                if np.sum(region_mask) > 0:
                    region_stars = star_table[region_mask]

                    # 筛选有效数据
                    valid_mask = ~np.isnan(region_stars['distance']) & ~np.isnan(region_stars['a_v']) & (region_stars['distance'] > 0)
                    if np.sum(valid_mask) > 0:
                        valid_stars = region_stars[valid_mask]

                        # 绘制散点图
                        plt.scatter(valid_stars['distance'], valid_stars['a_v'],
                                  s=20, color=colors.get(region_name, 'gray'),
                                  alpha=0.7, label=f"{labels.get(region_name, region_name)} Region ({len(valid_stars)} stars)")

                        # 计算并绘制中位数曲线
                        if len(valid_stars) >= min_stars_per_bin:
                            distances = np.array(valid_stars['distance'])
                            extinctions = np.array(valid_stars['a_v'])

                            # 排序数据
                            sort_idx = np.argsort(distances)
                            sorted_distances = distances[sort_idx]
                            sorted_extinctions = extinctions[sort_idx]

                            # 确定bin边界
                            min_dist = np.min(sorted_distances)
                            max_dist = np.max(sorted_distances)
                            bin_edges = np.arange(min_dist, max_dist + bin_size, bin_size)

                            # 计算每个bin的中位数
                            bin_centers = []
                            bin_medians = []
                            bin_counts = []

                            for i in range(len(bin_edges) - 1):
                                bin_mask = (sorted_distances >= bin_edges[i]) & (sorted_distances < bin_edges[i+1])
                                if np.sum(bin_mask) >= min_stars_per_bin:
                                    bin_centers.append((bin_edges[i] + bin_edges[i+1]) / 2)
                                    bin_medians.append(np.median(sorted_extinctions[bin_mask]))
                                    bin_counts.append(np.sum(bin_mask))

                            if bin_centers:
                                plt.plot(bin_centers, bin_medians, '-', color=colors.get(region_name, 'gray'),
                                       linewidth=2, label=f"{labels.get(region_name, region_name)} Median")
        else:
            # 所有恒星使用相同颜色
            valid_mask = ~np.isnan(star_table['distance']) & ~np.isnan(star_table['a_v']) & (star_table['distance'] > 0)
            if np.sum(valid_mask) > 0:
                valid_stars = star_table[valid_mask]

                # 绘制散点图
                plt.scatter(valid_stars['distance'], valid_stars['a_v'],
                          s=20, color='blue', alpha=0.7, label=f"All Stars ({len(valid_stars)})")

                # 计算并绘制中位数曲线
                if len(valid_stars) >= min_stars_per_bin:
                    distances = np.array(valid_stars['distance'])
                    extinctions = np.array(valid_stars['a_v'])

                    # 排序数据
                    sort_idx = np.argsort(distances)
                    sorted_distances = distances[sort_idx]
                    sorted_extinctions = extinctions[sort_idx]

                    # 确定bin边界
                    min_dist = np.min(sorted_distances)
                    max_dist = np.max(sorted_distances)
                    bin_edges = np.arange(min_dist, max_dist + bin_size, bin_size)

                    # 计算每个bin的中位数
                    bin_centers = []
                    bin_medians = []
                    bin_counts = []

                    for i in range(len(bin_edges) - 1):
                        bin_mask = (sorted_distances >= bin_edges[i]) & (sorted_distances < bin_edges[i+1])
                        if np.sum(bin_mask) >= min_stars_per_bin:
                            bin_centers.append((bin_edges[i] + bin_edges[i+1]) / 2)
                            bin_medians.append(np.median(sorted_extinctions[bin_mask]))
                            bin_counts.append(np.sum(bin_mask))

                    if bin_centers:
                        plt.plot(bin_centers, bin_medians, '-', color='blue',
                               linewidth=2, label=f"Median")

        # 设置坐标轴标签
        plt.xlabel('Distance (kpc)', fontname='Arial')
        plt.ylabel('Extinction A_V (mag)', fontname='Arial')

        # 设置标题
        if title:
            # 使用英文标题，避免中文字体问题
            if '消光-距离关系' in title:
                title = title.replace('消光-距离关系', 'Extinction-Distance Relation')
            plt.title(title, fontname='Arial')

        # 添加图例
        plt.legend(loc='upper left')

        # 设置坐标轴范围
        plt.xlim(left=0)
        plt.ylim(bottom=0)

        # 添加网格
        plt.grid(True, linestyle='--', alpha=0.7)

        # 保存图像
        ensure_directory(os.path.dirname(output_path))
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"成功保存图像到: {output_path}")

    except Exception as e:
        logger.error(f"绘制消光-距离散点图失败: {str(e)}")
        raise
