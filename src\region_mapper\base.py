"""
区域映射基础模块

包含区域映射的基础函数和主要接口。
"""

import os
import numpy as np
from astropy.coordinates import SkyCoord
import astropy.units as u
from astropy.wcs import WCS
from scipy import ndimage as ndi

from src.utils.logger import setup_logger
from src.utils.helpers import load_config, ensure_directory

# 导入各种PDR检测方法
from src.region_mapper.threshold_method import define_pdr_mask_threshold
from src.region_mapper.contour_method import define_pdr_mask_contour
from src.region_mapper.ratio_method import define_pdr_mask_ratio

# 设置日志记录器
logger = setup_logger(name='region_mapper')

def process_wise_image(wise_data, wise_header=None, percentile=99.5):
    """
    处理WISE图像，应用对数缩放并归一化

    Args:
        wise_data: WISE图像数据
        wise_header: WISE图像头信息（可选）
        percentile: 用于归一化的百分位数，默认为99.5

    Returns:
        numpy.ndarray: 处理后的WISE图像
    """
    # 复制数据以避免修改原始数据
    processed_data = wise_data.copy()

    # 将负值和NaN设为0
    processed_data[np.isnan(processed_data)] = 0
    processed_data[processed_data < 0] = 0

    # 应用对数缩放
    processed_data = np.log1p(processed_data)

    # 归一化到[0, 1]
    # 确保percentile是浮点数
    percentile_float = float(percentile)

    # 只在有正值的情况下计算百分位数
    if np.any(processed_data > 0):
        max_val = np.percentile(processed_data[processed_data > 0], percentile_float)
        if max_val > 0:
            processed_data = processed_data / max_val
            processed_data[processed_data > 1] = 1

    return processed_data

def define_pdr_mask(processed_image, wcs, center_coord, r_eff,
                   threshold_factor=0.3, max_radius_factor=5.0, search_radius_factor=3.0,
                   use_contour_method=False, use_ratio_method=False, n_contours=10, contour_start_nsigma=3.0,
                   source_name=None, output_dir=None,
                   save_contour_plot=False, save_ratio_plot=False, config_path='config/default_config.yaml'):
    """
    定义PDR区域掩模

    Args:
        processed_image: 处理后的WISE图像
        wcs: 世界坐标系
        center_coord: 中心坐标 (SkyCoord对象)
        r_eff: 有效半径（度）
        threshold_factor: 阈值因子（相对于百分位数或峰值）
        max_radius_factor: 最大半径因子（相对于r_eff）
        search_radius_factor: PDR搜索半径因子（相对于r_eff），默认为3.0
        use_contour_method: 是否使用等高线方法，默认为False
        use_ratio_method: 是否使用W3/W4波段比值方法，默认为False
        n_contours: 等高线方法中生成的等高线数量，默认为10
        contour_start_nsigma: 等高线方法中最低等高线水平（相对于背景标准差的倍数），默认为3.0
        source_name: 源名称，用于保存等高线图像和比值图像
        output_dir: 输出目录，用于保存等高线图像和比值图像
        save_contour_plot: 是否保存等高线图像，默认为False
        save_ratio_plot: 是否保存比值图像，默认为False
        config_path: 配置文件路径，默认为'config/default_config.yaml'

    Returns:
        numpy.ndarray: PDR掩模（布尔数组）
    """

    # 如果使用W3/W4波段比值方法
    if use_ratio_method:
        logger.info("使用W3/W4波段比值方法定义PDR掩模")
        return define_pdr_mask_ratio(
            source_name, wcs, center_coord, r_eff,
            search_radius_factor, smooth_sigma=1.0,
            config_path=config_path,
            output_dir=output_dir, save_ratio_plot=save_ratio_plot
        )
    # 如果使用等高线方法
    elif use_contour_method:
        logger.info("使用等高线方法定义PDR掩模")
        return define_pdr_mask_contour(
            processed_image, wcs, center_coord, r_eff,
            search_radius_factor, n_contours, contour_start_nsigma,
            source_name, output_dir, save_contour_plot
        )
    # 默认使用阈值方法
    else:
        logger.info("使用阈值方法定义PDR掩模")
        return define_pdr_mask_threshold(
            processed_image, wcs, center_coord, r_eff,
            threshold_factor, max_radius_factor, search_radius_factor
        )

def define_cavity_mask(pdr_mask, wcs, center_coord, r_eff):
    """
    定义空腔区域掩模

    Args:
        pdr_mask: PDR掩模
        wcs: 世界坐标系
        center_coord: 中心坐标 (SkyCoord对象)
        r_eff: 有效半径（度）

    Returns:
        numpy.ndarray: 空腔掩模（布尔数组）
    """
    # 获取中心坐标在图像中的像素位置
    center_x, center_y = wcs.world_to_pixel(center_coord)

    # 计算像素尺度
    test_offset = 1.0 / 3600.0  # 1角秒

    # 根据坐标系统类型创建测试坐标
    if hasattr(center_coord, 'ra'):
        # 赤道坐标系
        test_coord = SkyCoord(ra=center_coord.ra + test_offset * u.degree,
                             dec=center_coord.dec,
                             frame=center_coord.frame)
    elif hasattr(center_coord, 'l'):
        # 银道坐标系
        test_coord = SkyCoord(l=center_coord.l + test_offset * u.degree,
                             b=center_coord.b,
                             frame=center_coord.frame)
    else:
        # 未知坐标系
        raise ValueError("未知的坐标系统类型")

    test_x, test_y = wcs.world_to_pixel(test_coord)
    pixel_scale = np.sqrt((test_x - center_x)**2 + (test_y - center_y)**2) * 3600  # 像素/角秒

    # 计算有效半径（像素）
    r_eff_pixels = r_eff * 3600 / pixel_scale

    # 创建距离图像
    y, x = np.ogrid[:pdr_mask.shape[0], :pdr_mask.shape[1]]
    distance_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)

    # 定义空腔区域：在有效半径内且不在PDR区域内
    cavity_mask = (distance_from_center <= r_eff_pixels) & ~pdr_mask

    return cavity_mask

def define_external_mask(pdr_mask, cavity_mask, wcs, center_coord, r_eff, max_radius_factor=5.0):
    """
    定义外部区域掩模

    Args:
        pdr_mask: PDR掩模
        cavity_mask: 空腔掩模
        wcs: 世界坐标系
        center_coord: 中心坐标 (SkyCoord对象)
        r_eff: 有效半径（度）
        max_radius_factor: 最大半径因子（相对于r_eff），默认为5.0

    Returns:
        numpy.ndarray: 外部掩模（布尔数组）
    """
    # 获取中心坐标在图像中的像素位置
    center_x, center_y = wcs.world_to_pixel(center_coord)

    # 计算像素尺度
    test_offset = 1.0 / 3600.0  # 1角秒

    # 根据坐标系统类型创建测试坐标
    if hasattr(center_coord, 'ra'):
        # 赤道坐标系
        test_coord = SkyCoord(ra=center_coord.ra + test_offset * u.degree,
                             dec=center_coord.dec,
                             frame=center_coord.frame)
    elif hasattr(center_coord, 'l'):
        # 银道坐标系
        test_coord = SkyCoord(l=center_coord.l + test_offset * u.degree,
                             b=center_coord.b,
                             frame=center_coord.frame)
    else:
        # 未知坐标系
        raise ValueError("未知的坐标系统类型")

    test_x, test_y = wcs.world_to_pixel(test_coord)
    pixel_scale = np.sqrt((test_x - center_x)**2 + (test_y - center_y)**2) * 3600  # 像素/角秒

    # 计算最大半径（像素）
    max_radius_pixels = r_eff * max_radius_factor * 3600 / pixel_scale

    # 创建距离图像
    y, x = np.ogrid[:pdr_mask.shape[0], :pdr_mask.shape[1]]
    distance_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)

    # 定义外部区域：在最大半径内且不在PDR区域和空腔区域内
    external_mask = (distance_from_center <= max_radius_pixels) & ~pdr_mask & ~cavity_mask

    return external_mask

def get_region_masks(processed_image, wcs, center_coord, r_eff,
                    threshold_factor=0.3, max_radius_factor=5.0, search_radius_factor=3.0,
                    use_contour_method=False, use_ratio_method=False, n_contours=10, contour_start_nsigma=3.0,
                    source_name=None, output_dir=None, save_contour_plot=False, save_ratio_plot=False,
                    config_path='config/default_config.yaml'):
    """
    获取区域掩模

    Args:
        processed_image: 处理后的WISE图像
        wcs: 世界坐标系
        center_coord: 中心坐标 (SkyCoord对象)
        r_eff: 有效半径（度）
        threshold_factor: 阈值因子（相对于百分位数或峰值）
        max_radius_factor: 最大半径因子（相对于r_eff）
        search_radius_factor: PDR搜索半径因子（相对于r_eff），默认为3.0
        use_contour_method: 是否使用等高线方法，默认为False
        use_ratio_method: 是否使用W3/W4波段比值方法，默认为False
        n_contours: 等高线方法中生成的等高线数量，默认为10
        contour_start_nsigma: 等高线方法中最低等高线水平（相对于背景标准差的倍数），默认为3.0
        source_name: 源名称，用于保存等高线图像和比值图像
        output_dir: 输出目录，用于保存等高线图像和比值图像
        save_contour_plot: 是否保存等高线图像，默认为False
        save_ratio_plot: 是否保存比值图像，默认为False
        config_path: 配置文件路径，默认为'config/default_config.yaml'

    Returns:
        dict: 包含PDR、空腔和外部区域掩模的字典
    """
    # 定义PDR掩模
    pdr_result = define_pdr_mask(
        processed_image, wcs, center_coord, r_eff,
        threshold_factor, max_radius_factor, search_radius_factor,
        use_contour_method, use_ratio_method, n_contours, contour_start_nsigma,
        source_name, output_dir, save_contour_plot, save_ratio_plot, config_path
    )

    # 解包结果
    if isinstance(pdr_result, tuple) and len(pdr_result) == 3:
        # 新版本返回 (pdr_mask, w3_data, w4_data)
        pdr_mask, w3_data, w4_data = pdr_result
        logger.info("使用改进的PDR检测方法，获取了W3和W4数据")
    else:
        # 旧版本只返回 pdr_mask
        pdr_mask = pdr_result
        w3_data = None
        w4_data = None
        logger.info("使用传统PDR检测方法，没有获取W3和W4数据")

    # 定义空腔掩模
    if w4_data is not None:
        # 使用W4数据定义空腔掩模
        logger.info("使用W4波段强度分布定义空腔区域")
        # 创建新的define_cavity_mask函数
        from skimage import morphology
        from scipy import ndimage as ndi

        # 获取中心坐标在图像中的像素位置
        center_x, center_y = wcs.world_to_pixel(center_coord)

        # 计算像素尺度
        test_offset = 1.0 / 3600.0  # 1角秒
        if hasattr(center_coord, 'ra'):
            # 赤道坐标系
            test_coord = SkyCoord(ra=center_coord.ra + test_offset * u.degree,
                                dec=center_coord.dec,
                                frame=center_coord.frame)
        elif hasattr(center_coord, 'l'):
            # 银道坐标系
            test_coord = SkyCoord(l=center_coord.l + test_offset * u.degree,
                                b=center_coord.b,
                                frame=center_coord.frame)
        else:
            # 未知坐标系
            raise ValueError("未知的坐标系统类型")

        test_x, test_y = wcs.world_to_pixel(test_coord)
        pixel_scale = np.sqrt((test_x - center_x)**2 + (test_y - center_y)**2) * 3600  # 像素/角秒

        # 计算有效半径（像素）
        r_eff_pixels = r_eff * 3600 / pixel_scale

        # 创建距离图像
        y, x = np.ogrid[:pdr_mask.shape[0], :pdr_mask.shape[1]]
        distance_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)

        # 在有效半径内的区域
        radius_mask = distance_from_center <= r_eff_pixels

        # 处理W4数据
        w4_processed = w4_data.copy()
        w4_processed[np.isnan(w4_processed)] = 0
        w4_processed[w4_processed < 0] = 0

        # 在有效半径内计算W4的统计信息
        w4_in_radius = w4_processed[radius_mask]
        if np.sum(w4_in_radius > 0) > 0:
            # 使用中值和标准差计算阈值
            w4_median = np.median(w4_in_radius[w4_in_radius > 0])
            w4_std = np.std(w4_in_radius[w4_in_radius > 0])
            w4_threshold = w4_median + 0.5 * w4_std
            logger.info(f"W4统计: 中值={w4_median:.2f}, 标准差={w4_std:.2f}, 阈值={w4_threshold:.2f}")
        else:
            # 如果没有有效数据，使用全局统计信息
            w4_valid = w4_processed[w4_processed > 0]
            if len(w4_valid) > 0:
                w4_threshold = np.median(w4_valid)
                logger.info(f"使用全局W4统计: 中值={np.median(w4_valid):.2f}, 阈值={w4_threshold:.2f}")
            else:
                # 如果没有有效数据，使用0作为阈值
                w4_threshold = 0
                logger.warning("没有有效的W4数据，使用0作为阈值")

        # 识别W4高强度区域
        w4_high_mask = w4_processed > w4_threshold

        # 定义空腔区域：在有效半径内，W4强度高，且不在PDR区域内
        cavity_mask = radius_mask & w4_high_mask & ~pdr_mask

        # 应用形态学操作，填充孔洞并平滑边界
        cavity_mask = morphology.binary_closing(cavity_mask, morphology.disk(3))
        cavity_mask = ndi.binary_fill_holes(cavity_mask)

        logger.info(f"空腔区域识别: 使用W4高强度区域，覆盖{np.sum(cavity_mask)}个像素")
    else:
        # 使用几何定义空腔掩模
        logger.info("使用几何定义空腔区域")
        cavity_mask = define_cavity_mask(pdr_mask, wcs, center_coord, r_eff)

    # 定义外部掩模
    external_mask = define_external_mask(pdr_mask, cavity_mask, wcs, center_coord, r_eff, max_radius_factor)

    return {
        'pdr': pdr_mask,
        'cavity': cavity_mask,
        'external': external_mask
    }

def get_region_tags(gaia_coords, region_masks, wcs):
    """
    获取Gaia恒星的区域标签

    Args:
        gaia_coords: Gaia恒星坐标 (SkyCoord对象)
        region_masks: 区域掩模字典
        wcs: 世界坐标系

    Returns:
        numpy.ndarray: 区域标签数组
    """
    # 获取Gaia恒星在图像中的像素位置
    gaia_x, gaia_y = wcs.world_to_pixel(gaia_coords)

    # 初始化区域标签数组
    region_tags = np.full(len(gaia_x), 'unknown', dtype='U10')

    # 为每颗恒星分配区域标签
    for i in range(len(gaia_x)):
        x, y = int(gaia_x[i]), int(gaia_y[i])

        # 检查坐标是否在图像范围内
        if 0 <= y < region_masks['pdr'].shape[0] and 0 <= x < region_masks['pdr'].shape[1]:
            if region_masks['pdr'][y, x]:
                region_tags[i] = 'pdr'
            elif region_masks['cavity'][y, x]:
                region_tags[i] = 'cavity'
            elif region_masks['external'][y, x]:
                region_tags[i] = 'external'

    return region_tags

def define_regions(processed_image, wcs, center_coord, r_eff, gaia_coords=None,
                  threshold_factor=0.3, max_radius_factor=5.0, search_radius_factor=3.0,
                  use_contour_method=False, use_ratio_method=False, n_contours=10, contour_start_nsigma=3.0,
                  source_name=None, output_dir=None, save_contour_plot=False, save_ratio_plot=False,
                  config_path='config/default_config.yaml'):
    """
    定义区域并为Gaia恒星分配区域标签

    Args:
        processed_image: 处理后的WISE图像
        wcs: 世界坐标系
        center_coord: 中心坐标 (SkyCoord对象)
        r_eff: 有效半径（度）
        gaia_coords: Gaia恒星坐标 (SkyCoord对象)，默认为None
        threshold_factor: 阈值因子（相对于百分位数或峰值）
        max_radius_factor: 最大半径因子（相对于r_eff）
        search_radius_factor: PDR搜索半径因子（相对于r_eff），默认为3.0
        use_contour_method: 是否使用等高线方法，默认为False
        use_ratio_method: 是否使用W3/W4波段比值方法，默认为False
        n_contours: 等高线方法中生成的等高线数量，默认为10
        contour_start_nsigma: 等高线方法中最低等高线水平（相对于背景标准差的倍数），默认为3.0
        source_name: 源名称，用于保存等高线图像和比值图像
        output_dir: 输出目录，用于保存等高线图像和比值图像
        save_contour_plot: 是否保存等高线图像，默认为False
        save_ratio_plot: 是否保存比值图像，默认为False
        config_path: 配置文件路径，默认为'config/default_config.yaml'

    Returns:
        tuple: (区域掩模字典, 区域标签数组)
    """
    # 获取区域掩模
    region_masks = get_region_masks(
        processed_image, wcs, center_coord, r_eff,
        threshold_factor, max_radius_factor, search_radius_factor,
        use_contour_method, use_ratio_method, n_contours, contour_start_nsigma,
        source_name, output_dir, save_contour_plot, save_ratio_plot, config_path
    )

    # 如果提供了Gaia恒星坐标，为每颗恒星分配区域标签
    region_tags = None
    if gaia_coords is not None:
        region_tags = get_region_tags(gaia_coords, region_masks, wcs)

    return region_masks, region_tags
