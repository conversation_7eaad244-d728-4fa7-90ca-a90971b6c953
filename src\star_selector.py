"""
恒星选择模块

负责应用质量筛选和识别YSO，为后续分析准备恒星样本。
"""

import numpy as np
from astropy.table import Table, Column

from src.utils.logger import setup_logger
from src.utils.helpers import load_config

# 设置日志记录器
logger = setup_logger(name='star_selector')

def apply_quality_cuts(star_table, config_path='config/default_config.yaml'):
    """
    应用Gaia质量筛选

    Args:
        star_table: 恒星表 (astropy.table.Table)
        config_path: 配置文件路径

    Returns:
        tuple: (筛选后的表, 质量标志数组)
    """
    config = load_config(config_path)
    quality_params = config['processing']['gaia_quality']

    logger.info(f"应用Gaia质量筛选，参数: {quality_params}")

    try:
        # 创建质量标志数组
        quality_flags = np.ones(len(star_table), dtype=bool)

        # 应用视差信噪比筛选
        if 'parallax' in star_table.colnames and 'parallax_error' in star_table.colnames:
            # 检查是否有有效的视差数据
            valid_parallax = ~np.isnan(star_table['parallax']) & ~np.isnan(star_table['parallax_error']) & (star_table['parallax_error'] > 0)

            if np.sum(valid_parallax) > 0:
                parallax_snr = np.zeros(len(star_table))
                parallax_snr[valid_parallax] = np.abs(star_table['parallax'][valid_parallax] / star_table['parallax_error'][valid_parallax])
                parallax_snr_mask = parallax_snr >= quality_params['parallax_snr_min']
                quality_flags &= parallax_snr_mask
                logger.info(f"视差信噪比筛选: 保留{np.sum(parallax_snr_mask)}/{len(star_table)}个源")
            else:
                logger.warning("没有有效的视差数据，跳过视差信噪比筛选")
        else:
            logger.warning("表中缺少视差或视差误差列，跳过视差信噪比筛选")

        # 应用RUWE筛选
        if 'ruwe' in star_table.colnames:
            # 检查是否有有效的RUWE数据
            valid_ruwe = ~np.isnan(star_table['ruwe'])

            if np.sum(valid_ruwe) > 0:
                ruwe_mask = np.ones(len(star_table), dtype=bool)
                ruwe_mask[valid_ruwe] = star_table['ruwe'][valid_ruwe] <= quality_params['ruwe_max']
                quality_flags &= ruwe_mask
                logger.info(f"RUWE筛选: 保留{np.sum(ruwe_mask)}/{len(star_table)}个源")
            else:
                logger.warning("没有有效的RUWE数据，跳过RUWE筛选")
        else:
            logger.warning("表中缺少RUWE列，跳过RUWE筛选")

        # 应用其他可能的质量筛选...

        # 创建筛选后的表
        filtered_table = star_table[quality_flags]

        # 记录详细的质量筛选信息
        logger.info(f"质量筛选详细信息:")
        logger.info(f"  视差信噪比阈值: {quality_params['parallax_snr_min']}")
        logger.info(f"  RUWE阈值: {quality_params['ruwe_max']}")

        # 记录各项筛选的通过率
        if 'parallax' in star_table.colnames and 'parallax_error' in star_table.colnames:
            valid_parallax = ~np.isnan(star_table['parallax']) & ~np.isnan(star_table['parallax_error']) & (star_table['parallax_error'] > 0)
            if np.sum(valid_parallax) > 0:
                parallax_snr = np.zeros(len(star_table))
                parallax_snr[valid_parallax] = np.abs(star_table['parallax'][valid_parallax] / star_table['parallax_error'][valid_parallax])
                parallax_snr_mask = parallax_snr >= quality_params['parallax_snr_min']
                pass_rate = np.sum(parallax_snr_mask) / np.sum(valid_parallax) * 100 if np.sum(valid_parallax) > 0 else 0
                logger.info(f"  视差信噪比筛选通过率: {pass_rate:.1f}%")

        if 'ruwe' in star_table.colnames:
            valid_ruwe = ~np.isnan(star_table['ruwe'])
            if np.sum(valid_ruwe) > 0:
                ruwe_mask = np.ones(len(star_table), dtype=bool)
                ruwe_mask[valid_ruwe] = star_table['ruwe'][valid_ruwe] <= quality_params['ruwe_max']
                pass_rate = np.sum(ruwe_mask) / np.sum(valid_ruwe) * 100 if np.sum(valid_ruwe) > 0 else 0
                logger.info(f"  RUWE筛选通过率: {pass_rate:.1f}%")

        # 记录总体通过率
        overall_pass_rate = len(filtered_table) / len(star_table) * 100 if len(star_table) > 0 else 0
        logger.info(f"  总体通过率: {overall_pass_rate:.1f}%")

        logger.info(f"质量筛选完成，保留{len(filtered_table)}/{len(star_table)}个源")
        return filtered_table, quality_flags

    except Exception as e:
        logger.error(f"应用质量筛选失败: {str(e)}")
        raise

def flag_ysos_wise(star_table, config_path='config/default_config.yaml'):
    """
    基于WISE颜色识别YSO

    Args:
        star_table: 恒星表 (astropy.table.Table)，包含WISE数据
        config_path: 配置文件路径

    Returns:
        numpy.ndarray: YSO标志数组 (True表示是YSO)
    """
    config = load_config(config_path)
    yso_params = config['processing']['yso_identification']

    logger.info(f"基于WISE颜色识别YSO，参数: {yso_params}")

    try:
        # 初始化YSO标志数组
        yso_flags = np.zeros(len(star_table), dtype=bool)

        # 检查是否有WISE颜色数据
        w1_key = next((col for col in star_table.colnames if 'w1' in col.lower()), None)
        w2_key = next((col for col in star_table.colnames if 'w2' in col.lower()), None)
        w3_key = next((col for col in star_table.colnames if 'w3' in col.lower()), None)

        if w1_key and w2_key and len(star_table) > 0:
            # 检查是否有有效的颜色数据
            valid_w1w2 = ~np.isnan(star_table[w1_key]) & ~np.isnan(star_table[w2_key])

            if np.sum(valid_w1w2) > 0:
                # 计算W1-W2颜色
                w1w2_color = np.zeros(len(star_table))
                w1w2_color[valid_w1w2] = star_table[w1_key][valid_w1w2] - star_table[w2_key][valid_w1w2]

                # 应用W1-W2颜色筛选
                w1w2_mask = np.zeros(len(star_table), dtype=bool)
                w1w2_mask[valid_w1w2] = w1w2_color[valid_w1w2] >= yso_params['w1w2_min']

                # 如果有W3数据，可以应用更复杂的颜色筛选
                if w3_key:
                    valid_w2w3 = valid_w1w2 & ~np.isnan(star_table[w3_key])

                    if np.sum(valid_w2w3) > 0:
                        # 这里可以实现更复杂的YSO识别标准
                        # 例如，使用W1-W2 vs W2-W3颜色-颜色图
                        # 简单示例：
                        # w2w3_color = np.zeros(len(star_table))
                        # w2w3_color[valid_w2w3] = star_table[w2_key][valid_w2w3] - star_table[w3_key][valid_w2w3]
                        # yso_mask = (w1w2_color >= yso_params['w1w2_min']) & (w2w3_color >= some_threshold)

                        # 暂时使用简单的W1-W2标准
                        yso_flags = w1w2_mask
                    else:
                        # 如果没有有效的W2-W3数据，只使用W1-W2
                        yso_flags = w1w2_mask
                else:
                    # 如果没有W3数据，只使用W1-W2
                    yso_flags = w1w2_mask
            else:
                logger.warning("没有有效的WISE颜色数据")
                yso_flags = np.zeros(len(star_table), dtype=bool)

            logger.info(f"识别出{np.sum(yso_flags)}个YSO候选体")
        else:
            logger.warning("表中缺少WISE颜色数据，无法识别YSO")

        return yso_flags

    except Exception as e:
        logger.error(f"识别YSO失败: {str(e)}")
        raise

def filter_star_sample(star_table, quality_flags=None, yso_flags=None, region_tags=None):
    """
    根据质量标志、YSO标志和区域标签筛选恒星样本

    Args:
        star_table: 恒星表 (astropy.table.Table)
        quality_flags: 质量标志数组
        yso_flags: YSO标志数组
        region_tags: 区域标签数组

    Returns:
        astropy.table.Table: 筛选后的表
    """
    logger.info(f"筛选恒星样本，原始样本大小: {len(star_table)}")

    try:
        # 创建筛选掩模
        mask = np.ones(len(star_table), dtype=bool)

        # 应用质量筛选
        if quality_flags is not None:
            mask &= quality_flags
            logger.info(f"应用质量筛选后: {np.sum(mask)}/{len(star_table)}个源")

        # 排除YSO
        if yso_flags is not None:
            mask &= ~yso_flags
            logger.info(f"排除YSO后: {np.sum(mask)}/{len(star_table)}个源")

        # 筛选区域
        if region_tags is not None:
            # 添加区域标签列
            if 'region' not in star_table.colnames:
                star_table.add_column(Column(region_tags, name='region'))

            # 只保留有效区域内的源
            valid_regions = ['cavity', 'pdr', 'external']
            region_mask = np.array([tag in valid_regions for tag in region_tags])
            mask &= region_mask
            logger.info(f"筛选有效区域后: {np.sum(mask)}/{len(star_table)}个源")

        # 创建筛选后的表
        filtered_table = star_table[mask]

        # 统计各区域的源数量
        if 'region' in filtered_table.colnames:
            for region in ['cavity', 'pdr', 'external']:
                count = np.sum(filtered_table['region'] == region)
                logger.info(f"区域 {region}: {count}个源")

        logger.info(f"恒星样本筛选完成，最终样本大小: {len(filtered_table)}")
        return filtered_table

    except Exception as e:
        logger.error(f"筛选恒星样本失败: {str(e)}")
        raise
