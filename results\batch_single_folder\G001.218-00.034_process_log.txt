处理时间: 2025-05-20 09:42:23
耗时: 174.82秒

标准输出:

未能估计G001.218-00.034的分子云距离


标准错误:
2025-05-20 09:39:32,172 - main_single_folder - INFO - 输出目录: results/batch_single_folder
2025-05-20 09:39:32,173 - main_single_folder - INFO - 加载源G001.218-00.034的信息
2025-05-20 09:39:32,173 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
2025-05-20 09:39:32,178 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-05-20 09:39:32,180 - main_single_folder - INFO - 从源名解析银道坐标: l=1.218000, b=-0.034000
2025-05-20 09:39:32,186 - main_single_folder - INFO - 银道坐标转换为赤道坐标: RA=267.155957, Dec=-27.912203 (ICRS)
2025-05-20 09:39:32,186 - main_single_folder - INFO - 从源表加载信息: RA=267.155957, Dec=-27.912203, R_eff=0.169722度
2025-05-20 09:39:32,187 - main_single_folder - INFO - 步骤1：加载WISE数据
2025-05-20 09:39:32,191 - data_manager - INFO - 为源G001.218-00.034加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G001.218-00.034
2025-05-20 09:39:32,201 - data_manager - INFO - 目录中的所有文件: ['G001.218-00.034_ATLASGAL_870um.fits', 'G001.218-00.034_IRIS_100.fits', 'G001.218-00.034_NVSS.fits', 'G001.218-00.034_WISE_12.fits', 'G001.218-00.034_WISE_22.fits', 'G001.218-00.034_WISE_3.4.fits', 'G001.218-00.034_WISE_4.6.fits']
2025-05-20 09:39:32,201 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:39:32,201 - data_manager - INFO - 第一次匹配结果: ['G001.218-00.034_WISE_12.fits']
2025-05-20 09:39:32,201 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G001.218-00.034\G001.218-00.034_WISE_12.fits
2025-05-20 09:39:32,288 - data_manager - INFO - FITS数据统计: 最小值=895.8681030273438, 最大值=9402.419921875, 均值=1502.4730224609375, 中位数=1376.12255859375
2025-05-20 09:39:32,288 - data_manager - INFO - 有效数据点数量: 883319/883600 (99.97%)
2025-05-20 09:39:32,297 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (940, 940)
2025-05-20 09:39:32,297 - main_single_folder - INFO - WISE数据加载完成
2025-05-20 09:39:32,297 - main_single_folder - INFO - 步骤2：区域映射
2025-05-20 09:39:32,326 - main_single_folder - INFO - 使用W3/W4波段比值方法定义PDR区域
2025-05-20 09:39:32,326 - region_mapper - INFO - 使用W3/W4波段比值方法定义PDR掩模
2025-05-20 09:39:32,326 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:39:32,326 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:39:32,326 - region_mapper.ratio - INFO - 加载源G001.218-00.034的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:39:32,326 - region_mapper.ratio - INFO - 加载源G001.218-00.034的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:39:32,327 - data_manager - INFO - 为源G001.218-00.034加载多波段WISE数据: ('12', '22')
2025-05-20 09:39:32,331 - data_manager - INFO - 为源G001.218-00.034加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G001.218-00.034
2025-05-20 09:39:32,332 - data_manager - INFO - 目录中的所有文件: ['G001.218-00.034_ATLASGAL_870um.fits', 'G001.218-00.034_IRIS_100.fits', 'G001.218-00.034_NVSS.fits', 'G001.218-00.034_WISE_12.fits', 'G001.218-00.034_WISE_22.fits', 'G001.218-00.034_WISE_3.4.fits', 'G001.218-00.034_WISE_4.6.fits']
2025-05-20 09:39:32,332 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:39:32,332 - data_manager - INFO - 第一次匹配结果: ['G001.218-00.034_WISE_12.fits']
2025-05-20 09:39:32,332 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G001.218-00.034\G001.218-00.034_WISE_12.fits
2025-05-20 09:39:32,352 - data_manager - INFO - FITS数据统计: 最小值=895.8681030273438, 最大值=9402.419921875, 均值=1502.4730224609375, 中位数=1376.12255859375
2025-05-20 09:39:32,352 - data_manager - INFO - 有效数据点数量: 883319/883600 (99.97%)
2025-05-20 09:39:32,424 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (940, 940)
2025-05-20 09:39:32,425 - data_manager - INFO - 使用12μm波段的WCS作为参考
2025-05-20 09:39:32,425 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (940, 940)
2025-05-20 09:39:32,431 - data_manager - INFO - 为源G001.218-00.034加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G001.218-00.034
2025-05-20 09:39:32,431 - data_manager - INFO - 目录中的所有文件: ['G001.218-00.034_ATLASGAL_870um.fits', 'G001.218-00.034_IRIS_100.fits', 'G001.218-00.034_NVSS.fits', 'G001.218-00.034_WISE_12.fits', 'G001.218-00.034_WISE_22.fits', 'G001.218-00.034_WISE_3.4.fits', 'G001.218-00.034_WISE_4.6.fits']
2025-05-20 09:39:32,431 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:39:32,431 - data_manager - INFO - 第一次匹配结果: ['G001.218-00.034_WISE_22.fits']
2025-05-20 09:39:32,431 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G001.218-00.034\G001.218-00.034_WISE_22.fits
2025-05-20 09:39:32,502 - data_manager - INFO - FITS数据统计: 最小值=318.38128662109375, 最大值=2527.570556640625, 均值=355.3402099609375, 中位数=337.1629333496094
2025-05-20 09:39:32,502 - data_manager - INFO - 有效数据点数量: 258974/259081 (99.96%)
2025-05-20 09:39:32,510 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (509, 509)
2025-05-20 09:39:32,510 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (509, 509)
2025-05-20 09:39:32,510 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w3', 'w4']
2025-05-20 09:39:32,522 - region_mapper.ratio - INFO - 调整W4波段数据从(509, 509)到(940, 940)
2025-05-20 09:39:32,522 - region_mapper.ratio - INFO - 调整W4波段数据从(509, 509)到(940, 940)
2025-05-20 09:39:32,690 - region_mapper.ratio - INFO - W3/W4比值范围: 3.07-6.84，均值: 4.19
2025-05-20 09:39:32,690 - region_mapper.ratio - INFO - W3/W4比值范围: 3.07-6.84，均值: 4.19
2025-05-20 09:39:32,699 - region_mapper.ratio - INFO - 像素尺度: 489.41 像素/角秒
2025-05-20 09:39:32,699 - region_mapper.ratio - INFO - 像素尺度: 489.41 像素/角秒
2025-05-20 09:39:32,699 - region_mapper.ratio - INFO - 有效半径: 1.2 像素
2025-05-20 09:39:32,699 - region_mapper.ratio - INFO - 有效半径: 1.2 像素
2025-05-20 09:39:32,699 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:39:32,699 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:39:33,473 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.574
2025-05-20 09:39:33,473 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.574
2025-05-20 09:39:33,550 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.586
2025-05-20 09:39:33,550 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.586
2025-05-20 09:39:33,627 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.576
2025-05-20 09:39:33,627 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.576
2025-05-20 09:39:33,627 - region_mapper.ratio - INFO - 使用最佳聚类数: 3
2025-05-20 09:39:33,627 - region_mapper.ratio - INFO - 使用最佳聚类数: 3
2025-05-20 09:39:33,697 - region_mapper.ratio - INFO - 聚类 0: 平均比值=3.93±0.07, 大小=128, 平均距离=5.1, 紧凑性=5.1
2025-05-20 09:39:33,697 - region_mapper.ratio - INFO - 聚类 0: 平均比值=3.93±0.07, 大小=128, 平均距离=5.1, 紧凑性=5.1
2025-05-20 09:39:33,698 - region_mapper.ratio - INFO - 聚类 1: 平均比值=4.17±0.08, 大小=118, 平均距离=7.7, 紧凑性=6.5
2025-05-20 09:39:33,698 - region_mapper.ratio - INFO - 聚类 1: 平均比值=4.17±0.08, 大小=118, 平均距离=7.7, 紧凑性=6.5
2025-05-20 09:39:33,699 - region_mapper.ratio - INFO - 聚类 2: 平均比值=3.63±0.07, 大小=65, 平均距离=7.7, 紧凑性=3.6
2025-05-20 09:39:33,699 - region_mapper.ratio - INFO - 聚类 2: 平均比值=3.63±0.07, 大小=65, 平均距离=7.7, 紧凑性=3.6
2025-05-20 09:39:33,699 - region_mapper.ratio - INFO - 选择聚类 1 作为PDR区域
2025-05-20 09:39:33,699 - region_mapper.ratio - INFO - 选择聚类 1 作为PDR区域
2025-05-20 09:39:33,800 - region_mapper.ratio - INFO - PDR掩模覆盖138个像素
2025-05-20 09:39:33,800 - region_mapper.ratio - INFO - PDR掩模覆盖138个像素
2025-05-20 09:39:33,819 - region_mapper.ratio - INFO - 发现2个连通区域，选择包含中心或最近的区域
2025-05-20 09:39:33,819 - region_mapper.ratio - INFO - 发现2个连通区域，选择包含中心或最近的区域
2025-05-20 09:39:38,519 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G001.218-00.034_w3w4_ratio.png
2025-05-20 09:39:38,519 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G001.218-00.034_w3w4_ratio.png
2025-05-20 09:39:38,521 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖137个像素
2025-05-20 09:39:38,521 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖137个像素
2025-05-20 09:39:38,579 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G001.218-00.034_region_masks.fits
2025-05-20 09:39:38,579 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G001.218-00.034_region_masks.fits
2025-05-20 09:39:38,584 - data_manager - INFO - 为源G001.218-00.034加载Gaia数据，中心坐标: RA=267.155957, Dec=-27.912203, 半径=0.8486度
2025-05-20 09:39:38,587 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances-Data/gaia_data\gaia_G001.218-00.034_51arcmin_5R.csv
H:\Augment\Parallax distances\src\data_manager.py:87: DtypeWarning: Columns (47,52,54) have mixed types. Specify dtype option on import or set low_memory=False.
  df = pd.read_csv(gaia_file)
2025-05-20 09:39:45,910 - data_manager - INFO - 成功加载Gaia数据，共492671条记录
2025-05-20 09:39:46,320 - data_manager - INFO - 成功加载Gaia数据，共492667个源在搜索半径内
2025-05-20 09:39:47,489 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G001.218-00.034_gaia_regions.fits
2025-05-20 09:41:57,439 - data_manager - WARNING - 检测到可能导致保存问题的列: ['_2MASS', 'twomass__2MASS', 'region']
2025-05-20 09:41:57,615 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:42:08,279 - data_manager - ERROR - 保存数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U8'), dtype('<U11'), dtype('<U10'), dtype('<U9'), dtype('<U1')}
2025-05-20 09:42:08,279 - main_single_folder - ERROR - 加载Gaia数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U8'), dtype('<U11'), dtype('<U10'), dtype('<U9'), dtype('<U1')}
2025-05-20 09:42:08,279 - main_single_folder - WARNING - 将使用空的Gaia数据继续处理
2025-05-20 09:42:08,702 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:42:08,702 - main_single_folder - INFO - 步骤3：恒星选择
2025-05-20 09:42:08,707 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-05-20 09:42:08,708 - star_selector - WARNING - 没有有效的视差数据，跳过视差信噪比筛选
2025-05-20 09:42:08,708 - star_selector - WARNING - 没有有效的RUWE数据，跳过RUWE筛选
2025-05-20 09:42:08,709 - star_selector - INFO - 质量筛选详细信息:
2025-05-20 09:42:08,709 - star_selector - INFO -   视差信噪比阈值: 5.0
2025-05-20 09:42:08,709 - star_selector - INFO -   RUWE阈值: 1.4
2025-05-20 09:42:08,709 - star_selector - INFO -   总体通过率: 0.0%
2025-05-20 09:42:08,709 - star_selector - INFO - 质量筛选完成，保留0/0个源
2025-05-20 09:42:08,710 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G001.218-00.034_filtered_data.fits
2025-05-20 09:42:08,723 - data_manager - INFO - 成功保存数据
2025-05-20 09:42:08,723 - main_single_folder - INFO - 保存质量筛选后的数据到: results/batch_single_folder\processed\G001.218-00.034_filtered_data.fits
2025-05-20 09:42:08,728 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-05-20 09:42:08,728 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-05-20 09:42:08,728 - star_selector - INFO - 筛选恒星样本，原始样本大小: 0
2025-05-20 09:42:08,728 - star_selector - INFO - 排除YSO后: 0/0个源
2025-05-20 09:42:08,729 - star_selector - INFO - 区域 cavity: 0个源
2025-05-20 09:42:08,729 - star_selector - INFO - 区域 pdr: 0个源
2025-05-20 09:42:08,729 - star_selector - INFO - 区域 external: 0个源
2025-05-20 09:42:08,730 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 0
2025-05-20 09:42:08,730 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G001.218-00.034_star_sample.fits
2025-05-20 09:42:08,742 - data_manager - INFO - 成功保存数据
2025-05-20 09:42:08,742 - main_single_folder - INFO - 恒星选择完成
2025-05-20 09:42:08,742 - main_single_folder - INFO - 步骤4：消光计算
2025-05-20 09:42:08,747 - extinction_estimator - INFO - 为0个恒星计算A_V，参数: {'rv': 3.1}
2025-05-20 09:42:08,747 - extinction_estimator - WARNING - 星表为空，无法计算A_V
2025-05-20 09:42:08,748 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G001.218-00.034_stars_with_av.fits
2025-05-20 09:42:08,760 - data_manager - INFO - 成功保存数据
2025-05-20 09:42:08,760 - main_single_folder - INFO - 消光计算完成
2025-05-20 09:42:08,760 - main_single_folder - INFO - 步骤5：距离分析
2025-05-20 09:42:08,760 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-05-20 09:42:08,761 - distance_analyzer - INFO - 将分析0个区域: 
2025-05-20 09:42:08,761 - distance_analyzer - INFO - 估计分子云距离
2025-05-20 09:42:08,761 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity, pdr, external
2025-05-20 09:42:08,761 - distance_analyzer - WARNING - 没有检测到任何跳变，无法估计距离
2025-05-20 09:42:09,318 - main_single_folder - INFO - 距离分析完成
2025-05-20 09:42:09,318 - main_single_folder - INFO - 步骤6：可视化和报告生成
2025-05-20 09:42:09,323 - data_manager - INFO - 为源G001.218-00.034加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G001.218-00.034
2025-05-20 09:42:09,323 - data_manager - INFO - 目录中的所有文件: ['G001.218-00.034_ATLASGAL_870um.fits', 'G001.218-00.034_IRIS_100.fits', 'G001.218-00.034_NVSS.fits', 'G001.218-00.034_WISE_12.fits', 'G001.218-00.034_WISE_22.fits', 'G001.218-00.034_WISE_3.4.fits', 'G001.218-00.034_WISE_4.6.fits']
2025-05-20 09:42:09,323 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:42:09,324 - data_manager - INFO - 第一次匹配结果: ['G001.218-00.034_WISE_12.fits']
2025-05-20 09:42:09,324 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G001.218-00.034\G001.218-00.034_WISE_12.fits
2025-05-20 09:42:09,344 - data_manager - INFO - FITS数据统计: 最小值=895.8681030273438, 最大值=9402.419921875, 均值=1502.4730224609375, 中位数=1376.12255859375
2025-05-20 09:42:09,345 - data_manager - INFO - 有效数据点数量: 883319/883600 (99.97%)
2025-05-20 09:42:09,354 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (940, 940)
2025-05-20 09:42:09,384 - visualizer - INFO - 绘制WISE图像和区域掩模，输出到: results/batch_single_folder\visualizations\G001.218-00.034_wise_regions_stars.png
2025-05-20 09:42:09,505 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:42:11,563 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:42:11,571 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:42:16,077 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G001.218-00.034_wise_regions_stars.png
2025-05-20 09:42:16,077 - main_single_folder - INFO - 成功保存WISE图像和区域掩模到: results/batch_single_folder\visualizations\G001.218-00.034_wise_regions_stars.png
2025-05-20 09:42:16,077 - main_single_folder - INFO - 加载WISE多波段数据用于RGB图像生成
2025-05-20 09:42:16,077 - data_manager - INFO - 为源G001.218-00.034加载多波段WISE数据: ('3.4', '12', '22')
2025-05-20 09:42:16,083 - data_manager - INFO - 为源G001.218-00.034加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G001.218-00.034
2025-05-20 09:42:16,083 - data_manager - INFO - 目录中的所有文件: ['G001.218-00.034_ATLASGAL_870um.fits', 'G001.218-00.034_IRIS_100.fits', 'G001.218-00.034_NVSS.fits', 'G001.218-00.034_WISE_12.fits', 'G001.218-00.034_WISE_22.fits', 'G001.218-00.034_WISE_3.4.fits', 'G001.218-00.034_WISE_4.6.fits']
2025-05-20 09:42:16,083 - data_manager - INFO - 查找WISE 3.4μm波段的FITS文件
2025-05-20 09:42:16,083 - data_manager - INFO - 第一次匹配结果: ['G001.218-00.034_WISE_3.4.fits']
2025-05-20 09:42:16,083 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G001.218-00.034\G001.218-00.034_WISE_3.4.fits
2025-05-20 09:42:16,197 - data_manager - INFO - FITS数据统计: 最小值=19.775352478027344, 最大值=4469.42919921875, 均值=403.56146240234375, 中位数=312.1278076171875
2025-05-20 09:42:16,197 - data_manager - INFO - 有效数据点数量: 1001981/1002001 (100.00%)
2025-05-20 09:42:16,206 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (1001, 1001)
2025-05-20 09:42:16,207 - data_manager - INFO - 使用3.4μm波段的WCS作为参考
2025-05-20 09:42:16,207 - data_manager - INFO - 成功加载WISE 3.4μm波段数据，尺寸: (1001, 1001)
2025-05-20 09:42:16,211 - data_manager - INFO - 为源G001.218-00.034加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G001.218-00.034
2025-05-20 09:42:16,212 - data_manager - INFO - 目录中的所有文件: ['G001.218-00.034_ATLASGAL_870um.fits', 'G001.218-00.034_IRIS_100.fits', 'G001.218-00.034_NVSS.fits', 'G001.218-00.034_WISE_12.fits', 'G001.218-00.034_WISE_22.fits', 'G001.218-00.034_WISE_3.4.fits', 'G001.218-00.034_WISE_4.6.fits']
2025-05-20 09:42:16,212 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:42:16,212 - data_manager - INFO - 第一次匹配结果: ['G001.218-00.034_WISE_12.fits']
2025-05-20 09:42:16,212 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G001.218-00.034\G001.218-00.034_WISE_12.fits
2025-05-20 09:42:16,231 - data_manager - INFO - FITS数据统计: 最小值=895.8681030273438, 最大值=9402.419921875, 均值=1502.4730224609375, 中位数=1376.12255859375
2025-05-20 09:42:16,231 - data_manager - INFO - 有效数据点数量: 883319/883600 (99.97%)
2025-05-20 09:42:16,240 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (940, 940)
2025-05-20 09:42:16,241 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (940, 940)
2025-05-20 09:42:16,246 - data_manager - INFO - 为源G001.218-00.034加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G001.218-00.034
2025-05-20 09:42:16,246 - data_manager - INFO - 目录中的所有文件: ['G001.218-00.034_ATLASGAL_870um.fits', 'G001.218-00.034_IRIS_100.fits', 'G001.218-00.034_NVSS.fits', 'G001.218-00.034_WISE_12.fits', 'G001.218-00.034_WISE_22.fits', 'G001.218-00.034_WISE_3.4.fits', 'G001.218-00.034_WISE_4.6.fits']
2025-05-20 09:42:16,246 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:42:16,246 - data_manager - INFO - 第一次匹配结果: ['G001.218-00.034_WISE_22.fits']
2025-05-20 09:42:16,246 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G001.218-00.034\G001.218-00.034_WISE_22.fits
2025-05-20 09:42:16,255 - data_manager - INFO - FITS数据统计: 最小值=318.38128662109375, 最大值=2527.570556640625, 均值=355.3402099609375, 中位数=337.1629333496094
2025-05-20 09:42:16,255 - data_manager - INFO - 有效数据点数量: 258974/259081 (99.96%)
2025-05-20 09:42:16,264 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (509, 509)
2025-05-20 09:42:16,264 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (509, 509)
2025-05-20 09:42:16,264 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w1', 'w3', 'w4']
2025-05-20 09:42:16,264 - visualizer - INFO - 创建WISE三波段RGB图像，输出到: results/batch_single_folder\visualizations\G001.218-00.034_wise_rgb.png
2025-05-20 09:42:16,395 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:42:16,395 - visualizer - INFO - 波段 3.4μm 的尺寸: (1001, 1001)
2025-05-20 09:42:16,395 - visualizer - INFO - 波段 12μm 的尺寸: (940, 940)
2025-05-20 09:42:16,395 - visualizer - INFO - 波段 22μm 的尺寸: (509, 509)
2025-05-20 09:42:16,395 - visualizer - INFO - Using 12μm band size as target: (940, 940)
2025-05-20 09:42:16,395 - visualizer - INFO - Using target image size: (940, 940), original sizes: {'w1': (1001, 1001), 'w3': (940, 940), 'w4': (509, 509)}
2025-05-20 09:42:16,510 - visualizer - INFO - 调整w4波段数据从(509, 509)到(940, 940)
2025-05-20 09:42:16,513 - visualizer - INFO - 红色通道(22μm)数据范围: 0.6305530071258545-2527.570556640625
2025-05-20 09:42:16,521 - visualizer - INFO - 绿色通道(12μm)数据范围: 895.8681030273438-9402.419921875
2025-05-20 09:42:16,688 - visualizer - INFO - 调整w1波段数据从(1001, 1001)到(940, 940)
2025-05-20 09:42:16,692 - visualizer - INFO - 蓝色通道(3.4μm)数据范围: 18.394826889038086-4469.42919921875
2025-05-20 09:42:16,727 - visualizer - INFO - 使用Lupton RGB方法，stretch=2501.45, Q=10
2025-05-20 09:42:16,727 - visualizer - INFO - 各波段99.5%百分位数: R=1027.24, G=5002.89, B=1725.14
2025-05-20 09:42:16,898 - visualizer - INFO - RGB数据形状: (940, 940, 3), 类型: float32
2025-05-20 09:42:16,904 - visualizer - INFO - Red通道数据范围：0.0-0.5372549295425415，均值：0.0956
2025-05-20 09:42:16,908 - visualizer - INFO - Green通道数据范围：0.0-1.0，均值：0.4025
2025-05-20 09:42:16,912 - visualizer - INFO - Blue通道数据范围：0.0-0.8117647171020508，均值：0.1045
2025-05-20 09:42:18,421 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:42:18,427 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:42:18,603 - visualizer - INFO - 按照要求不显示Gaia星
2025-05-20 09:42:18,604 - visualizer - INFO - 设置坐标刻度间隔: RA=100.000度, Dec=100.000度
2025-05-20 09:42:22,984 - visualizer - INFO - 成功保存RGB图像到: results/batch_single_folder\visualizations\G001.218-00.034_wise_rgb.png
2025-05-20 09:42:22,987 - main_single_folder - INFO - 成功保存WISE RGB图像到: results/batch_single_folder\visualizations\G001.218-00.034_wise_rgb.png
2025-05-20 09:42:22,988 - main_single_folder - WARNING - 没有足够的数据绘制消光-距离散点图
2025-05-20 09:42:23,007 - main_single_folder - INFO - 成功保存处理报告到: results/batch_single_folder\reports\G001.218-00.034_report.txt
2025-05-20 09:42:23,007 - main_single_folder - INFO - 处理G001.218-00.034完成
