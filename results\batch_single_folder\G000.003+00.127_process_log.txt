处理时间: 2025-05-20 09:36:29
耗时: 29.25秒

标准输出:

未能估计G000.003+00.127的分子云距离


标准错误:
2025-05-20 09:36:03,591 - main_single_folder - INFO - 输出目录: results/batch_single_folder
2025-05-20 09:36:03,591 - main_single_folder - INFO - 加载源G000.003+00.127的信息
2025-05-20 09:36:03,591 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
2025-05-20 09:36:03,597 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-05-20 09:36:03,598 - main_single_folder - INFO - 从源名解析银道坐标: l=0.003000, b=0.127000
2025-05-20 09:36:03,603 - main_single_folder - INFO - 银道坐标转换为赤道坐标: RA=266.282993, Dec=-28.867391 (ICRS)
2025-05-20 09:36:03,604 - main_single_folder - INFO - 从源表加载信息: RA=266.282993, Dec=-28.867391, R_eff=0.057222度
2025-05-20 09:36:03,604 - main_single_folder - INFO - 步骤1：加载WISE数据
2025-05-20 09:36:03,609 - data_manager - INFO - 为源G000.003+00.127加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.003+00.127
2025-05-20 09:36:03,609 - data_manager - INFO - 目录中的所有文件: ['G000.003+00.127_ATLASGAL_870um.fits', 'G000.003+00.127_IRIS_100.fits', 'G000.003+00.127_NVSS.fits', 'G000.003+00.127_WISE_12.fits', 'G000.003+00.127_WISE_22.fits', 'G000.003+00.127_WISE_3.4.fits', 'G000.003+00.127_WISE_4.6.fits']
2025-05-20 09:36:03,610 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:36:03,610 - data_manager - INFO - 第一次匹配结果: ['G000.003+00.127_WISE_12.fits']
2025-05-20 09:36:03,610 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.003+00.127\G000.003+00.127_WISE_12.fits
2025-05-20 09:36:03,614 - data_manager - INFO - FITS数据统计: 最小值=-140.62432861328125, 最大值=10446.5400390625, 均值=3167.337890625, 中位数=2562.6845703125
2025-05-20 09:36:03,614 - data_manager - INFO - 有效数据点数量: 96116/99856 (96.25%)
2025-05-20 09:36:03,622 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (316, 316)
2025-05-20 09:36:03,622 - main_single_folder - INFO - WISE数据加载完成
2025-05-20 09:36:03,622 - main_single_folder - INFO - 步骤2：区域映射
2025-05-20 09:36:03,625 - main_single_folder - INFO - 使用W3/W4波段比值方法定义PDR区域
2025-05-20 09:36:03,626 - region_mapper - INFO - 使用W3/W4波段比值方法定义PDR掩模
2025-05-20 09:36:03,626 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:36:03,626 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:36:03,626 - region_mapper.ratio - INFO - 加载源G000.003+00.127的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:36:03,626 - region_mapper.ratio - INFO - 加载源G000.003+00.127的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:36:03,626 - data_manager - INFO - 为源G000.003+00.127加载多波段WISE数据: ('12', '22')
2025-05-20 09:36:03,631 - data_manager - INFO - 为源G000.003+00.127加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.003+00.127
2025-05-20 09:36:03,631 - data_manager - INFO - 目录中的所有文件: ['G000.003+00.127_ATLASGAL_870um.fits', 'G000.003+00.127_IRIS_100.fits', 'G000.003+00.127_NVSS.fits', 'G000.003+00.127_WISE_12.fits', 'G000.003+00.127_WISE_22.fits', 'G000.003+00.127_WISE_3.4.fits', 'G000.003+00.127_WISE_4.6.fits']
2025-05-20 09:36:03,631 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:36:03,631 - data_manager - INFO - 第一次匹配结果: ['G000.003+00.127_WISE_12.fits']
2025-05-20 09:36:03,632 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.003+00.127\G000.003+00.127_WISE_12.fits
2025-05-20 09:36:03,635 - data_manager - INFO - FITS数据统计: 最小值=-140.62432861328125, 最大值=10446.5400390625, 均值=3167.337890625, 中位数=2562.6845703125
2025-05-20 09:36:03,635 - data_manager - INFO - 有效数据点数量: 96116/99856 (96.25%)
2025-05-20 09:36:03,714 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (316, 316)
2025-05-20 09:36:03,715 - data_manager - INFO - 使用12μm波段的WCS作为参考
2025-05-20 09:36:03,715 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (316, 316)
2025-05-20 09:36:03,720 - data_manager - INFO - 为源G000.003+00.127加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.003+00.127
2025-05-20 09:36:03,720 - data_manager - INFO - 目录中的所有文件: ['G000.003+00.127_ATLASGAL_870um.fits', 'G000.003+00.127_IRIS_100.fits', 'G000.003+00.127_NVSS.fits', 'G000.003+00.127_WISE_12.fits', 'G000.003+00.127_WISE_22.fits', 'G000.003+00.127_WISE_3.4.fits', 'G000.003+00.127_WISE_4.6.fits']
2025-05-20 09:36:03,721 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:36:03,721 - data_manager - INFO - 第一次匹配结果: ['G000.003+00.127_WISE_22.fits']
2025-05-20 09:36:03,721 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.003+00.127\G000.003+00.127_WISE_22.fits
2025-05-20 09:36:03,724 - data_manager - INFO - FITS数据统计: 最小值=364.5624694824219, 最大值=3002.782958984375, 均值=650.20068359375, 中位数=427.20440673828125
2025-05-20 09:36:03,724 - data_manager - INFO - 有效数据点数量: 27844/29241 (95.22%)
2025-05-20 09:36:03,731 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (171, 171)
2025-05-20 09:36:03,731 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (171, 171)
2025-05-20 09:36:03,731 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w3', 'w4']
2025-05-20 09:36:03,733 - region_mapper.ratio - INFO - 调整W4波段数据从(171, 171)到(316, 316)
2025-05-20 09:36:03,733 - region_mapper.ratio - INFO - 调整W4波段数据从(171, 171)到(316, 316)
2025-05-20 09:36:03,753 - region_mapper.ratio - INFO - W3/W4比值范围: 0.03-113.18，均值: 1463.66
2025-05-20 09:36:03,753 - region_mapper.ratio - INFO - W3/W4比值范围: 0.03-113.18，均值: 1463.66
2025-05-20 09:36:03,760 - region_mapper.ratio - INFO - 像素尺度: 483.61 像素/角秒
2025-05-20 09:36:03,760 - region_mapper.ratio - INFO - 像素尺度: 483.61 像素/角秒
2025-05-20 09:36:03,760 - region_mapper.ratio - INFO - 有效半径: 0.4 像素
2025-05-20 09:36:03,760 - region_mapper.ratio - INFO - 有效半径: 0.4 像素
2025-05-20 09:36:03,760 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:36:03,760 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:36:04,528 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.581
2025-05-20 09:36:04,528 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.581
2025-05-20 09:36:04,609 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.542
2025-05-20 09:36:04,609 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.542
2025-05-20 09:36:04,692 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.576
2025-05-20 09:36:04,692 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.576
2025-05-20 09:36:04,692 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 09:36:04,692 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 09:36:04,760 - region_mapper.ratio - INFO - 聚类 0: 平均比值=5.56±0.29, 大小=103, 平均距离=8.0, 紧凑性=6.3
2025-05-20 09:36:04,760 - region_mapper.ratio - INFO - 聚类 0: 平均比值=5.56±0.29, 大小=103, 平均距离=8.0, 紧凑性=6.3
2025-05-20 09:36:04,761 - region_mapper.ratio - INFO - 聚类 1: 平均比值=4.88±0.21, 大小=213, 平均距离=6.0, 紧凑性=5.7
2025-05-20 09:36:04,761 - region_mapper.ratio - INFO - 聚类 1: 平均比值=4.88±0.21, 大小=213, 平均距离=6.0, 紧凑性=5.7
2025-05-20 09:36:04,761 - region_mapper.ratio - INFO - 选择聚类 0 作为PDR区域
2025-05-20 09:36:04,761 - region_mapper.ratio - INFO - 选择聚类 0 作为PDR区域
2025-05-20 09:36:04,775 - region_mapper.ratio - INFO - PDR掩模覆盖126个像素
2025-05-20 09:36:04,775 - region_mapper.ratio - INFO - PDR掩模覆盖126个像素
2025-05-20 09:36:07,463 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G000.003+00.127_w3w4_ratio.png
2025-05-20 09:36:07,463 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G000.003+00.127_w3w4_ratio.png
2025-05-20 09:36:07,464 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖126个像素
2025-05-20 09:36:07,464 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖126个像素
2025-05-20 09:36:07,494 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G000.003+00.127_region_masks.fits
2025-05-20 09:36:07,494 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G000.003+00.127_region_masks.fits
2025-05-20 09:36:07,500 - data_manager - INFO - 为源G000.003+00.127加载Gaia数据，中心坐标: RA=266.282993, Dec=-28.867391, 半径=0.2861度
2025-05-20 09:36:07,503 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances-Data/gaia_data\gaia_G000.003+00.127_17arcmin_5R.csv
2025-05-20 09:36:07,896 - data_manager - INFO - 成功加载Gaia数据，共19876条记录
2025-05-20 09:36:07,927 - data_manager - INFO - 成功加载Gaia数据，共19875个源在搜索半径内
2025-05-20 09:36:07,977 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G000.003+00.127_gaia_regions.fits
2025-05-20 09:36:13,967 - data_manager - WARNING - 检测到可能导致保存问题的列: ['TYC2', 'region']
2025-05-20 09:36:13,999 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:36:14,433 - data_manager - INFO - 成功保存数据
2025-05-20 09:36:14,436 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:36:14,436 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:36:14,437 - main_single_folder - INFO - 步骤3：恒星选择
2025-05-20 09:36:14,442 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-05-20 09:36:14,447 - star_selector - INFO - 视差信噪比筛选: 保留3907/19875个源
2025-05-20 09:36:14,448 - star_selector - INFO - RUWE筛选: 保留19375/19875个源
2025-05-20 09:36:14,482 - star_selector - INFO - 质量筛选详细信息:
2025-05-20 09:36:14,482 - star_selector - INFO -   视差信噪比阈值: 5.0
2025-05-20 09:36:14,482 - star_selector - INFO -   RUWE阈值: 1.4
2025-05-20 09:36:14,487 - star_selector - INFO -   视差信噪比筛选通过率: 23.9%
2025-05-20 09:36:14,488 - star_selector - INFO -   RUWE筛选通过率: 118.5%
2025-05-20 09:36:14,489 - star_selector - INFO -   总体通过率: 18.7%
2025-05-20 09:36:14,489 - star_selector - INFO - 质量筛选完成，保留3716/19875个源
2025-05-20 09:36:14,489 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G000.003+00.127_filtered_data.fits
2025-05-20 09:36:15,309 - data_manager - WARNING - 检测到可能导致保存问题的列: ['TYC2', 'region']
2025-05-20 09:36:15,329 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:36:15,482 - data_manager - INFO - 成功保存数据
2025-05-20 09:36:15,483 - main_single_folder - INFO - 保存质量筛选后的数据到: results/batch_single_folder\processed\G000.003+00.127_filtered_data.fits
2025-05-20 09:36:15,489 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-05-20 09:36:15,489 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-05-20 09:36:15,489 - star_selector - INFO - 筛选恒星样本，原始样本大小: 3716
2025-05-20 09:36:15,490 - star_selector - INFO - 排除YSO后: 3716/3716个源
2025-05-20 09:36:15,504 - star_selector - INFO - 区域 cavity: 0个源
2025-05-20 09:36:15,504 - star_selector - INFO - 区域 pdr: 13个源
2025-05-20 09:36:15,504 - star_selector - INFO - 区域 external: 1个源
2025-05-20 09:36:15,504 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 3716
2025-05-20 09:36:15,505 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G000.003+00.127_star_sample.fits
2025-05-20 09:36:16,361 - data_manager - WARNING - 检测到可能导致保存问题的列: ['TYC2', 'region']
2025-05-20 09:36:16,381 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:36:16,521 - data_manager - INFO - 成功保存数据
2025-05-20 09:36:16,522 - main_single_folder - INFO - 恒星选择完成
2025-05-20 09:36:16,522 - main_single_folder - INFO - 步骤4：消光计算
2025-05-20 09:36:16,527 - extinction_estimator - INFO - 为3716个恒星计算A_V，参数: {'rv': 3.1}
2025-05-20 09:36:16,547 - extinction_estimator - INFO - 使用2MASS H-K颜色计算A_V
2025-05-20 09:36:16,547 - extinction_estimator - INFO - 估计本征H-K颜色，方法: select_giants
2025-05-20 09:36:16,547 - extinction_estimator - INFO - 使用红巨星的典型H-K颜色: 0.15
2025-05-20 09:36:16,548 - extinction_estimator - INFO - 基于H-K颜色过量计算A_V，R_V=3.1
2025-05-20 09:36:16,549 - extinction_estimator - INFO - 计算得到3320个A_V值，范围: 0.00-59.66
2025-05-20 09:36:16,549 - extinction_estimator - INFO - 使用Gaia BP-RP颜色计算A_V
2025-05-20 09:36:16,550 - extinction_estimator - INFO - 估计本征BP-RP颜色，方法: fixed_value
2025-05-20 09:36:16,550 - extinction_estimator - INFO - 使用固定的BP-RP颜色: 0.8
2025-05-20 09:36:16,550 - extinction_estimator - INFO - 基于Gaia BP-RP颜色过量计算A_V，R_V=3.1
2025-05-20 09:36:16,551 - extinction_estimator - INFO - 计算得到3676个A_V值，范围: 0.00-10.13
2025-05-20 09:36:16,553 - extinction_estimator - INFO - 方法 2MASS_HK: 3320个源
2025-05-20 09:36:16,553 - extinction_estimator - INFO - 方法 Gaia_BPRP: 370个源
2025-05-20 09:36:16,553 - extinction_estimator - INFO - 方法 unknown: 26个源
2025-05-20 09:36:16,555 - extinction_estimator - INFO - A_V计算完成，统计信息:
2025-05-20 09:36:16,555 - extinction_estimator - INFO -   范围: 0.00-59.66 mag
2025-05-20 09:36:16,556 - extinction_estimator - INFO -   均值: 12.84 mag
2025-05-20 09:36:16,556 - extinction_estimator - INFO -   中位数: 12.85 mag
2025-05-20 09:36:16,556 - extinction_estimator - INFO -   标准差: 10.21 mag
2025-05-20 09:36:16,557 - extinction_estimator - INFO - 消光计算方法统计:
2025-05-20 09:36:16,557 - extinction_estimator - INFO -   2MASS_HK: 3320颗恒星 (89.3%)
2025-05-20 09:36:16,557 - extinction_estimator - INFO -   Gaia_BPRP: 370颗恒星 (10.0%)
2025-05-20 09:36:16,557 - extinction_estimator - INFO -   unknown: 26颗恒星 (0.7%)
2025-05-20 09:36:16,558 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G000.003+00.127_stars_with_av.fits
2025-05-20 09:36:17,392 - data_manager - WARNING - 检测到可能导致保存问题的列: ['TYC2', 'region', 'av_method']
2025-05-20 09:36:17,412 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:36:17,556 - data_manager - INFO - 成功保存数据
2025-05-20 09:36:17,556 - main_single_folder - INFO - 消光计算完成
2025-05-20 09:36:17,556 - main_single_folder - INFO - 步骤5：距离分析
2025-05-20 09:36:17,556 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-05-20 09:36:17,557 - distance_analyzer - INFO - 将分析2个区域: external, pdr
2025-05-20 09:36:17,562 - distance_analyzer - INFO - 分析external区域的消光-距离关系
2025-05-20 09:36:17,562 - distance_analyzer - INFO - 准备距离-消光数据，区域: external
2025-05-20 09:36:17,562 - distance_analyzer - INFO - 区域external中有1个有效源
2025-05-20 09:36:17,562 - distance_analyzer - INFO - external区域统计信息:
2025-05-20 09:36:17,563 - distance_analyzer - INFO -   总恒星数: 3716
2025-05-20 09:36:17,563 - distance_analyzer - INFO -   区域内恒星数: 1 (0.0% 的总数)
2025-05-20 09:36:17,563 - distance_analyzer - INFO -   有效数据点: 1 (100.0% 的区域内恒星)
2025-05-20 09:36:17,563 - distance_analyzer - WARNING - external区域有效数据点太少（1 < 10），跳过分析
2025-05-20 09:36:17,568 - distance_analyzer - INFO - 分析pdr区域的消光-距离关系
2025-05-20 09:36:17,568 - distance_analyzer - INFO - 准备距离-消光数据，区域: pdr
2025-05-20 09:36:17,568 - distance_analyzer - INFO - 区域pdr中有13个有效源
2025-05-20 09:36:17,568 - distance_analyzer - INFO - pdr区域统计信息:
2025-05-20 09:36:17,569 - distance_analyzer - INFO -   总恒星数: 3716
2025-05-20 09:36:17,569 - distance_analyzer - INFO -   区域内恒星数: 13 (0.3% 的总数)
2025-05-20 09:36:17,569 - distance_analyzer - INFO -   有效数据点: 13 (100.0% 的区域内恒星)
2025-05-20 09:36:17,569 - distance_analyzer - INFO - 计算消光的滑动统计量，bin大小=50pc，最小恒星数=5
2025-05-20 09:36:17,572 - distance_analyzer - WARNING - 没有bin包含足够的恒星（>=5）
2025-05-20 09:36:17,572 - distance_analyzer - INFO - 检测消光跳变，最小跳变高度=0.5mag，最小显著性=2.0
2025-05-20 09:36:17,572 - distance_analyzer - INFO - 跳变检测统计:
2025-05-20 09:36:17,573 - distance_analyzer - INFO -   差分标准差: 14.5615
2025-05-20 09:36:17,573 - distance_analyzer - INFO -   最小跳变高度阈值: 0.5000
2025-05-20 09:36:17,573 - distance_analyzer - INFO -   最小显著性阈值: 2.00
2025-05-20 09:36:17,573 - distance_analyzer - INFO -   检测到的跳变数量: 0
2025-05-20 09:36:17,573 - distance_analyzer - INFO -   未检测到显著跳变
2025-05-20 09:36:17,573 - distance_analyzer - INFO - 共检测到0个消光跳变
2025-05-20 09:36:18,720 - distance_analyzer - INFO - 保存消光-距离图到: results/batch_single_folder\extinction_distance_pdr.png
2025-05-20 09:36:18,721 - distance_analyzer - INFO - pdr区域分析完成，检测到0个跳变
No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.
2025-05-20 09:36:19,482 - distance_analyzer - INFO - 保存区域比较图到: results/batch_single_folder\extinction_jumps_comparison.png
2025-05-20 09:36:19,482 - distance_analyzer - INFO - 估计分子云距离
2025-05-20 09:36:19,483 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity
2025-05-20 09:36:19,483 - distance_analyzer - WARNING - 没有检测到任何跳变，无法估计距离
2025-05-20 09:36:19,483 - main_single_folder - INFO - 距离分析完成
2025-05-20 09:36:19,484 - main_single_folder - INFO - 步骤6：可视化和报告生成
2025-05-20 09:36:19,488 - data_manager - INFO - 为源G000.003+00.127加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.003+00.127
2025-05-20 09:36:19,489 - data_manager - INFO - 目录中的所有文件: ['G000.003+00.127_ATLASGAL_870um.fits', 'G000.003+00.127_IRIS_100.fits', 'G000.003+00.127_NVSS.fits', 'G000.003+00.127_WISE_12.fits', 'G000.003+00.127_WISE_22.fits', 'G000.003+00.127_WISE_3.4.fits', 'G000.003+00.127_WISE_4.6.fits']
2025-05-20 09:36:19,489 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:36:19,489 - data_manager - INFO - 第一次匹配结果: ['G000.003+00.127_WISE_12.fits']
2025-05-20 09:36:19,489 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.003+00.127\G000.003+00.127_WISE_12.fits
2025-05-20 09:36:19,493 - data_manager - INFO - FITS数据统计: 最小值=-140.62432861328125, 最大值=10446.5400390625, 均值=3167.337890625, 中位数=2562.6845703125
2025-05-20 09:36:19,493 - data_manager - INFO - 有效数据点数量: 96116/99856 (96.25%)
2025-05-20 09:36:19,500 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (316, 316)
2025-05-20 09:36:19,518 - visualizer - INFO - 绘制WISE图像和区域掩模，输出到: results/batch_single_folder\visualizations\G000.003+00.127_wise_regions_stars.png
2025-05-20 09:36:19,633 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:36:21,573 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:36:21,582 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:36:23,641 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G000.003+00.127_wise_regions_stars.png
2025-05-20 09:36:23,642 - main_single_folder - INFO - 成功保存WISE图像和区域掩模到: results/batch_single_folder\visualizations\G000.003+00.127_wise_regions_stars.png
2025-05-20 09:36:23,642 - main_single_folder - INFO - 加载WISE多波段数据用于RGB图像生成
2025-05-20 09:36:23,642 - data_manager - INFO - 为源G000.003+00.127加载多波段WISE数据: ('3.4', '12', '22')
2025-05-20 09:36:23,647 - data_manager - INFO - 为源G000.003+00.127加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.003+00.127
2025-05-20 09:36:23,647 - data_manager - INFO - 目录中的所有文件: ['G000.003+00.127_ATLASGAL_870um.fits', 'G000.003+00.127_IRIS_100.fits', 'G000.003+00.127_NVSS.fits', 'G000.003+00.127_WISE_12.fits', 'G000.003+00.127_WISE_22.fits', 'G000.003+00.127_WISE_3.4.fits', 'G000.003+00.127_WISE_4.6.fits']
2025-05-20 09:36:23,648 - data_manager - INFO - 查找WISE 3.4μm波段的FITS文件
2025-05-20 09:36:23,648 - data_manager - INFO - 第一次匹配结果: ['G000.003+00.127_WISE_3.4.fits']
2025-05-20 09:36:23,648 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.003+00.127\G000.003+00.127_WISE_3.4.fits
2025-05-20 09:36:23,653 - data_manager - INFO - FITS数据统计: 最小值=11.908340454101562, 最大值=4521.4091796875, 均值=809.6646118164062, 中位数=683.5828857421875
2025-05-20 09:36:23,653 - data_manager - INFO - 有效数据点数量: 113531/113569 (99.97%)
2025-05-20 09:36:23,660 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (337, 337)
2025-05-20 09:36:23,661 - data_manager - INFO - 使用3.4μm波段的WCS作为参考
2025-05-20 09:36:23,661 - data_manager - INFO - 成功加载WISE 3.4μm波段数据，尺寸: (337, 337)
2025-05-20 09:36:23,666 - data_manager - INFO - 为源G000.003+00.127加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.003+00.127
2025-05-20 09:36:23,666 - data_manager - INFO - 目录中的所有文件: ['G000.003+00.127_ATLASGAL_870um.fits', 'G000.003+00.127_IRIS_100.fits', 'G000.003+00.127_NVSS.fits', 'G000.003+00.127_WISE_12.fits', 'G000.003+00.127_WISE_22.fits', 'G000.003+00.127_WISE_3.4.fits', 'G000.003+00.127_WISE_4.6.fits']
2025-05-20 09:36:23,666 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:36:23,667 - data_manager - INFO - 第一次匹配结果: ['G000.003+00.127_WISE_12.fits']
2025-05-20 09:36:23,667 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.003+00.127\G000.003+00.127_WISE_12.fits
2025-05-20 09:36:23,670 - data_manager - INFO - FITS数据统计: 最小值=-140.62432861328125, 最大值=10446.5400390625, 均值=3167.337890625, 中位数=2562.6845703125
2025-05-20 09:36:23,671 - data_manager - INFO - 有效数据点数量: 96116/99856 (96.25%)
2025-05-20 09:36:23,678 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (316, 316)
2025-05-20 09:36:23,678 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (316, 316)
2025-05-20 09:36:23,684 - data_manager - INFO - 为源G000.003+00.127加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.003+00.127
2025-05-20 09:36:23,684 - data_manager - INFO - 目录中的所有文件: ['G000.003+00.127_ATLASGAL_870um.fits', 'G000.003+00.127_IRIS_100.fits', 'G000.003+00.127_NVSS.fits', 'G000.003+00.127_WISE_12.fits', 'G000.003+00.127_WISE_22.fits', 'G000.003+00.127_WISE_3.4.fits', 'G000.003+00.127_WISE_4.6.fits']
2025-05-20 09:36:23,684 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:36:23,684 - data_manager - INFO - 第一次匹配结果: ['G000.003+00.127_WISE_22.fits']
2025-05-20 09:36:23,684 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.003+00.127\G000.003+00.127_WISE_22.fits
2025-05-20 09:36:23,687 - data_manager - INFO - FITS数据统计: 最小值=364.5624694824219, 最大值=3002.782958984375, 均值=650.20068359375, 中位数=427.20440673828125
2025-05-20 09:36:23,687 - data_manager - INFO - 有效数据点数量: 27844/29241 (95.22%)
2025-05-20 09:36:23,694 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (171, 171)
2025-05-20 09:36:23,695 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (171, 171)
2025-05-20 09:36:23,695 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w1', 'w3', 'w4']
2025-05-20 09:36:23,695 - visualizer - INFO - 创建WISE三波段RGB图像，输出到: results/batch_single_folder\visualizations\G000.003+00.127_wise_rgb.png
2025-05-20 09:36:23,823 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:36:23,824 - visualizer - INFO - 波段 3.4μm 的尺寸: (337, 337)
2025-05-20 09:36:23,824 - visualizer - INFO - 波段 12μm 的尺寸: (316, 316)
2025-05-20 09:36:23,824 - visualizer - INFO - 波段 22μm 的尺寸: (171, 171)
2025-05-20 09:36:23,824 - visualizer - INFO - Using 12μm band size as target: (316, 316)
2025-05-20 09:36:23,824 - visualizer - INFO - Using target image size: (316, 316), original sizes: {'w1': (337, 337), 'w3': (316, 316), 'w4': (171, 171)}
2025-05-20 09:36:23,838 - visualizer - INFO - 调整w4波段数据从(171, 171)到(316, 316)
2025-05-20 09:36:23,838 - visualizer - INFO - 红色通道(22μm)数据范围: 1.692727238378211e-07-3002.782958984375
2025-05-20 09:36:23,840 - visualizer - INFO - 绿色通道(12μm)数据范围: 30.70025634765625-10446.5400390625
2025-05-20 09:36:23,858 - visualizer - INFO - 调整w1波段数据从(337, 337)到(316, 316)
2025-05-20 09:36:23,859 - visualizer - INFO - 蓝色通道(3.4μm)数据范围: 8.218746185302734-4521.4091796875
2025-05-20 09:36:23,862 - visualizer - INFO - 使用Lupton RGB方法，stretch=4440.55, Q=10
2025-05-20 09:36:23,863 - visualizer - INFO - 各波段99.5%百分位数: R=2411.06, G=8881.10, B=2796.70
2025-05-20 09:36:23,876 - visualizer - INFO - RGB数据形状: (316, 316, 3), 类型: float32
2025-05-20 09:36:23,876 - visualizer - INFO - Red通道数据范围：0.0-0.4745098054409027，均值：0.0822
2025-05-20 09:36:23,877 - visualizer - INFO - Green通道数据范围：0.0-0.8705882430076599，均值：0.4170
2025-05-20 09:36:23,878 - visualizer - INFO - Blue通道数据范围：0.0-0.658823549747467，均值：0.1153
2025-05-20 09:36:25,316 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:36:25,322 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:36:25,344 - visualizer - INFO - 按照要求不显示Gaia星
2025-05-20 09:36:25,345 - visualizer - INFO - 设置坐标刻度间隔: RA=10.000度, Dec=10.000度
2025-05-20 09:36:26,322 - visualizer - INFO - 成功保存RGB图像到: results/batch_single_folder\visualizations\G000.003+00.127_wise_rgb.png
2025-05-20 09:36:26,323 - main_single_folder - INFO - 成功保存WISE RGB图像到: results/batch_single_folder\visualizations\G000.003+00.127_wise_rgb.png
2025-05-20 09:36:26,323 - visualizer - INFO - 绘制消光-距离散点图，输出到: results/batch_single_folder\visualizations\G000.003+00.127_extinction_distance.png
2025-05-20 09:36:28,664 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G000.003+00.127_extinction_distance.png
2025-05-20 09:36:28,666 - main_single_folder - INFO - 成功保存消光-距离散点图到: results/batch_single_folder\visualizations\G000.003+00.127_extinction_distance.png
2025-05-20 09:36:28,752 - main_single_folder - INFO - 成功保存处理报告到: results/batch_single_folder\reports\G000.003+00.127_report.txt
2025-05-20 09:36:28,752 - main_single_folder - INFO - 处理G000.003+00.127完成
