处理时间: 2025-04-24 04:59:22
耗时: 12.31秒

返回码: 1

标准输出:
错误: zero-size array to reduction operation minimum which has no identity


标准错误:
2025-04-24 04:59:13,661 - main - INFO - 输出目录: data/output/batch_results\G000.120-00.556
2025-04-24 04:59:13,661 - main - INFO - 加载源G000.120-00.556的信息
2025-04-24 04:59:13,661 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
H:\Augment\Parallax distances\src\data_manager.py:43: FutureWarning: The 'delim_whitespace' keyword in pd.read_csv is deprecated and will be removed in a future version. Use ``sep='\s+'`` instead
  df = pd.read_csv(catalog_path, delim_whitespace=True, comment='#', header=None, names=column_names)
2025-04-24 04:59:13,667 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-04-24 04:59:13,668 - main - INFO - 目录中的所有文件: ['G000.120-00.556_ATLASGAL_870um.fits', 'G000.120-00.556_IRIS_100.fits', 'G000.120-00.556_NVSS.fits', 'G000.120-00.556_WISE_12.fits', 'G000.120-00.556_WISE_22.fits', 'G000.120-00.556_WISE_3.4.fits', 'G000.120-00.556_WISE_4.6.fits']
2025-04-24 04:59:13,668 - main - INFO - 第一次匹配结果: ['G000.120-00.556_WISE_12.fits']
2025-04-24 04:59:13,668 - main - INFO - 从FITS文件获取坐标: U:/Data/Bubbles/Wise bubbles\G000.120-00.556\G000.120-00.556_WISE_12.fits
2025-04-24 04:59:13,671 - main - INFO - 从FITS头信息获取坐标系统: fk5
2025-04-24 04:59:13,674 - main - INFO - 从FITS头信息获取坐标: RA=267.019992, Dec=-29.122005 (ICRS)
2025-04-24 04:59:13,674 - main - INFO - 使用有效半径360.0角秒 (0.100000度)
2025-04-24 04:59:13,675 - main - INFO - 从源表加载信息: RA=267.019992, Dec=-29.122005, R_eff=0.100000度
2025-04-24 04:59:13,675 - main - INFO - 步骤1：加载WISE数据
2025-04-24 04:59:13,679 - data_manager - INFO - 为源G000.120-00.556加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.120-00.556
2025-04-24 04:59:13,679 - data_manager - INFO - 目录中的所有文件: ['G000.120-00.556_ATLASGAL_870um.fits', 'G000.120-00.556_IRIS_100.fits', 'G000.120-00.556_NVSS.fits', 'G000.120-00.556_WISE_12.fits', 'G000.120-00.556_WISE_22.fits', 'G000.120-00.556_WISE_3.4.fits', 'G000.120-00.556_WISE_4.6.fits']
2025-04-24 04:59:13,679 - data_manager - INFO - 第一次匹配结果: ['G000.120-00.556_WISE_12.fits']
2025-04-24 04:59:13,680 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.120-00.556\G000.120-00.556_WISE_12.fits
2025-04-24 04:59:13,689 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (553, 553)
2025-04-24 04:59:13,690 - main - INFO - WISE数据加载完成
2025-04-24 04:59:13,690 - main - INFO - 步骤2：区域映射
2025-04-24 04:59:13,690 - region_mapper - INFO - 处理WISE图像，平滑sigma=1.0
2025-04-24 04:59:13,692 - region_mapper - INFO - 替换图像中的NaN值
2025-04-24 04:59:13,695 - region_mapper - INFO - 应用高斯平滑
2025-04-24 04:59:13,704 - region_mapper - INFO - 估计并减除背景
2025-04-24 04:59:13,721 - region_mapper - INFO - WISE图像处理完成
2025-04-24 04:59:13,722 - region_mapper - INFO - 定义PDR掩模，阈值因子=0.5，最大半径因子=3.0
2025-04-24 04:59:13,722 - region_mapper - INFO - WCS坐标系统: fk5
2025-04-24 04:59:13,722 - region_mapper - INFO - 将中心坐标从ICRS转换为fk5
2025-04-24 04:59:13,727 - region_mapper - INFO - 中心坐标 (RA=267.019992, Dec=-29.122005) 对应像素坐标 (X=276.0, Y=276.0)
2025-04-24 04:59:13,732 - region_mapper - INFO - 像素尺度: 483.09 像素/角秒
2025-04-24 04:59:13,732 - region_mapper - INFO - 最大搜索半径: 2.2 像素
2025-04-24 04:59:13,736 - region_mapper - INFO - PDR阈值: 0.00 (峰值的50%)
2025-04-24 04:59:13,785 - region_mapper - INFO - PDR掩模创建完成，覆盖13个像素
2025-04-24 04:59:13,786 - region_mapper - INFO - 定义空腔掩模，有效半径=0.1度
2025-04-24 04:59:13,793 - region_mapper - INFO - 有效半径: 0.7 像素
2025-04-24 04:59:13,797 - region_mapper - INFO - 空腔掩模创建完成，覆盖0个像素
2025-04-24 04:59:13,798 - region_mapper - INFO - 定义外部区域掩模，最大半径因子=5.0
2025-04-24 04:59:13,805 - region_mapper - INFO - 最大半径: 3.7 像素
2025-04-24 04:59:13,808 - region_mapper - INFO - 外部区域掩模创建完成，覆盖32个像素
2025-04-24 04:59:13,809 - region_mapper - INFO - 保存区域掩模到: data/output/batch_results\G000.120-00.556\processed\G000.120-00.556_region_masks.npz
2025-04-24 04:59:13,818 - region_mapper - INFO - 成功保存区域掩模
2025-04-24 04:59:13,821 - data_manager - INFO - 为源G000.120-00.556加载Gaia数据，中心坐标: RA=267.019992, Dec=-29.122005, 半径=0.5000度
2025-04-24 04:59:13,824 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances/gaia_data\gaia_G000.120-00.556_30arcmin_5R.csv
H:\Augment\Parallax distances\src\data_manager.py:87: DtypeWarning: Columns (47,52,54) have mixed types. Specify dtype option on import or set low_memory=False.
  df = pd.read_csv(gaia_file)
2025-04-24 04:59:16,552 - data_manager - INFO - 成功加载Gaia数据，共176254条记录
2025-04-24 04:59:16,735 - data_manager - INFO - 成功加载Gaia数据，共176231个源在搜索半径内
2025-04-24 04:59:16,806 - region_mapper - INFO - 为176231个恒星分配区域标签
2025-04-24 04:59:16,806 - region_mapper - INFO - WCS坐标系统: fk5
2025-04-24 04:59:16,807 - region_mapper - INFO - 将恒星坐标从ICRS转换为fk5
2025-04-24 04:59:17,886 - region_mapper - INFO - 区域标签分配完成: 空腔=0, PDR=6, 外部=23, 区域外=176202
2025-04-24 04:59:17,889 - data_manager - INFO - 保存处理后的数据到: data/output/batch_results\G000.120-00.556\processed\G000.120-00.556_gaia_regions.fits
2025-04-24 04:59:21,695 - data_manager - ERROR - 保存数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U11'), dtype('<U1'), dtype('<U10'), dtype('<U9')}
2025-04-24 04:59:21,696 - main - ERROR - 加载Gaia数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U11'), dtype('<U1'), dtype('<U10'), dtype('<U9')}
2025-04-24 04:59:21,696 - main - WARNING - 将使用空的Gaia数据继续处理
2025-04-24 04:59:21,700 - main - WARNING - 区域映射完成，但没有Gaia数据
2025-04-24 04:59:21,808 - main - INFO - 步骤3：恒星选择
2025-04-24 04:59:21,812 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-04-24 04:59:21,813 - star_selector - WARNING - 没有有效的视差数据，跳过视差信噪比筛选
2025-04-24 04:59:21,813 - star_selector - WARNING - 没有有效的RUWE数据，跳过RUWE筛选
2025-04-24 04:59:21,814 - star_selector - INFO - 质量筛选完成，保留0/0个源
2025-04-24 04:59:21,817 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-04-24 04:59:21,817 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-04-24 04:59:21,817 - star_selector - INFO - 筛选恒星样本，原始样本大小: 0
2025-04-24 04:59:21,818 - star_selector - INFO - 排除YSO后: 0/0个源
2025-04-24 04:59:21,818 - star_selector - INFO - 区域 cavity: 0个源
2025-04-24 04:59:21,818 - star_selector - INFO - 区域 pdr: 0个源
2025-04-24 04:59:21,819 - star_selector - INFO - 区域 external: 0个源
2025-04-24 04:59:21,819 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 0
2025-04-24 04:59:21,819 - data_manager - INFO - 保存处理后的数据到: data/output/batch_results\G000.120-00.556\processed\G000.120-00.556_star_sample.fits
2025-04-24 04:59:21,844 - data_manager - INFO - 成功保存数据
2025-04-24 04:59:21,844 - main - INFO - 恒星选择完成
2025-04-24 04:59:21,844 - main - INFO - 步骤4：消光计算
2025-04-24 04:59:21,847 - extinction_estimator - INFO - 为0个恒星计算A_V，参数: {'rv': 3.1}
2025-04-24 04:59:21,849 - extinction_estimator - ERROR - 计算A_V失败: zero-size array to reduction operation minimum which has no identity
2025-04-24 04:59:21,849 - main - ERROR - 处理失败: zero-size array to reduction operation minimum which has no identity
Traceback (most recent call last):
  File "H:\Augment\Parallax distances\main.py", line 459, in main
    results = process_hii_region(args)
              ^^^^^^^^^^^^^^^^^^^^^^^^
  File "H:\Augment\Parallax distances\main.py", line 388, in process_hii_region
    stars_with_av = compute_av_per_star(star_sample)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "H:\Augment\Parallax distances\src\extinction_estimator.py", line 294, in compute_av_per_star
    logger.info(f"A_V计算完成，范围: {np.min(av_values):.2f}-{np.max(av_values):.2f}")
                                      ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\_core\fromnumeric.py", line 3343, in min
    return _wrapreduction(a, np.minimum, 'min', axis, None, out,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\_core\fromnumeric.py", line 86, in _wrapreduction
    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: zero-size array to reduction operation minimum which has no identity
