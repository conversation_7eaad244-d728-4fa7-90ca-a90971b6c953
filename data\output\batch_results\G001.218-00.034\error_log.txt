处理时间: 2025-04-24 05:00:15
耗时: 26.92秒

返回码: 1

标准输出:
错误: zero-size array to reduction operation minimum which has no identity


标准错误:
2025-04-24 04:59:52,244 - main - INFO - 输出目录: data/output/batch_results\G001.218-00.034
2025-04-24 04:59:52,244 - main - INFO - 加载源G001.218-00.034的信息
2025-04-24 04:59:52,244 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
H:\Augment\Parallax distances\src\data_manager.py:43: FutureWarning: The 'delim_whitespace' keyword in pd.read_csv is deprecated and will be removed in a future version. Use ``sep='\s+'`` instead
  df = pd.read_csv(catalog_path, delim_whitespace=True, comment='#', header=None, names=column_names)
2025-04-24 04:59:52,250 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-04-24 04:59:52,251 - main - INFO - 目录中的所有文件: ['G001.218-00.034_ATLASGAL_870um.fits', 'G001.218-00.034_IRIS_100.fits', 'G001.218-00.034_NVSS.fits', 'G001.218-00.034_WISE_12.fits', 'G001.218-00.034_WISE_22.fits', 'G001.218-00.034_WISE_3.4.fits', 'G001.218-00.034_WISE_4.6.fits']
2025-04-24 04:59:52,252 - main - INFO - 第一次匹配结果: ['G001.218-00.034_WISE_12.fits']
2025-04-24 04:59:52,252 - main - INFO - 从FITS文件获取坐标: U:/Data/Bubbles/Wise bubbles\G001.218-00.034\G001.218-00.034_WISE_12.fits
2025-04-24 04:59:52,254 - main - INFO - 从FITS头信息获取坐标系统: fk5
2025-04-24 04:59:52,257 - main - INFO - 从FITS头信息获取坐标: RA=267.156992, Dec=-27.911305 (ICRS)
2025-04-24 04:59:52,257 - main - INFO - 使用有效半径611.0角秒 (0.169722度)
2025-04-24 04:59:52,258 - main - INFO - 从源表加载信息: RA=267.156992, Dec=-27.911305, R_eff=0.169722度
2025-04-24 04:59:52,258 - main - INFO - 步骤1：加载WISE数据
2025-04-24 04:59:52,262 - data_manager - INFO - 为源G001.218-00.034加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G001.218-00.034
2025-04-24 04:59:52,262 - data_manager - INFO - 目录中的所有文件: ['G001.218-00.034_ATLASGAL_870um.fits', 'G001.218-00.034_IRIS_100.fits', 'G001.218-00.034_NVSS.fits', 'G001.218-00.034_WISE_12.fits', 'G001.218-00.034_WISE_22.fits', 'G001.218-00.034_WISE_3.4.fits', 'G001.218-00.034_WISE_4.6.fits']
2025-04-24 04:59:52,263 - data_manager - INFO - 第一次匹配结果: ['G001.218-00.034_WISE_12.fits']
2025-04-24 04:59:52,263 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G001.218-00.034\G001.218-00.034_WISE_12.fits
2025-04-24 04:59:52,273 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (940, 940)
2025-04-24 04:59:52,273 - main - INFO - WISE数据加载完成
2025-04-24 04:59:52,273 - main - INFO - 步骤2：区域映射
2025-04-24 04:59:52,273 - region_mapper - INFO - 处理WISE图像，平滑sigma=1.0
2025-04-24 04:59:52,279 - region_mapper - INFO - 替换图像中的NaN值
2025-04-24 04:59:52,289 - region_mapper - INFO - 应用高斯平滑
2025-04-24 04:59:52,309 - region_mapper - INFO - 估计并减除背景
2025-04-24 04:59:52,335 - region_mapper - INFO - WISE图像处理完成
2025-04-24 04:59:52,336 - region_mapper - INFO - 定义PDR掩模，阈值因子=0.5，最大半径因子=3.0
2025-04-24 04:59:52,336 - region_mapper - INFO - WCS坐标系统: fk5
2025-04-24 04:59:52,337 - region_mapper - INFO - 将中心坐标从ICRS转换为fk5
2025-04-24 04:59:52,342 - region_mapper - INFO - 中心坐标 (RA=267.156992, Dec=-27.911305) 对应像素坐标 (X=469.5, Y=469.5)
2025-04-24 04:59:52,346 - region_mapper - INFO - 像素尺度: 489.42 像素/角秒
2025-04-24 04:59:52,346 - region_mapper - INFO - 最大搜索半径: 3.7 像素
2025-04-24 04:59:52,358 - region_mapper - INFO - PDR阈值: 0.00 (峰值的50%)
2025-04-24 04:59:52,478 - region_mapper - INFO - PDR掩模创建完成，覆盖44个像素
2025-04-24 04:59:52,480 - region_mapper - INFO - 定义空腔掩模，有效半径=0.16972222222222222度
2025-04-24 04:59:52,487 - region_mapper - INFO - 有效半径: 1.2 像素
2025-04-24 04:59:52,499 - region_mapper - INFO - 空腔掩模创建完成，覆盖0个像素
2025-04-24 04:59:52,500 - region_mapper - INFO - 定义外部区域掩模，最大半径因子=5.0
2025-04-24 04:59:52,507 - region_mapper - INFO - 最大半径: 6.2 像素
2025-04-24 04:59:52,519 - region_mapper - INFO - 外部区域掩模创建完成，覆盖76个像素
2025-04-24 04:59:52,520 - region_mapper - INFO - 保存区域掩模到: data/output/batch_results\G001.218-00.034\processed\G001.218-00.034_region_masks.npz
2025-04-24 04:59:52,542 - region_mapper - INFO - 成功保存区域掩模
2025-04-24 04:59:52,546 - data_manager - INFO - 为源G001.218-00.034加载Gaia数据，中心坐标: RA=267.156992, Dec=-27.911305, 半径=0.8486度
2025-04-24 04:59:52,549 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances/gaia_data\gaia_G001.218-00.034_51arcmin_5R.csv
H:\Augment\Parallax distances\src\data_manager.py:87: DtypeWarning: Columns (47,52,54) have mixed types. Specify dtype option on import or set low_memory=False.
  df = pd.read_csv(gaia_file)
2025-04-24 04:59:59,905 - data_manager - INFO - 成功加载Gaia数据，共492671条记录
2025-04-24 05:00:00,375 - data_manager - INFO - 成功加载Gaia数据，共492370个源在搜索半径内
2025-04-24 05:00:00,504 - region_mapper - INFO - 为492370个恒星分配区域标签
2025-04-24 05:00:00,504 - region_mapper - INFO - WCS坐标系统: fk5
2025-04-24 05:00:00,504 - region_mapper - INFO - 将恒星坐标从ICRS转换为fk5
2025-04-24 05:00:03,400 - region_mapper - INFO - 区域标签分配完成: 空腔=0, PDR=39, 外部=53, 区域外=492278
2025-04-24 05:00:03,408 - data_manager - INFO - 保存处理后的数据到: data/output/batch_results\G001.218-00.034\processed\G001.218-00.034_gaia_regions.fits
2025-04-24 05:00:14,756 - data_manager - ERROR - 保存数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U11'), dtype('<U1'), dtype('<U10'), dtype('<U9'), dtype('<U8')}
2025-04-24 05:00:14,756 - main - ERROR - 加载Gaia数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U11'), dtype('<U1'), dtype('<U10'), dtype('<U9'), dtype('<U8')}
2025-04-24 05:00:14,756 - main - WARNING - 将使用空的Gaia数据继续处理
2025-04-24 05:00:14,760 - main - WARNING - 区域映射完成，但没有Gaia数据
2025-04-24 05:00:15,033 - main - INFO - 步骤3：恒星选择
2025-04-24 05:00:15,036 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-04-24 05:00:15,037 - star_selector - WARNING - 没有有效的视差数据，跳过视差信噪比筛选
2025-04-24 05:00:15,037 - star_selector - WARNING - 没有有效的RUWE数据，跳过RUWE筛选
2025-04-24 05:00:15,038 - star_selector - INFO - 质量筛选完成，保留0/0个源
2025-04-24 05:00:15,041 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-04-24 05:00:15,041 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-04-24 05:00:15,041 - star_selector - INFO - 筛选恒星样本，原始样本大小: 0
2025-04-24 05:00:15,041 - star_selector - INFO - 排除YSO后: 0/0个源
2025-04-24 05:00:15,042 - star_selector - INFO - 区域 cavity: 0个源
2025-04-24 05:00:15,042 - star_selector - INFO - 区域 pdr: 0个源
2025-04-24 05:00:15,043 - star_selector - INFO - 区域 external: 0个源
2025-04-24 05:00:15,043 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 0
2025-04-24 05:00:15,043 - data_manager - INFO - 保存处理后的数据到: data/output/batch_results\G001.218-00.034\processed\G001.218-00.034_star_sample.fits
2025-04-24 05:00:15,067 - data_manager - INFO - 成功保存数据
2025-04-24 05:00:15,068 - main - INFO - 恒星选择完成
2025-04-24 05:00:15,068 - main - INFO - 步骤4：消光计算
2025-04-24 05:00:15,072 - extinction_estimator - INFO - 为0个恒星计算A_V，参数: {'rv': 3.1}
2025-04-24 05:00:15,073 - extinction_estimator - ERROR - 计算A_V失败: zero-size array to reduction operation minimum which has no identity
2025-04-24 05:00:15,074 - main - ERROR - 处理失败: zero-size array to reduction operation minimum which has no identity
Traceback (most recent call last):
  File "H:\Augment\Parallax distances\main.py", line 459, in main
    results = process_hii_region(args)
              ^^^^^^^^^^^^^^^^^^^^^^^^
  File "H:\Augment\Parallax distances\main.py", line 388, in process_hii_region
    stars_with_av = compute_av_per_star(star_sample)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "H:\Augment\Parallax distances\src\extinction_estimator.py", line 294, in compute_av_per_star
    logger.info(f"A_V计算完成，范围: {np.min(av_values):.2f}-{np.max(av_values):.2f}")
                                      ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\_core\fromnumeric.py", line 3343, in min
    return _wrapreduction(a, np.minimum, 'min', axis, None, out,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\numpy\_core\fromnumeric.py", line 86, in _wrapreduction
    return ufunc.reduce(obj, axis, dtype, out, **passkwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: zero-size array to reduction operation minimum which has no identity
