"""
测试前5个HII区域源的处理脚本
"""

import os
import sys
import subprocess
import time

def run_source(source_name):
    """运行单个源的处理"""
    print(f"\n{'='*50}")
    print(f"处理源: {source_name}")
    print(f"{'='*50}\n")
    
    cmd = f"python main.py --source {source_name} --config config/default_config.yaml"
    
    start_time = time.time()
    process = subprocess.Popen(cmd, shell=True)
    process.wait()
    end_time = time.time()
    
    elapsed = end_time - start_time
    print(f"\n处理完成，耗时: {elapsed:.2f}秒")
    
    return process.returncode == 0

def main():
    """主函数"""
    # 前5个源
    sources = [
        "G000.003+00.127",
        "G000.120-00.556",
        "G000.121-00.304",
        "G000.138-00.115",
        "G000.484-00.900"
    ]
    
    success_count = 0
    failed_sources = []
    
    for source in sources:
        success = run_source(source)
        if success:
            success_count += 1
        else:
            failed_sources.append(source)
    
    print(f"\n{'='*50}")
    print(f"测试完成: {success_count}/{len(sources)} 个源处理成功")
    
    if failed_sources:
        print(f"失败的源: {', '.join(failed_sources)}")
    
    return 0 if success_count == len(sources) else 1

if __name__ == "__main__":
    sys.exit(main())
