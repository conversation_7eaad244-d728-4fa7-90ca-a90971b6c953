处理时间: 2025-05-20 09:30:58
耗时: 7.68秒

返回码: 1

标准输出:
错误: define_cavity_mask() takes 4 positional arguments but 5 were given


标准错误:
2025-05-20 09:30:54,467 - main_single_folder - INFO - 输出目录: results/batch_single_folder
2025-05-20 09:30:54,468 - main_single_folder - INFO - 加载源G000.003+00.127的信息
2025-05-20 09:30:54,468 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
2025-05-20 09:30:54,473 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-05-20 09:30:54,474 - main_single_folder - INFO - 从源名解析银道坐标: l=0.003000, b=0.127000
2025-05-20 09:30:54,480 - main_single_folder - INFO - 银道坐标转换为赤道坐标: RA=266.282993, Dec=-28.867391 (ICRS)
2025-05-20 09:30:54,480 - main_single_folder - INFO - 从源表加载信息: RA=266.282993, Dec=-28.867391, R_eff=0.057222度
2025-05-20 09:30:54,481 - main_single_folder - INFO - 步骤1：加载WISE数据
2025-05-20 09:30:54,485 - data_manager - INFO - 为源G000.003+00.127加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.003+00.127
2025-05-20 09:30:54,485 - data_manager - INFO - 目录中的所有文件: ['G000.003+00.127_ATLASGAL_870um.fits', 'G000.003+00.127_IRIS_100.fits', 'G000.003+00.127_NVSS.fits', 'G000.003+00.127_WISE_12.fits', 'G000.003+00.127_WISE_22.fits', 'G000.003+00.127_WISE_3.4.fits', 'G000.003+00.127_WISE_4.6.fits']
2025-05-20 09:30:54,486 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:30:54,486 - data_manager - INFO - 第一次匹配结果: ['G000.003+00.127_WISE_12.fits']
2025-05-20 09:30:54,486 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.003+00.127\G000.003+00.127_WISE_12.fits
2025-05-20 09:30:54,490 - data_manager - INFO - FITS数据统计: 最小值=-140.62432861328125, 最大值=10446.5400390625, 均值=3167.337890625, 中位数=2562.6845703125
2025-05-20 09:30:54,490 - data_manager - INFO - 有效数据点数量: 96116/99856 (96.25%)
2025-05-20 09:30:54,498 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (316, 316)
2025-05-20 09:30:54,498 - main_single_folder - INFO - WISE数据加载完成
2025-05-20 09:30:54,498 - main_single_folder - INFO - 步骤2：区域映射
2025-05-20 09:30:54,501 - main_single_folder - INFO - 使用W3/W4波段比值方法定义PDR区域
2025-05-20 09:30:54,501 - region_mapper - INFO - 使用W3/W4波段比值方法定义PDR掩模
2025-05-20 09:30:54,501 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:30:54,501 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:30:54,502 - region_mapper.ratio - INFO - 加载源G000.003+00.127的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:30:54,502 - region_mapper.ratio - INFO - 加载源G000.003+00.127的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:30:54,502 - data_manager - INFO - 为源G000.003+00.127加载多波段WISE数据: ('12', '22')
2025-05-20 09:30:54,507 - data_manager - INFO - 为源G000.003+00.127加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.003+00.127
2025-05-20 09:30:54,507 - data_manager - INFO - 目录中的所有文件: ['G000.003+00.127_ATLASGAL_870um.fits', 'G000.003+00.127_IRIS_100.fits', 'G000.003+00.127_NVSS.fits', 'G000.003+00.127_WISE_12.fits', 'G000.003+00.127_WISE_22.fits', 'G000.003+00.127_WISE_3.4.fits', 'G000.003+00.127_WISE_4.6.fits']
2025-05-20 09:30:54,507 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:30:54,507 - data_manager - INFO - 第一次匹配结果: ['G000.003+00.127_WISE_12.fits']
2025-05-20 09:30:54,507 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.003+00.127\G000.003+00.127_WISE_12.fits
2025-05-20 09:30:54,511 - data_manager - INFO - FITS数据统计: 最小值=-140.62432861328125, 最大值=10446.5400390625, 均值=3167.337890625, 中位数=2562.6845703125
2025-05-20 09:30:54,511 - data_manager - INFO - 有效数据点数量: 96116/99856 (96.25%)
2025-05-20 09:30:54,578 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (316, 316)
2025-05-20 09:30:54,579 - data_manager - INFO - 使用12μm波段的WCS作为参考
2025-05-20 09:30:54,579 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (316, 316)
2025-05-20 09:30:54,584 - data_manager - INFO - 为源G000.003+00.127加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.003+00.127
2025-05-20 09:30:54,584 - data_manager - INFO - 目录中的所有文件: ['G000.003+00.127_ATLASGAL_870um.fits', 'G000.003+00.127_IRIS_100.fits', 'G000.003+00.127_NVSS.fits', 'G000.003+00.127_WISE_12.fits', 'G000.003+00.127_WISE_22.fits', 'G000.003+00.127_WISE_3.4.fits', 'G000.003+00.127_WISE_4.6.fits']
2025-05-20 09:30:54,584 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:30:54,585 - data_manager - INFO - 第一次匹配结果: ['G000.003+00.127_WISE_22.fits']
2025-05-20 09:30:54,585 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.003+00.127\G000.003+00.127_WISE_22.fits
2025-05-20 09:30:54,587 - data_manager - INFO - FITS数据统计: 最小值=364.5624694824219, 最大值=3002.782958984375, 均值=650.20068359375, 中位数=427.20440673828125
2025-05-20 09:30:54,588 - data_manager - INFO - 有效数据点数量: 27844/29241 (95.22%)
2025-05-20 09:30:54,595 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (171, 171)
2025-05-20 09:30:54,595 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (171, 171)
2025-05-20 09:30:54,595 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w3', 'w4']
2025-05-20 09:30:54,597 - region_mapper.ratio - INFO - 调整W4波段数据从(171, 171)到(316, 316)
2025-05-20 09:30:54,597 - region_mapper.ratio - INFO - 调整W4波段数据从(171, 171)到(316, 316)
2025-05-20 09:30:54,617 - region_mapper.ratio - INFO - W3/W4比值范围: 0.03-113.18，均值: 1463.66
2025-05-20 09:30:54,617 - region_mapper.ratio - INFO - W3/W4比值范围: 0.03-113.18，均值: 1463.66
2025-05-20 09:30:54,623 - region_mapper.ratio - INFO - 像素尺度: 483.61 像素/角秒
2025-05-20 09:30:54,623 - region_mapper.ratio - INFO - 像素尺度: 483.61 像素/角秒
2025-05-20 09:30:54,624 - region_mapper.ratio - INFO - 有效半径: 0.4 像素
2025-05-20 09:30:54,624 - region_mapper.ratio - INFO - 有效半径: 0.4 像素
2025-05-20 09:30:54,624 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:30:54,624 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:30:55,372 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.581
2025-05-20 09:30:55,372 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.581
2025-05-20 09:30:55,447 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.542
2025-05-20 09:30:55,447 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.542
2025-05-20 09:30:55,520 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.576
2025-05-20 09:30:55,520 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.576
2025-05-20 09:30:55,520 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 09:30:55,520 - region_mapper.ratio - INFO - 使用最佳聚类数: 2
2025-05-20 09:30:55,585 - region_mapper.ratio - INFO - 聚类 0: 平均比值=5.56±0.29, 大小=103, 平均距离=8.0, 紧凑性=6.3
2025-05-20 09:30:55,585 - region_mapper.ratio - INFO - 聚类 0: 平均比值=5.56±0.29, 大小=103, 平均距离=8.0, 紧凑性=6.3
2025-05-20 09:30:55,586 - region_mapper.ratio - INFO - 聚类 1: 平均比值=4.88±0.21, 大小=213, 平均距离=6.0, 紧凑性=5.7
2025-05-20 09:30:55,586 - region_mapper.ratio - INFO - 聚类 1: 平均比值=4.88±0.21, 大小=213, 平均距离=6.0, 紧凑性=5.7
2025-05-20 09:30:55,586 - region_mapper.ratio - INFO - 选择聚类 0 作为PDR区域
2025-05-20 09:30:55,586 - region_mapper.ratio - INFO - 选择聚类 0 作为PDR区域
2025-05-20 09:30:55,599 - region_mapper.ratio - INFO - PDR掩模覆盖126个像素
2025-05-20 09:30:55,599 - region_mapper.ratio - INFO - PDR掩模覆盖126个像素
2025-05-20 09:30:58,232 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G000.003+00.127_w3w4_ratio.png
2025-05-20 09:30:58,232 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G000.003+00.127_w3w4_ratio.png
2025-05-20 09:30:58,233 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖126个像素
2025-05-20 09:30:58,233 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖126个像素
2025-05-20 09:30:58,234 - main_single_folder - ERROR - 处理失败: define_cavity_mask() takes 4 positional arguments but 5 were given
Traceback (most recent call last):
  File "H:\Augment\Parallax distances\main_single_folder.py", line 757, in main
    results = process_hii_region(args)
              ^^^^^^^^^^^^^^^^^^^^^^^^
  File "H:\Augment\Parallax distances\main_single_folder.py", line 275, in process_hii_region
    cavity_mask = define_cavity_mask(
                  ^^^^^^^^^^^^^^^^^^^
TypeError: define_cavity_mask() takes 4 positional arguments but 5 were given
