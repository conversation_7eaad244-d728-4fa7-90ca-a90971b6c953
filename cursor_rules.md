# Cursor规则：Python天文数据处理规范

## 1. 代码可读性与结构
- **命名规范**
  - 使用下划线分隔的变量名（如 `stellar_magnitude`）
  - 函数名使用动词前缀（如 `calculate_redshift`, `download_gaia_data`）
  - 常量名全大写加下划线（如 `DEFAULT_RADIUS`, `BAILER_JONES_CATALOG`）
- **格式规范**
  - 每行代码不超过80字符
  - 运算符两侧和逗号后使用空格
  - 函数定义之间空两行，方法定义之间空一行
- **注释与文档**
  - 每个函数或方法必须添加文档字符串（docstring），包含功能说明、参数和返回值
  - 复杂算法需要添加详细注释说明原理和实现逻辑
  - 使用 `"""..."""` 格式化文档字符串，包含 Args 和 Returns 部分

## 2. 天文数据处理专用工具
- **必要导入**
  ```python
  import astropy.units as u
  from astropy.coordinates import SkyCoord
  from astropy.table import Table
  import numpy as np
  import matplotlib.pyplot as plt
  ```
- **数据操作规范**
  - 处理FITS文件必须使用 `astropy.io.fits` 或 `Table.read()`
  - 坐标转换必须通过 `SkyCoord` 完成，并明确指定坐标系
  - 单位转换必须使用 `astropy.units`，禁止手动计算单位换算
  - 星表数据处理优先使用 `pandas` 或 `astropy.table.Table`

## 3. 配置与参数化
- **参数管理**
  - 将配置参数集中在脚本开头，使用大写变量名定义默认值
  - 使用 `argparse` 处理命令行参数，设置合理的默认值和帮助信息
  - 避免硬编码路径和常量，使用变量或配置文件
- **脚本结构**
  - 采用三区式结构：配置声明 → 功能函数 → 主程序
  - 主程序必须包含 `if __name__ == "__main__"` 保护

## 4. 异常处理与日志
- **错误处理**
  - 文件操作必须使用 `try-except` 块包裹，捕获 `IOError` 和 `ValueError`
  - 网络请求必须设置超时并捕获连接异常
  - 使用装饰器封装重试逻辑（如 `@retry_on_network_error()`）
- **日志记录**
  - 使用 `logging` 模块替代 `print`，按级别记录信息
  - 记录关键操作和异常情况，提供足够的错误诊断信息
  - 长时间运行的脚本应当周期性输出进度信息

## 5. 性能优化
- **向量化与并行**
  - 避免Python循环，使用NumPy向量化操作
  - 处理大型数据集时使用分块读取或并行处理
  - 使用 `@functools.lru_cache` 缓存重复计算结果
- **资源管理**
  - 及时释放不再使用的大型数据对象（`del variable`）
  - 禁止在循环中重复执行相同的天文计算，应缓存结果
  - 处理大规模数据时，使用 `astropy.table` 的分块读取或 `dask` 并行处理

## 6. 天文学特定规范
- **坐标与单位**
  - 处理天文坐标时必须指定坐标系统（如 'icrs', 'galactic'）
  - 所有物理量必须带单位，使用 `astropy.units`
  - 时间处理使用 `astropy.time`，支持不同时间系统转换
- **数据格式**
  - FITS文件操作使用 `astropy.io.fits`
  - VO Table或星表数据使用 `astropy.table`
  - 数据验证必须检查坐标有效性和物理量合理性

## 7. 可视化规范
- **绘图标准**
  - 使用 `matplotlib` + `astropy.visualization` 绘制专业图表
  - 添加坐标轴标签、色标、比例尺等元数据
  - 绘图函数需要参数化控制样式、大小、分辨率等
- **输出格式**
  - 提供高分辨率图表导出选项（至少300dpi）
  - 支持多种输出格式：PNG（屏幕查看）、PDF/EPS（出版）
  - 图表文件命名应包含数据源和处理方法信息

## 8. 版本控制与测试
- **代码管理**
  - 使用 `.gitignore` 排除大型数据文件和缓存
  - 提交清晰的commit消息，说明改动内容和目的
- **测试规范**
  - 编写单元测试验证核心功能，尤其是坐标转换和距离计算
  - 提供小型测试数据集，便于验证脚本功能
  - 新增功能通过参数开关控制（默认关闭） 

---
# Always respond in 中文
# 文件审阅优先
在执行任务前，必须仔细审阅提供的全部文件或资料，并提取关键信息，确保理解任务背景与要求。
# 信息验证原则
回答问题或执行任务前，必须优先搜索可靠来源（如文档、数据库、官方资料等），严禁编造答案或虚构结论，所有内容需基于可验证的信息。
# 聚焦任务核心
严格围绕用户明确提出的任务展开，避免偏离主题或引入无关内容，不得添加主观推测、个人意见或未经验证的假设。
# 正确使用win10的系统工具完成任务
# 更新readme，每次完成脚本修改后