"""
区域映射模块

负责处理WISE图像并定义HII区域的不同区域（空腔、PDR、外部）。
此文件为兼容性保留，实际功能已移至src/region_mapper/目录下的模块。
"""

import numpy as np
import os
import matplotlib.pyplot as plt
from astropy.coordinates import SkyCoord
import astropy.units as u
from scipy import ndimage
from skimage import measure, filters, morphology
from astropy.stats import sigma_clipped_stats
from skimage.transform import resize

# 导入所有公共函数
from src.region_mapper.base import (
    define_regions,
    define_pdr_mask,
    define_cavity_mask,
    define_external_mask,
    get_region_tags,
    get_region_masks,
    process_wise_image
)

# 为兼容性导入旧函数名
from src.region_mapper.base import process_wise_image as process_image

# 导入数据管理函数
from src.data_manager import load_wise_fits, load_wise_multiband

# 设置日志记录器
from src.utils.logger import setup_logger
from src.utils.helpers import load_config, ensure_directory

logger = setup_logger(name='region_mapper')

# 以下是旧函数的兼容性包装器
def process_image(image_data, header=None, smooth_sigma=1.0):
    """
    处理WISE图像（平滑、背景减除）- 兼容性函数
    请使用 process_wise_image 代替

    Args:
        image_data: WISE图像数据
        header: FITS头信息（可选）
        smooth_sigma: 高斯平滑的sigma值

    Returns:
        numpy.ndarray: 处理后的图像
    """
    return process_wise_image(image_data, header, smooth_sigma)
