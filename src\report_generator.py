"""
报告生成模块，用于生成详细的处理报告
"""

import os
import json
import numpy as np
from datetime import datetime
from astropy.table import Table

from src.utils.logger import setup_logger
from src.utils.helpers import ensure_directory

# 设置日志记录器
logger = setup_logger(name='report_generator')

def generate_processing_report(source_info, processing_stats, config, output_path):
    """
    生成处理报告

    Args:
        source_info: 源信息字典
        processing_stats: 处理统计信息字典
        config: 配置字典
        output_path: 输出文件路径
    """
    logger.info(f"生成处理报告，输出到: {output_path}")

    try:
        # 创建报告字典
        report = {
            'source_info': source_info,
            'processing_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'processing_stats': processing_stats,
            'config_settings': {
                'quality_cuts': config.get('quality_cuts', {}),
                'extinction_params': config.get('extinction_params', {}),
                'distance_analysis': config.get('distance_analysis', {})
            }
        }

        # 确保目录存在
        ensure_directory(os.path.dirname(output_path))

        # 保存为JSON文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=4, ensure_ascii=False)

        logger.info(f"成功保存处理报告到: {output_path}")

        # 同时生成人类可读的文本报告
        txt_path = output_path.replace('.json', '.txt')
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write(f"处理报告 - {source_info['name']}\n")
            f.write(f"生成时间: {report['processing_time']}\n\n")

            f.write("源信息:\n")
            f.write(f"  名称: {source_info['name']}\n")
            f.write(f"  坐标: RA={source_info['ra']:.6f}, Dec={source_info['dec']:.6f} (ICRS)\n")
            f.write(f"  有效半径: {source_info['r_eff']*3600:.1f}角秒 ({source_info['r_eff']:.6f}度)\n\n")

            f.write("处理统计:\n")

            # Gaia数据统计
            if 'gaia_stats' in processing_stats:
                gaia_stats = processing_stats['gaia_stats']
                f.write("  Gaia数据:\n")
                f.write(f"    初始恒星数量: {gaia_stats.get('initial_count', 0)}\n")

                # 质量筛选统计
                if 'quality_cuts' in gaia_stats:
                    qc = gaia_stats['quality_cuts']
                    f.write("    质量筛选:\n")
                    for cut_name, cut_stats in qc.items():
                        f.write(f"      {cut_name}: 通过 {cut_stats.get('passed', 0)}/{cut_stats.get('total', 0)} ({cut_stats.get('percentage', 0):.1f}%)\n")
                    f.write(f"      筛选后恒星数量: {gaia_stats.get('after_quality_cuts', 0)}\n")

                # YSO筛选统计
                if 'yso_filtering' in gaia_stats:
                    yso = gaia_stats['yso_filtering']
                    f.write("    YSO筛选:\n")
                    f.write(f"      识别的YSO数量: {yso.get('yso_count', 0)}\n")
                    f.write(f"      最终恒星样本数量: {gaia_stats.get('final_count', 0)}\n")

                # 区域分布统计
                if 'region_distribution' in gaia_stats:
                    rd = gaia_stats['region_distribution']
                    f.write("    区域分布:\n")
                    for region, count in rd.items():
                        f.write(f"      {region}: {count} 颗恒星\n")

            # 消光计算统计
            if 'extinction_stats' in processing_stats:
                ext_stats = processing_stats['extinction_stats']
                f.write("\n  消光计算:\n")
                f.write(f"    有效消光值数量: {ext_stats.get('valid_av_count', 0)}\n")

                # 消光方法统计
                if 'methods' in ext_stats:
                    methods = ext_stats['methods']
                    f.write("    消光计算方法:\n")
                    for method, count in methods.items():
                        f.write(f"      {method}: {count} 颗恒星\n")

                # 消光范围
                if 'av_range' in ext_stats:
                    av_range = ext_stats['av_range']
                    f.write(f"    消光范围: {av_range.get('min', 0):.2f} - {av_range.get('max', 0):.2f} mag\n")

            # 距离分析统计
            if 'distance_stats' in processing_stats:
                dist_stats = processing_stats['distance_stats']
                f.write("\n  距离分析:\n")

                # 区域距离跳变
                if 'jumps' in dist_stats:
                    jumps = dist_stats['jumps']
                    f.write("    检测到的距离跳变:\n")
                    for region, region_jumps in jumps.items():
                        if region_jumps:
                            f.write(f"      {region}区域:\n")
                            for jump in region_jumps:
                                f.write(f"        距离: {jump.get('distance', 0):.2f} kpc, 高度: {jump.get('height', 0):.2f} mag, 显著性: {jump.get('significance', 0):.2f}\n")
                        else:
                            f.write(f"      {region}区域: 未检测到显著跳变\n")

                # 最终距离估计
                if 'final_distance' in dist_stats:
                    fd = dist_stats['final_distance']
                    f.write(f"    最终距离估计: {fd.get('value', 0):.2f} ± {fd.get('error', 0):.2f} kpc\n")
                    f.write(f"    置信度: {fd.get('confidence', 'unknown')}\n")

                    if 'evidence' in fd:
                        f.write("    证据:\n")
                        for evidence in fd['evidence']:
                            f.write(f"      - {evidence}\n")

            f.write("\n配置设置:\n")

            # 质量筛选参数
            if 'quality_cuts' in report['config_settings']:
                qc = report['config_settings']['quality_cuts']
                f.write("  质量筛选参数:\n")
                for param, value in qc.items():
                    f.write(f"    {param}: {value}\n")

            # 消光计算参数
            if 'extinction_params' in report['config_settings']:
                ep = report['config_settings']['extinction_params']
                f.write("\n  消光计算参数:\n")
                for param, value in ep.items():
                    f.write(f"    {param}: {value}\n")

            # 距离分析参数
            if 'distance_analysis' in report['config_settings']:
                da = report['config_settings']['distance_analysis']
                f.write("\n  距离分析参数:\n")
                for param, value in da.items():
                    f.write(f"    {param}: {value}\n")

        logger.info(f"成功保存文本报告到: {txt_path}")

    except Exception as e:
        logger.error(f"生成处理报告失败: {str(e)}")
        raise

def collect_processing_stats(gaia_data, filtered_data, star_sample, stars_with_av, region_results, distance_estimate):
    """
    收集处理统计信息

    Args:
        gaia_data: 原始Gaia数据
        filtered_data: 质量筛选后的数据
        star_sample: 最终恒星样本
        stars_with_av: 带消光值的恒星样本
        region_results: 区域分析结果
        distance_estimate: 距离估计结果

    Returns:
        dict: 处理统计信息字典
    """
    # 导入必要的模块
    from astropy.table import Table
    stats = {}

    # Gaia数据统计
    gaia_stats = {
        'initial_count': len(gaia_data) if gaia_data is not None else 0
    }

    # 质量筛选统计
    if filtered_data is not None:
        gaia_stats['after_quality_cuts'] = len(filtered_data)

        # 计算各项质量筛选的通过率
        quality_cuts = {}

        # 视差信噪比筛选
        if 'parallax' in gaia_data.colnames and 'parallax_error' in gaia_data.colnames:
            valid_parallax = ~np.isnan(gaia_data['parallax']) & ~np.isnan(gaia_data['parallax_error']) & (gaia_data['parallax_error'] > 0)
            if np.sum(valid_parallax) > 0:
                parallax_snr = np.zeros(len(gaia_data))
                parallax_snr[valid_parallax] = np.abs(gaia_data['parallax'][valid_parallax] / gaia_data['parallax_error'][valid_parallax])
                parallax_snr_mask = parallax_snr >= 5.0  # 假设阈值为5.0
                quality_cuts['视差信噪比'] = {
                    'total': np.sum(valid_parallax),
                    'passed': np.sum(parallax_snr_mask),
                    'percentage': np.sum(parallax_snr_mask) / np.sum(valid_parallax) * 100 if np.sum(valid_parallax) > 0 else 0
                }

        # RUWE筛选
        if 'ruwe' in gaia_data.colnames:
            valid_ruwe = ~np.isnan(gaia_data['ruwe'])
            if np.sum(valid_ruwe) > 0:
                ruwe_mask = np.ones(len(gaia_data), dtype=bool)
                ruwe_mask[valid_ruwe] = gaia_data['ruwe'][valid_ruwe] <= 1.4  # 假设阈值为1.4
                quality_cuts['RUWE'] = {
                    'total': np.sum(valid_ruwe),
                    'passed': np.sum(ruwe_mask),
                    'percentage': np.sum(ruwe_mask) / np.sum(valid_ruwe) * 100 if np.sum(valid_ruwe) > 0 else 0
                }

        gaia_stats['quality_cuts'] = quality_cuts

    # YSO筛选统计
    if star_sample is not None:
        gaia_stats['final_count'] = len(star_sample)

        # 计算YSO数量
        yso_count = len(filtered_data) - len(star_sample) if filtered_data is not None else 0
        gaia_stats['yso_filtering'] = {
            'yso_count': yso_count
        }

    # 区域分布统计
    if star_sample is not None and 'region' in star_sample.colnames:
        region_counts = {}
        for region in np.unique(star_sample['region']):
            region_counts[region] = np.sum(star_sample['region'] == region)
        gaia_stats['region_distribution'] = region_counts

    stats['gaia_stats'] = gaia_stats

    # 消光计算统计
    if stars_with_av is not None:
        ext_stats = {}

        # 有效消光值数量
        valid_av = ~np.isnan(stars_with_av['a_v']) if 'a_v' in stars_with_av.colnames else np.array([])
        ext_stats['valid_av_count'] = np.sum(valid_av)

        # 消光方法统计
        if 'av_method' in stars_with_av.colnames:
            method_counts = {}
            for method in np.unique(stars_with_av['av_method']):
                method_counts[method] = np.sum(stars_with_av['av_method'] == method)
            ext_stats['methods'] = method_counts

        # 消光范围
        if 'a_v' in stars_with_av.colnames and np.sum(valid_av) > 0:
            ext_stats['av_range'] = {
                'min': np.nanmin(stars_with_av['a_v']),
                'max': np.nanmax(stars_with_av['a_v']),
                'mean': np.nanmean(stars_with_av['a_v']),
                'median': np.nanmedian(stars_with_av['a_v'])
            }

        stats['extinction_stats'] = ext_stats

    # 距离分析统计
    dist_stats = {}

    # 区域距离跳变
    if region_results:
        jumps = {}
        for region, result in region_results.items():
            if 'jumps' in result:
                region_jumps = []
                for jump in result['jumps']:
                    region_jumps.append({
                        'distance': jump.get('distance'),
                        'height': jump.get('height'),
                        'significance': jump.get('significance')
                    })
                jumps[region] = region_jumps
        dist_stats['jumps'] = jumps

    # 最终距离估计
    if distance_estimate:
        dist_stats['final_distance'] = {
            'value': distance_estimate.get('cloud_distance'),
            'error': distance_estimate.get('distance_error'),
            'confidence': distance_estimate.get('confidence'),
            'evidence': distance_estimate.get('evidence')
        }

    stats['distance_stats'] = dist_stats

    return stats
