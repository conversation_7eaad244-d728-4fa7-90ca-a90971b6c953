"""
测试基于W3/W4波段比值的PDR区域检测方法

对前5个HII区源进行测试处理，生成详细的处理报告和可视化结果。
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from astropy.coordinates import SkyCoord
import astropy.units as u
from astropy.wcs import WCS
from astropy.io import fits
import logging
from datetime import datetime
from skimage.transform import resize

from src.utils.logger import setup_logger
from src.utils.helpers import load_config, ensure_directory, parse_galactic_name
from src.data_manager import load_wise_fits, load_wise_multiband
from src.region_mapper.base import define_regions, process_wise_image
from src.region_mapper.ratio_method import define_pdr_mask_ratio
from src.region_mapper.threshold_method import define_pdr_mask_threshold
from src.region_mapper.contour_method import define_pdr_mask_contour

# 设置日志记录器
logger = setup_logger(name='test_ratio_method')

def load_hii_region_catalog(catalog_path):
    """
    加载HII区源表

    Args:
        catalog_path: 源表路径

    Returns:
        pandas.DataFrame: 源表数据
    """
    try:
        # 尝试读取源表
        with open(catalog_path, 'r') as f:
            lines = f.readlines()

        # 手动解析源表
        data = []
        for line in lines:
            if line.strip() and not line.startswith('#'):
                parts = line.strip().split()
                if len(parts) >= 3:
                    source_name = parts[0]  # 源名称
                    quality = parts[1]      # 质量标记 (K/Q)
                    try:
                        N = float(parts[2])  # 有效半径（角秒）
                        distance = float(parts[3])  # 距离（kpc）
                        distance_error_min = float(parts[4]) if len(parts) > 4 else 0.0  # 距离误差下限
                        distance_error_max = float(parts[5]) if len(parts) > 5 else 0.0  # 距离误差上限
                        data.append([source_name, quality, N, distance, distance_error_min, distance_error_max])
                    except ValueError:
                        continue

        # 创建DataFrame
        catalog = pd.DataFrame(data, columns=['source_name', 'quality', 'N', 'distance',
                                             'distance_error_min', 'distance_error_max'])
        logger.info(f"成功加载源表，共{len(catalog)}个源")
        return catalog
    except Exception as e:
        logger.error(f"加载源表失败: {str(e)}")
        raise

def process_source(source_name, output_dir, config_path='config/default_config.yaml'):
    """
    处理单个HII区源，使用三种PDR检测方法并比较结果

    Args:
        source_name: 源名称
        output_dir: 输出目录
        config_path: 配置文件路径

    Returns:
        dict: 处理结果
    """
    logger.info(f"开始处理源 {source_name}")

    try:
        # 加载配置
        config = load_config(config_path)

        # 创建源专用输出目录
        source_output_dir = os.path.join(output_dir, source_name)
        ensure_directory(source_output_dir)

        # 创建报告目录
        report_dir = os.path.join(source_output_dir, 'reports')
        ensure_directory(report_dir)

        # 创建可视化目录
        vis_dir = os.path.join(source_output_dir, 'visualizations')
        ensure_directory(vis_dir)

        # 加载源信息
        catalog_path = config['data_paths']['hii_region_catalog']
        catalog = load_hii_region_catalog(catalog_path)
        source_info = catalog[catalog['source_name'] == source_name]

        if len(source_info) == 0:
            logger.error(f"源表中未找到源 {source_name}")
            return {'success': False, 'error': f"源表中未找到源 {source_name}"}

        # 获取源参数
        r_eff_arcsec = source_info['N'].values[0]  # 有效半径（角秒）
        r_eff_deg = r_eff_arcsec / 3600.0  # 转换为度

        # 解析银道坐标
        l, b = parse_galactic_name(source_name)
        center_coord = SkyCoord(l=l*u.degree, b=b*u.degree, frame='galactic')
        logger.info(f"源中心坐标: l={l:.6f}°, b={b:.6f}°")

        # 加载WISE 12μm数据
        wise_base_path = config['data_paths']['wise_fits_base']
        w3_data, w3_header, w3_wcs = load_wise_fits(
            source_name, wise_base_path, config_path, band='12')

        if w3_data is None:
            logger.error(f"未能加载源 {source_name} 的WISE 12μm数据")
            return {'success': False, 'error': f"未能加载WISE 12μm数据"}

        # 处理WISE图像
        processed_w3 = process_wise_image(w3_data)

        # 使用三种方法定义PDR掩模
        # 1. 阈值方法
        try:
            threshold_mask = define_pdr_mask_threshold(
                processed_w3, w3_wcs, center_coord, r_eff_deg,
                threshold_factor=0.3, max_radius_factor=5.0, search_radius_factor=3.0
            )
            if threshold_mask is None:
                threshold_mask = np.zeros_like(processed_w3, dtype=bool)
                logger.warning("阈值方法返回了None，使用零掩模代替")
        except Exception as e:
            logger.error(f"阈值方法失败: {str(e)}")
            threshold_mask = np.zeros_like(processed_w3, dtype=bool)

        # 2. 等高线方法
        try:
            contour_mask = define_pdr_mask_contour(
                processed_w3, w3_wcs, center_coord, r_eff_deg,
                search_radius_factor=3.0, n_contours=10, contour_start_nsigma=1.0,
                source_name=source_name, output_dir=source_output_dir, save_contour_plot=True
            )
            if contour_mask is None:
                contour_mask = np.zeros_like(processed_w3, dtype=bool)
                logger.warning("等高线方法返回了None，使用零掩模代替")
        except Exception as e:
            logger.error(f"等高线方法失败: {str(e)}")
            contour_mask = np.zeros_like(processed_w3, dtype=bool)

        # 3. W3/W4波段比值方法
        try:
            ratio_mask = define_pdr_mask_ratio(
                source_name, w3_wcs, center_coord, r_eff_deg,
                search_radius_factor=3.0, smooth_sigma=1.0,
                wise_base_path=wise_base_path, config_path=config_path,
                output_dir=source_output_dir, save_ratio_plot=True
            )
            if ratio_mask is None:
                ratio_mask = np.zeros_like(processed_w3, dtype=bool)
                logger.warning("W3/W4波段比值方法返回了None，使用零掩模代替")
            elif ratio_mask.shape != processed_w3.shape:
                logger.warning(f"W3/W4波段比值方法返回的掩模形状({ratio_mask.shape})与W3数据形状({processed_w3.shape})不一致，进行调整")
                # 调整比值掩模大小以匹配W3数据
                ratio_mask_resized = np.zeros_like(processed_w3, dtype=bool)
                # 如果比值掩模较小，将其放置在中心位置
                if ratio_mask.shape[0] <= processed_w3.shape[0] and ratio_mask.shape[1] <= processed_w3.shape[1]:
                    start_row = (processed_w3.shape[0] - ratio_mask.shape[0]) // 2
                    start_col = (processed_w3.shape[1] - ratio_mask.shape[1]) // 2
                    ratio_mask_resized[start_row:start_row+ratio_mask.shape[0],
                                      start_col:start_col+ratio_mask.shape[1]] = ratio_mask
                else:
                    # 如果比值掩模较大，进行缩放
                    ratio_mask_resized = resize(ratio_mask.astype(float), processed_w3.shape,
                                              order=0, preserve_range=True).astype(bool)
                ratio_mask = ratio_mask_resized
        except Exception as e:
            logger.error(f"W3/W4波段比值方法失败: {str(e)}")
            ratio_mask = np.zeros_like(processed_w3, dtype=bool)

        # 创建可视化比较图
        plt.figure(figsize=(15, 10))

        # 显示原始WISE 12μm图像
        plt.subplot(221)
        plt.title(f"{source_name} - WISE 12μm")
        plt.imshow(np.log1p(w3_data), origin='lower', cmap='inferno')
        center_x, center_y = w3_wcs.world_to_pixel(center_coord)
        plt.plot(center_x, center_y, 'c+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_deg * 3600 / (w3_header['CDELT2'] * 3600),
                          fill=False, color='cyan', linestyle='--')
        plt.gca().add_patch(circle)

        # 显示阈值方法结果
        plt.subplot(222)
        plt.title("阈值方法 PDR掩模")
        plt.imshow(threshold_mask, origin='lower', cmap='gray')
        plt.plot(center_x, center_y, 'r+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_deg * 3600 / (w3_header['CDELT2'] * 3600),
                          fill=False, color='r', linestyle='--')
        plt.gca().add_patch(circle)

        # 显示等高线方法结果
        plt.subplot(223)
        plt.title("等高线方法 PDR掩模")
        plt.imshow(contour_mask, origin='lower', cmap='gray')
        plt.plot(center_x, center_y, 'r+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_deg * 3600 / (w3_header['CDELT2'] * 3600),
                          fill=False, color='r', linestyle='--')
        plt.gca().add_patch(circle)

        # 显示W3/W4波段比值方法结果
        plt.subplot(224)
        plt.title("W3/W4波段比值方法 PDR掩模")
        plt.imshow(ratio_mask, origin='lower', cmap='gray')
        plt.plot(center_x, center_y, 'r+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_deg * 3600 / (w3_header['CDELT2'] * 3600),
                          fill=False, color='r', linestyle='--')
        plt.gca().add_patch(circle)

        plt.tight_layout()
        plt.savefig(os.path.join(vis_dir, f"{source_name}_pdr_methods_comparison.png"), dpi=300)
        plt.close()

        # 计算各方法的PDR区域像素数和覆盖率
        total_pixels = w3_data.size
        threshold_pixels = np.sum(threshold_mask)
        contour_pixels = np.sum(contour_mask)
        ratio_pixels = np.sum(ratio_mask)

        threshold_coverage = threshold_pixels / total_pixels * 100
        contour_coverage = contour_pixels / total_pixels * 100
        ratio_coverage = ratio_pixels / total_pixels * 100

        # 计算方法间的重叠度
        threshold_contour_overlap = np.sum(threshold_mask & contour_mask)
        threshold_ratio_overlap = np.sum(threshold_mask & ratio_mask)
        contour_ratio_overlap = np.sum(contour_mask & ratio_mask)

        threshold_contour_overlap_pct = threshold_contour_overlap / min(threshold_pixels, contour_pixels) * 100 if min(threshold_pixels, contour_pixels) > 0 else 0
        threshold_ratio_overlap_pct = threshold_ratio_overlap / min(threshold_pixels, ratio_pixels) * 100 if min(threshold_pixels, ratio_pixels) > 0 else 0
        contour_ratio_overlap_pct = contour_ratio_overlap / min(contour_pixels, ratio_pixels) * 100 if min(contour_pixels, ratio_pixels) > 0 else 0

        # 生成处理报告
        report_path = os.path.join(report_dir, f"{source_name}_pdr_analysis_report.txt")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(f"PDR区域检测方法比较报告 - {source_name}\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("="*50 + "\n\n")

            f.write("源信息:\n")
            f.write(f"  名称: {source_name}\n")
            f.write(f"  银道坐标: l={l:.6f}°, b={b:.6f}°\n")
            f.write(f"  有效半径: {r_eff_arcsec:.1f}角秒 ({r_eff_deg:.6f}°)\n")
            f.write(f"  距离: {source_info['distance'].values[0]:.2f} kpc\n\n")

            f.write("PDR区域检测结果:\n")
            f.write(f"  阈值方法: {threshold_pixels}像素 ({threshold_coverage:.2f}%)\n")
            f.write(f"  等高线方法: {contour_pixels}像素 ({contour_coverage:.2f}%)\n")
            f.write(f"  W3/W4波段比值方法: {ratio_pixels}像素 ({ratio_coverage:.2f}%)\n\n")

            f.write("方法间重叠度:\n")
            f.write(f"  阈值方法 vs 等高线方法: {threshold_contour_overlap}像素 ({threshold_contour_overlap_pct:.2f}%)\n")
            f.write(f"  阈值方法 vs W3/W4波段比值方法: {threshold_ratio_overlap}像素 ({threshold_ratio_overlap_pct:.2f}%)\n")
            f.write(f"  等高线方法 vs W3/W4波段比值方法: {contour_ratio_overlap}像素 ({contour_ratio_overlap_pct:.2f}%)\n\n")

            f.write("结论:\n")
            if ratio_pixels > 0:
                f.write("  W3/W4波段比值方法成功检测到PDR区域。\n")
                if ratio_coverage > contour_coverage and ratio_coverage > threshold_coverage:
                    f.write("  W3/W4波段比值方法检测到的PDR区域覆盖率最高。\n")
                elif contour_coverage > ratio_coverage and contour_coverage > threshold_coverage:
                    f.write("  等高线方法检测到的PDR区域覆盖率最高。\n")
                else:
                    f.write("  阈值方法检测到的PDR区域覆盖率最高。\n")
            else:
                f.write("  W3/W4波段比值方法未能检测到PDR区域。\n")

            if threshold_ratio_overlap_pct > 70 or contour_ratio_overlap_pct > 70:
                f.write("  W3/W4波段比值方法与其他方法有较高的一致性。\n")
            elif threshold_ratio_overlap_pct > 30 or contour_ratio_overlap_pct > 30:
                f.write("  W3/W4波段比值方法与其他方法有中等程度的一致性。\n")
            else:
                f.write("  W3/W4波段比值方法与其他方法的一致性较低。\n")

        logger.info(f"源 {source_name} 处理完成，报告已保存到 {report_path}")

        return {
            'success': True,
            'source_name': source_name,
            'threshold_coverage': threshold_coverage,
            'contour_coverage': contour_coverage,
            'ratio_coverage': ratio_coverage,
            'threshold_contour_overlap_pct': threshold_contour_overlap_pct,
            'threshold_ratio_overlap_pct': threshold_ratio_overlap_pct,
            'contour_ratio_overlap_pct': contour_ratio_overlap_pct,
            'report_path': report_path
        }

    except Exception as e:
        logger.error(f"处理源 {source_name} 时发生错误: {str(e)}", exc_info=True)
        return {'success': False, 'error': str(e)}

def main():
    """
    主函数
    """
    # 设置输出目录
    output_dir = "results/ratio_method_test"
    ensure_directory(output_dir)

    # 加载配置
    config_path = 'config/default_config.yaml'
    config = load_config(config_path)

    # 加载HII区源表
    catalog_path = config['data_paths']['hii_region_catalog']
    catalog = load_hii_region_catalog(catalog_path)

    # 选择前5个源
    sources_to_process = catalog.iloc[:5]

    # 处理结果
    results = []

    # 处理每个源
    for _, row in sources_to_process.iterrows():
        source_name = row['source_name'].strip()
        result = process_source(source_name, output_dir, config_path)
        results.append(result)

    # 生成总结报告
    summary_path = os.path.join(output_dir, "summary_report.txt")
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write("W3/W4波段比值PDR检测方法测试总结报告\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("="*50 + "\n\n")

        f.write(f"测试源数量: {len(sources_to_process)}\n")
        successful = [r for r in results if r['success']]
        f.write(f"成功处理: {len(successful)}\n")
        f.write(f"失败: {len(results) - len(successful)}\n\n")

        if successful:
            f.write("成功处理的源:\n")
            for result in successful:
                f.write(f"  {result['source_name']}:\n")
                f.write(f"    PDR覆盖率 - 阈值方法: {result['threshold_coverage']:.2f}%\n")
                f.write(f"    PDR覆盖率 - 等高线方法: {result['contour_coverage']:.2f}%\n")
                f.write(f"    PDR覆盖率 - W3/W4波段比值方法: {result['ratio_coverage']:.2f}%\n")
                f.write(f"    方法重叠度 - 阈值vs等高线: {result['threshold_contour_overlap_pct']:.2f}%\n")
                f.write(f"    方法重叠度 - 阈值vsW3/W4: {result['threshold_ratio_overlap_pct']:.2f}%\n")
                f.write(f"    方法重叠度 - 等高线vsW3/W4: {result['contour_ratio_overlap_pct']:.2f}%\n\n")

            # 计算平均覆盖率和重叠度
            avg_threshold_coverage = np.mean([r['threshold_coverage'] for r in successful])
            avg_contour_coverage = np.mean([r['contour_coverage'] for r in successful])
            avg_ratio_coverage = np.mean([r['ratio_coverage'] for r in successful])

            avg_threshold_contour_overlap = np.mean([r['threshold_contour_overlap_pct'] for r in successful])
            avg_threshold_ratio_overlap = np.mean([r['threshold_ratio_overlap_pct'] for r in successful])
            avg_contour_ratio_overlap = np.mean([r['contour_ratio_overlap_pct'] for r in successful])

            f.write("平均统计:\n")
            f.write(f"  平均PDR覆盖率 - 阈值方法: {avg_threshold_coverage:.2f}%\n")
            f.write(f"  平均PDR覆盖率 - 等高线方法: {avg_contour_coverage:.2f}%\n")
            f.write(f"  平均PDR覆盖率 - W3/W4波段比值方法: {avg_ratio_coverage:.2f}%\n")
            f.write(f"  平均方法重叠度 - 阈值vs等高线: {avg_threshold_contour_overlap:.2f}%\n")
            f.write(f"  平均方法重叠度 - 阈值vsW3/W4: {avg_threshold_ratio_overlap:.2f}%\n")
            f.write(f"  平均方法重叠度 - 等高线vsW3/W4: {avg_contour_ratio_overlap:.2f}%\n\n")

            # 总结
            f.write("总结:\n")
            if avg_ratio_coverage > 0:
                f.write("  W3/W4波段比值方法能够成功检测PDR区域。\n")

                if avg_ratio_coverage > avg_contour_coverage and avg_ratio_coverage > avg_threshold_coverage:
                    f.write("  W3/W4波段比值方法平均检测到的PDR区域覆盖率最高。\n")
                elif avg_contour_coverage > avg_ratio_coverage and avg_contour_coverage > avg_threshold_coverage:
                    f.write("  等高线方法平均检测到的PDR区域覆盖率最高。\n")
                else:
                    f.write("  阈值方法平均检测到的PDR区域覆盖率最高。\n")

                if avg_threshold_ratio_overlap > 70 or avg_contour_ratio_overlap > 70:
                    f.write("  W3/W4波段比值方法与其他方法有较高的平均一致性。\n")
                elif avg_threshold_ratio_overlap > 30 or avg_contour_ratio_overlap > 30:
                    f.write("  W3/W4波段比值方法与其他方法有中等程度的平均一致性。\n")
                else:
                    f.write("  W3/W4波段比值方法与其他方法的平均一致性较低。\n")
            else:
                f.write("  W3/W4波段比值方法在大多数情况下未能检测到PDR区域。\n")

        if len(results) - len(successful) > 0:
            f.write("\n处理失败的源:\n")
            for result in [r for r in results if not r['success']]:
                f.write(f"  {result.get('source_name', 'Unknown')}: {result.get('error', 'Unknown error')}\n")

    logger.info(f"总结报告已保存到 {summary_path}")
    print(f"测试完成，总结报告已保存到 {summary_path}")

if __name__ == "__main__":
    main()
