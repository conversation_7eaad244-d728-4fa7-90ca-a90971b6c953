"""
批量处理HII区域源的脚本

该脚本用于批量处理多个HII区域源，估计它们的分子云距离。
"""

import os
import sys
import subprocess
import pandas as pd
import time
from datetime import datetime

def load_source_list(file_path, num_sources=10):
    """
    加载源列表

    Args:
        file_path: 源表文件路径
        num_sources: 要处理的源数量

    Returns:
        list: 源名称列表
    """
    try:
        # 读取源表
        column_names = ['source_name', 'type', 'N', 'distance', 'distance_err_minus', 'distance_err_plus']
        df = pd.read_csv(file_path, delim_whitespace=True, header=None, names=column_names)

        # 提取源名称
        source_names = df['source_name'].iloc[:num_sources].tolist()

        print(f"成功加载{len(source_names)}个源")
        return source_names

    except Exception as e:
        print(f"加载源列表失败: {str(e)}")
        return []

def process_source(source_name, output_dir):
    """
    处理单个源

    Args:
        source_name: 源名称
        output_dir: 输出目录

    Returns:
        bool: 处理是否成功
    """
    print(f"\n{'='*50}")
    print(f"开始处理源: {source_name}")
    print(f"{'='*50}")

    # 创建源的输出目录
    source_output_dir = os.path.join(output_dir, source_name)
    os.makedirs(source_output_dir, exist_ok=True)

    # 构建命令
    cmd = [sys.executable, "main.py", "--source", source_name, "--output-dir", source_output_dir]

    # 记录开始时间
    start_time = time.time()

    try:
        # 执行命令
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        stdout, stderr = process.communicate()

        # 记录结束时间
        end_time = time.time()
        duration = end_time - start_time

        # 检查是否成功
        if process.returncode == 0:
            print(f"源 {source_name} 处理成功，耗时: {duration:.2f}秒")

            # 保存输出日志
            log_file = os.path.join(source_output_dir, "process_log.txt")
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"耗时: {duration:.2f}秒\n\n")
                f.write("标准输出:\n")
                f.write(stdout)
                f.write("\n\n标准错误:\n")
                f.write(stderr)

            return True
        else:
            print(f"源 {source_name} 处理失败，返回码: {process.returncode}")
            print(f"错误信息: {stderr}")

            # 保存错误日志
            error_log_file = os.path.join(source_output_dir, "error_log.txt")
            with open(error_log_file, 'w', encoding='utf-8') as f:
                f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"耗时: {duration:.2f}秒\n\n")
                f.write(f"返回码: {process.returncode}\n\n")
                f.write("标准输出:\n")
                f.write(stdout)
                f.write("\n\n标准错误:\n")
                f.write(stderr)

            return False

    except Exception as e:
        print(f"处理源 {source_name} 时发生异常: {str(e)}")
        return False

def main():
    """
    主函数
    """
    # 设置源表路径和输出目录
    source_list_path = "H:/Augment/Parallax distances/Parallax-based distances.dat"
    output_base_dir = "data/output/batch_results"

    # 创建输出基础目录
    os.makedirs(output_base_dir, exist_ok=True)

    # 加载源列表
    source_names = load_source_list(source_list_path, num_sources=3)

    if not source_names:
        print("没有源可处理")
        return 1

    # 记录开始时间
    batch_start_time = time.time()

    # 处理每个源
    results = []
    for i, source_name in enumerate(source_names):
        print(f"\n处理源 {i+1}/{len(source_names)}: {source_name}")
        success = process_source(source_name, output_base_dir)
        results.append((source_name, success))

    # 记录结束时间
    batch_end_time = time.time()
    batch_duration = batch_end_time - batch_start_time

    # 输出汇总结果
    print("\n\n" + "="*50)
    print("批处理完成")
    print(f"总耗时: {batch_duration:.2f}秒")
    print(f"成功: {sum(1 for _, success in results if success)}/{len(results)}")
    print("="*50)

    # 输出失败的源
    failed_sources = [name for name, success in results if not success]
    if failed_sources:
        print("\n失败的源:")
        for name in failed_sources:
            print(f"- {name}")

    # 保存汇总结果
    summary_file = os.path.join(output_base_dir, "batch_summary.txt")
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(f"批处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总耗时: {batch_duration:.2f}秒\n")
        f.write(f"成功: {sum(1 for _, success in results if success)}/{len(results)}\n\n")

        f.write("处理结果:\n")
        for name, success in results:
            f.write(f"- {name}: {'成功' if success else '失败'}\n")

        if failed_sources:
            f.write("\n失败的源:\n")
            for name in failed_sources:
                f.write(f"- {name}\n")

    return 0

if __name__ == "__main__":
    sys.exit(main())
