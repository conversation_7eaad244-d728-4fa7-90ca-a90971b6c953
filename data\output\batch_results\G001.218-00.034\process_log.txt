处理时间: 2025-04-24 04:51:30
耗时: 4.43秒

标准输出:

未能估计G001.218-00.034的分子云距离


标准错误:
2025-04-24 04:51:29,986 - main - INFO - 输出目录: data/output/batch_results\G001.218-00.034
2025-04-24 04:51:29,986 - main - INFO - 加载源G001.218-00.034的信息
2025-04-24 04:51:29,986 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
H:\Augment\Parallax distances\src\data_manager.py:43: FutureWarning: The 'delim_whitespace' keyword in pd.read_csv is deprecated and will be removed in a future version. Use ``sep='\s+'`` instead
  df = pd.read_csv(catalog_path, delim_whitespace=True, comment='#', header=None, names=column_names)
2025-04-24 04:51:29,993 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-04-24 04:51:29,994 - main - INFO - 目录中的所有文件: ['G001.218-00.034_ATLASGAL_870um.fits', 'G001.218-00.034_IRIS_100.fits', 'G001.218-00.034_NVSS.fits', 'G001.218-00.034_WISE_12.fits', 'G001.218-00.034_WISE_22.fits', 'G001.218-00.034_WISE_3.4.fits', 'G001.218-00.034_WISE_4.6.fits']
2025-04-24 04:51:29,994 - main - INFO - 第一次匹配结果: ['G001.218-00.034_WISE_12.fits']
2025-04-24 04:51:29,994 - main - INFO - 从FITS文件获取坐标: U:/Data/Bubbles/Wise bubbles\G001.218-00.034\G001.218-00.034_WISE_12.fits
2025-04-24 04:51:30,073 - main - INFO - 从FITS头信息获取坐标系统: fk5
2025-04-24 04:51:30,076 - main - INFO - 从FITS头信息获取坐标: RA=267.156992, Dec=-27.911305 (ICRS)
2025-04-24 04:51:30,076 - main - INFO - 使用有效半径611.0角秒 (0.169722度)
2025-04-24 04:51:30,077 - main - INFO - 从源表加载信息: RA=267.156992, Dec=-27.911305, R_eff=0.169722度
2025-04-24 04:51:30,078 - main - INFO - 步骤1：加载WISE数据
2025-04-24 04:51:30,081 - data_manager - INFO - 为源G001.218-00.034加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G001.218-00.034
2025-04-24 04:51:30,082 - data_manager - INFO - 目录中的所有文件: ['G001.218-00.034_ATLASGAL_870um.fits', 'G001.218-00.034_IRIS_100.fits', 'G001.218-00.034_NVSS.fits', 'G001.218-00.034_WISE_12.fits', 'G001.218-00.034_WISE_22.fits', 'G001.218-00.034_WISE_3.4.fits', 'G001.218-00.034_WISE_4.6.fits']
2025-04-24 04:51:30,082 - data_manager - INFO - 第一次匹配结果: ['G001.218-00.034_WISE_12.fits']
2025-04-24 04:51:30,082 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G001.218-00.034\G001.218-00.034_WISE_12.fits
2025-04-24 04:51:30,093 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (940, 940)
2025-04-24 04:51:30,093 - main - INFO - WISE数据加载完成
2025-04-24 04:51:30,093 - main - INFO - 步骤2：区域映射
2025-04-24 04:51:30,093 - region_mapper - INFO - 处理WISE图像，平滑sigma=1.0
2025-04-24 04:51:30,098 - region_mapper - INFO - 替换图像中的NaN值
2025-04-24 04:51:30,107 - region_mapper - INFO - 应用高斯平滑
2025-04-24 04:51:30,127 - region_mapper - INFO - 估计并减除背景
2025-04-24 04:51:30,158 - region_mapper - INFO - WISE图像处理完成
2025-04-24 04:51:30,159 - region_mapper - INFO - 定义PDR掩模，阈值因子=0.5，最大半径因子=3.0
2025-04-24 04:51:30,159 - region_mapper - INFO - WCS坐标系统: fk5
2025-04-24 04:51:30,159 - region_mapper - INFO - 将中心坐标从ICRS转换为fk5
2025-04-24 04:51:30,166 - region_mapper - INFO - 中心坐标 (RA=267.156992, Dec=-27.911305) 对应像素坐标 (X=469.5, Y=469.5)
2025-04-24 04:51:30,170 - region_mapper - INFO - 像素尺度: 489.42 像素/角秒
2025-04-24 04:51:30,170 - region_mapper - INFO - 最大搜索半径: 3.7 像素
2025-04-24 04:51:30,181 - region_mapper - INFO - PDR阈值: 0.00 (峰值的50%)
2025-04-24 04:51:30,303 - region_mapper - INFO - PDR掩模创建完成，覆盖44个像素
2025-04-24 04:51:30,306 - region_mapper - INFO - 定义空腔掩模，有效半径=0.16972222222222222度
2025-04-24 04:51:30,313 - region_mapper - INFO - 有效半径: 1.2 像素
2025-04-24 04:51:30,324 - region_mapper - INFO - 空腔掩模创建完成，覆盖0个像素
2025-04-24 04:51:30,325 - region_mapper - INFO - 定义外部区域掩模，最大半径因子=5.0
2025-04-24 04:51:30,333 - region_mapper - INFO - 最大半径: 6.2 像素
2025-04-24 04:51:30,346 - region_mapper - INFO - 外部区域掩模创建完成，覆盖76个像素
2025-04-24 04:51:30,347 - region_mapper - INFO - 保存区域掩模到: data/output/batch_results\G001.218-00.034\processed\G001.218-00.034_region_masks.npz
2025-04-24 04:51:30,370 - region_mapper - INFO - 成功保存区域掩模
2025-04-24 04:51:30,370 - main - INFO - 区域映射完成
2025-04-24 04:51:30,370 - main - INFO - 由于缺少Gaia数据，跳过恒星选择、消光计算和距离分析步骤
2025-04-24 04:51:30,370 - main - INFO - 处理G001.218-00.034完成
