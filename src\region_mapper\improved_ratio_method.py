"""
改进的波段比值法PDR检测模块

使用WISE W4(22μm)波段数据和W3(12μm)/W4(22μm)波段比值检测HII区空腔和PDR区域。
首先识别HII区空腔，然后在其周围寻找PDR区域。
"""

import os
import numpy as np
from scipy import ndimage
from scipy import ndimage as ndi
from skimage import measure, morphology
from skimage.transform import resize
from astropy.coordinates import SkyCoord
import astropy.units as u
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import matplotlib.colors as mcolors
from sklearn.cluster import DBSCAN
from sklearn.neighbors import NearestNeighbors

from src.utils.logger import setup_logger
from src.utils.helpers import ensure_directory, load_config
from src.data_manager import load_wise_fits, load_wise_multiband

# 设置日志记录器
logger = setup_logger(name='region_mapper.improved_ratio')

def define_cavity_mask_w4(w4_data, ratio_data, wcs, center_coord, r_eff, 
                        w4_threshold_factor=0.7, ratio_upper_limit=0.5,
                        min_size_factor=0.2, smooth_sigma=1.0,
                        cavity_weight=0.7, ratio_weight=0.3,
                        morphology_disk_size=5):
    """
    使用W4波段数据和W3/W4比值定义HII区空腔掩模
    
    HII区空腔通常在W4波段(22μm)具有较高的强度，同时W3/W4比值较低。
    该方法结合这两个特征自动识别HII区中心空腔区域。
    
    Args:
        w4_data: W4波段数据
        ratio_data: W3/W4比值数据
        wcs: 世界坐标系
        center_coord: 中心坐标 (SkyCoord对象)
        r_eff: 有效半径（度）
        w4_threshold_factor: W4阈值因子，默认为0.7
        ratio_upper_limit: W3/W4比值上限，默认为0.5
        min_size_factor: 最小空腔大小因子，默认为0.2
        smooth_sigma: 平滑参数，默认为1.0
        cavity_weight: W4波段在空腔检测中的权重，默认为0.7
        ratio_weight: W3/W4比值在空腔检测中的权重，默认为0.3
        morphology_disk_size: 形态学操作磁盘大小，默认为5
        
    Returns:
        numpy.ndarray: 空腔掩模（布尔数组）
    """
    logger.info(f"使用W4波段数据和W3/W4比值定义HII区空腔掩模")
    
    try:
        # 获取中心坐标在图像中的像素位置
        center_x, center_y = wcs.world_to_pixel(center_coord)
        
        # 计算像素尺度
        test_offset = 1.0 / 3600.0  # 1角秒
        # 根据坐标系统类型创建测试坐标
        if center_coord.frame.name == 'galactic':
            test_coord = SkyCoord(l=center_coord.l + test_offset * u.degree,
                                 b=center_coord.b,
                                 frame='galactic')
        else:
            test_coord = SkyCoord(ra=center_coord.ra + test_offset * u.degree,
                                 dec=center_coord.dec,
                                 frame=center_coord.frame)
        test_x, test_y = wcs.world_to_pixel(test_coord)
        pixel_scale = np.sqrt((test_x - center_x)**2 + (test_y - center_y)**2) * 3600  # 像素/角秒
        
        # 计算有效半径（像素）
        r_eff_pixels = r_eff * 3600 / pixel_scale
        logger.info(f"有效半径: {r_eff_pixels:.1f} 像素")
        
        # 创建距离图像
        y, x = np.ogrid[:w4_data.shape[0], :w4_data.shape[1]]
        distance_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)
        
        # 限制搜索区域在2倍有效半径内
        search_mask = distance_from_center <= (2.0 * r_eff_pixels)
        
        # 应用高斯平滑
        w4_smoothed = ndimage.gaussian_filter(w4_data, sigma=smooth_sigma)
        
        # 归一化W4数据
        w4_valid = w4_smoothed[search_mask & (w4_smoothed > 0)]
        if len(w4_valid) > 0:
            w4_min, w4_max = np.min(w4_valid), np.max(w4_valid)
            if w4_max > w4_min:
                w4_normalized = np.zeros_like(w4_smoothed)
                w4_normalized[search_mask] = (w4_smoothed[search_mask] - w4_min) / (w4_max - w4_min)
            else:
                w4_normalized = np.zeros_like(w4_smoothed)
        else:
            logger.warning("没有有效的W4数据，使用几何定义")
            # 使用几何定义（1倍有效半径的圆）
            cavity_mask = distance_from_center <= r_eff_pixels
            return cavity_mask
        
        # 计算W4阈值
        w4_threshold = w4_threshold_factor
        logger.info(f"W4阈值: {w4_threshold:.2f}")
        
        # 创建W4掩模
        w4_mask = w4_normalized > w4_threshold
        w4_mask = w4_mask & search_mask
        
        # 处理W3/W4比值
        # 限制比值范围，避免极值
        ratio_valid = ratio_data[np.isfinite(ratio_data) & (ratio_data > 0) & search_mask]
        if len(ratio_valid) > 0:
            p1, p99 = np.percentile(ratio_valid, [1, 99])
            ratio_clipped = np.clip(ratio_data, p1, p99)
            
            # 归一化比值
            ratio_min, ratio_max = np.min(ratio_valid), np.max(ratio_valid)
            if ratio_max > ratio_min:
                ratio_normalized = np.zeros_like(ratio_data)
                ratio_normalized[np.isfinite(ratio_data) & (ratio_data > 0)] = (
                    (ratio_clipped[np.isfinite(ratio_data) & (ratio_data > 0)] - ratio_min) / (ratio_max - ratio_min)
                )
            else:
                ratio_normalized = np.zeros_like(ratio_data)
            
            # 创建比值掩模（低比值区域）
            ratio_mask = ratio_normalized < ratio_upper_limit
            ratio_mask = ratio_mask & search_mask
        else:
            logger.warning("没有有效的比值数据，仅使用W4数据")
            ratio_mask = np.ones_like(w4_mask)
        
        # 结合W4掩模和比值掩模
        # 使用加权组合
        combined_score = np.zeros_like(w4_normalized)
        combined_score[search_mask] = (
            cavity_weight * w4_normalized[search_mask] + 
            ratio_weight * (1 - ratio_normalized[search_mask])
        )
        
        # 使用阈值创建初始空腔掩模
        combined_threshold = 0.5  # 组合得分阈值
        cavity_mask = combined_score > combined_threshold
        cavity_mask = cavity_mask & search_mask
        
        # 应用形态学操作
        # 闭操作连接相近区域
        cavity_mask = morphology.binary_closing(cavity_mask, morphology.disk(morphology_disk_size))
        # 填充孔洞
        cavity_mask = ndi.binary_fill_holes(cavity_mask)
        
        # 获取连通区域
        labels = measure.label(cavity_mask)
        regions = measure.regionprops(labels)
        
        # 如果有多个连通区域，选择最接近中心的区域
        if len(regions) > 1:
            logger.info(f"发现{len(regions)}个连通区域，选择最接近中心的区域")
            min_dist = float('inf')
            closest_label = 0
            for region in regions:
                y, x = region.centroid
                dist = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                if dist < min_dist:
                    min_dist = dist
                    closest_label = region.label
            cavity_mask = labels == closest_label
        elif len(regions) == 0:
            logger.warning("没有找到连通区域，使用几何定义")
            # 使用几何定义（1倍有效半径的圆）
            cavity_mask = distance_from_center <= r_eff_pixels
        
        # 验证空腔掩模
        cavity_size = np.sum(cavity_mask)
        min_size = int(min_size_factor * np.pi * r_eff_pixels**2)
        
        if cavity_size < min_size:
            logger.warning(f"空腔区域太小 ({cavity_size} < {min_size})，使用几何定义")
            # 使用几何定义（1倍有效半径的圆）
            cavity_mask = distance_from_center <= r_eff_pixels
        
        # 计算空腔中心与HII区中心的距离
        if np.sum(cavity_mask) > 0:
            cavity_props = measure.regionprops(cavity_mask.astype(int))[0]
            cavity_center_y, cavity_center_x = cavity_props.centroid
            cavity_center_dist = np.sqrt((cavity_center_x - center_x)**2 + (cavity_center_y - center_y)**2)
            
            # 如果空腔中心距离HII区中心太远，使用几何定义
            if cavity_center_dist > r_eff_pixels:
                logger.warning(f"空腔中心距离HII区中心太远 ({cavity_center_dist:.1f} > {r_eff_pixels:.1f})，使用几何定义")
                cavity_mask = distance_from_center <= r_eff_pixels
        
        logger.info(f"空腔掩模覆盖{np.sum(cavity_mask)}个像素")
        return cavity_mask
        
    except Exception as e:
        logger.error(f"定义空腔掩模失败: {str(e)}")
        # 出错时使用几何定义
        y, x = np.ogrid[:w4_data.shape[0], :w4_data.shape[1]]
        distance_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)
        cavity_mask = distance_from_center <= r_eff_pixels
        return cavity_mask
