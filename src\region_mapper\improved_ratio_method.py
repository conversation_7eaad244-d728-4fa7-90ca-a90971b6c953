"""
改进的波段比值法PDR检测模块

使用WISE W4(22μm)波段数据和W3(12μm)/W4(22μm)波段比值检测HII区空腔和PDR区域。
首先识别HII区空腔，然后在其周围寻找PDR区域。
"""

import os
import numpy as np
from scipy import ndimage
from scipy import ndimage as ndi
from skimage import measure, morphology
from skimage.transform import resize
from astropy.coordinates import SkyCoord
import astropy.units as u
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import matplotlib.colors as mcolors
from sklearn.cluster import DBSCAN
from sklearn.neighbors import NearestNeighbors

from src.utils.logger import setup_logger
from src.utils.helpers import ensure_directory, load_config
from src.data_manager import load_wise_fits, load_wise_multiband

# 设置日志记录器
logger = setup_logger(name='region_mapper.improved_ratio')

def define_cavity_mask_w4(w4_data, ratio_data, wcs, center_coord, r_eff,
                        w4_threshold_factor=0.7, ratio_upper_limit=0.5,
                        min_size_factor=0.2, smooth_sigma=1.0,
                        cavity_weight=0.7, ratio_weight=0.3,
                        morphology_disk_size=5):
    """
    使用W4波段数据和W3/W4比值定义HII区空腔掩模

    HII区空腔通常在W4波段(22μm)具有较高的强度，同时W3/W4比值较低。
    该方法结合这两个特征自动识别HII区中心空腔区域。

    Args:
        w4_data: W4波段数据
        ratio_data: W3/W4比值数据
        wcs: 世界坐标系
        center_coord: 中心坐标 (SkyCoord对象)
        r_eff: 有效半径（度）
        w4_threshold_factor: W4阈值因子，默认为0.7
        ratio_upper_limit: W3/W4比值上限，默认为0.5
        min_size_factor: 最小空腔大小因子，默认为0.2
        smooth_sigma: 平滑参数，默认为1.0
        cavity_weight: W4波段在空腔检测中的权重，默认为0.7
        ratio_weight: W3/W4比值在空腔检测中的权重，默认为0.3
        morphology_disk_size: 形态学操作磁盘大小，默认为5

    Returns:
        numpy.ndarray: 空腔掩模（布尔数组）
    """
    logger.info(f"使用W4波段数据和W3/W4比值定义HII区空腔掩模")

    try:
        # 获取中心坐标在图像中的像素位置
        center_x, center_y = wcs.world_to_pixel(center_coord)

        # 计算像素尺度
        test_offset = 1.0 / 3600.0  # 1角秒
        # 根据坐标系统类型创建测试坐标
        if center_coord.frame.name == 'galactic':
            test_coord = SkyCoord(l=center_coord.l + test_offset * u.degree,
                                 b=center_coord.b,
                                 frame='galactic')
        else:
            test_coord = SkyCoord(ra=center_coord.ra + test_offset * u.degree,
                                 dec=center_coord.dec,
                                 frame=center_coord.frame)
        test_x, test_y = wcs.world_to_pixel(test_coord)
        pixel_scale = np.sqrt((test_x - center_x)**2 + (test_y - center_y)**2) * 3600  # 像素/角秒

        # 计算有效半径（像素）
        r_eff_pixels = r_eff * 3600 / pixel_scale
        logger.info(f"有效半径: {r_eff_pixels:.1f} 像素")

        # 创建距离图像
        y, x = np.ogrid[:w4_data.shape[0], :w4_data.shape[1]]
        distance_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)

        # 限制搜索区域在2倍有效半径内
        search_mask = distance_from_center <= (2.0 * r_eff_pixels)

        # 应用高斯平滑
        w4_smoothed = ndimage.gaussian_filter(w4_data, sigma=smooth_sigma)

        # 归一化W4数据
        w4_valid = w4_smoothed[search_mask & (w4_smoothed > 0)]
        if len(w4_valid) > 0:
            w4_min, w4_max = np.min(w4_valid), np.max(w4_valid)
            if w4_max > w4_min:
                w4_normalized = np.zeros_like(w4_smoothed)
                w4_normalized[search_mask] = (w4_smoothed[search_mask] - w4_min) / (w4_max - w4_min)
            else:
                w4_normalized = np.zeros_like(w4_smoothed)
        else:
            logger.warning("没有有效的W4数据，使用几何定义")
            # 使用几何定义（1倍有效半径的圆）
            cavity_mask = distance_from_center <= r_eff_pixels
            return cavity_mask

        # 计算W4阈值
        w4_threshold = w4_threshold_factor
        logger.info(f"W4阈值: {w4_threshold:.2f}")

        # 创建W4掩模
        w4_mask = w4_normalized > w4_threshold
        w4_mask = w4_mask & search_mask

        # 处理W3/W4比值
        # 限制比值范围，避免极值
        ratio_valid = ratio_data[np.isfinite(ratio_data) & (ratio_data > 0) & search_mask]
        if len(ratio_valid) > 0:
            p1, p99 = np.percentile(ratio_valid, [1, 99])
            ratio_clipped = np.clip(ratio_data, p1, p99)

            # 归一化比值
            ratio_min, ratio_max = np.min(ratio_valid), np.max(ratio_valid)
            if ratio_max > ratio_min:
                ratio_normalized = np.zeros_like(ratio_data)
                ratio_normalized[np.isfinite(ratio_data) & (ratio_data > 0)] = (
                    (ratio_clipped[np.isfinite(ratio_data) & (ratio_data > 0)] - ratio_min) / (ratio_max - ratio_min)
                )
            else:
                ratio_normalized = np.zeros_like(ratio_data)

            # 创建比值掩模（低比值区域）
            ratio_mask = ratio_normalized < ratio_upper_limit
            ratio_mask = ratio_mask & search_mask
        else:
            logger.warning("没有有效的比值数据，仅使用W4数据")
            ratio_mask = np.ones_like(w4_mask)

        # 结合W4掩模和比值掩模
        # 使用加权组合
        combined_score = np.zeros_like(w4_normalized)
        combined_score[search_mask] = (
            cavity_weight * w4_normalized[search_mask] +
            ratio_weight * (1 - ratio_normalized[search_mask])
        )

        # 使用阈值创建初始空腔掩模
        combined_threshold = 0.5  # 组合得分阈值
        cavity_mask = combined_score > combined_threshold
        cavity_mask = cavity_mask & search_mask

        # 应用形态学操作
        # 闭操作连接相近区域
        cavity_mask = morphology.binary_closing(cavity_mask, morphology.disk(morphology_disk_size))
        # 填充孔洞
        cavity_mask = ndi.binary_fill_holes(cavity_mask)

        # 获取连通区域
        labels = measure.label(cavity_mask)
        regions = measure.regionprops(labels)

        # 如果有多个连通区域，选择最接近中心的区域
        if len(regions) > 1:
            logger.info(f"发现{len(regions)}个连通区域，选择最接近中心的区域")
            min_dist = float('inf')
            closest_label = 0
            for region in regions:
                y, x = region.centroid
                dist = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                if dist < min_dist:
                    min_dist = dist
                    closest_label = region.label
            cavity_mask = labels == closest_label
        elif len(regions) == 0:
            logger.warning("没有找到连通区域，使用几何定义")
            # 使用几何定义（1倍有效半径的圆）
            cavity_mask = distance_from_center <= r_eff_pixels

        # 验证空腔掩模
        cavity_size = np.sum(cavity_mask)
        min_size = int(min_size_factor * np.pi * r_eff_pixels**2)

        if cavity_size < min_size:
            logger.warning(f"空腔区域太小 ({cavity_size} < {min_size})，使用几何定义")
            # 使用几何定义（1倍有效半径的圆）
            cavity_mask = distance_from_center <= r_eff_pixels

        # 计算空腔中心与HII区中心的距离
        if np.sum(cavity_mask) > 0:
            cavity_props = measure.regionprops(cavity_mask.astype(int))[0]
            cavity_center_y, cavity_center_x = cavity_props.centroid
            cavity_center_dist = np.sqrt((cavity_center_x - center_x)**2 + (cavity_center_y - center_y)**2)

            # 如果空腔中心距离HII区中心太远，使用几何定义
            if cavity_center_dist > r_eff_pixels:
                logger.warning(f"空腔中心距离HII区中心太远 ({cavity_center_dist:.1f} > {r_eff_pixels:.1f})，使用几何定义")
                cavity_mask = distance_from_center <= r_eff_pixels

        logger.info(f"空腔掩模覆盖{np.sum(cavity_mask)}个像素")
        return cavity_mask

    except Exception as e:
        logger.error(f"定义空腔掩模失败: {str(e)}")
        # 出错时使用几何定义
        y, x = np.ogrid[:w4_data.shape[0], :w4_data.shape[1]]
        distance_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)
        cavity_mask = distance_from_center <= r_eff_pixels
        return cavity_mask

def define_pdr_mask_improved(source_name, wcs, center_coord, r_eff,
                           search_radius_factor=3.0, smooth_sigma=1.0,
                           wise_base_path=None, config_path='config/default_config.yaml',
                           output_dir=None, save_ratio_plot=False):
    """
    使用改进的W3/W4波段比值方法定义PDR区域掩模

    首先识别HII区空腔，然后在其周围寻找PDR区域。
    PDR区域通常在中红外波段具有特定的光谱特征，W3/W4比值可以帮助识别这些区域。
    该方法加载W3和W4波段数据，计算比值，并使用聚类方法自动确定PDR区域。

    Args:
        source_name: 源名称
        wcs: 世界坐标系
        center_coord: 中心坐标 (SkyCoord对象)
        r_eff: 有效半径（度）
        search_radius_factor: PDR搜索半径因子（相对于r_eff），默认为3.0
        smooth_sigma: 平滑参数，默认为1.0
        wise_base_path: WISE数据基础路径，如果为None则从配置文件读取
        config_path: 配置文件路径
        output_dir: 输出目录，用于保存比值图像
        save_ratio_plot: 是否保存比值图像，默认为False

    Returns:
        tuple: (PDR掩模（布尔数组）, 空腔掩模（布尔数组）, W3数据, W4数据)
    """
    logger.info(f"使用改进的W3/W4波段比值方法定义PDR掩模，搜索半径因子={search_radius_factor}")

    try:
        # 加载配置
        config = load_config(config_path)
        pdr_params = config.get('processing', {}).get('region_mapping', {}).get('pdr', {})

        # 获取改进方法参数
        w4_threshold_factor = pdr_params.get('w4_threshold_factor', 0.7)
        ratio_upper_limit = pdr_params.get('ratio_upper_limit', 0.5)
        min_cavity_size_factor = pdr_params.get('min_cavity_size_factor', 0.2)
        cavity_weight = pdr_params.get('cavity_weight', 0.7)
        ratio_weight = pdr_params.get('ratio_weight', 0.3)
        morphology_disk_size = pdr_params.get('morphology_disk_size', 5)

        # 加载W3和W4波段数据
        logger.info(f"加载源{source_name}的W3(12μm)和W4(22μm)波段数据")
        wise_data, wise_wcs = load_wise_multiband(
            source_name, wise_base_path, config_path, bands=('12', '22'))

        # 检查是否成功加载了两个波段
        if 'w3' not in wise_data or 'w4' not in wise_data or wise_data['w3'] is None or wise_data['w4'] is None:
            logger.error("未能加载W3或W4波段数据，无法计算比值")
            # 返回空掩模
            if 'w3' in wise_data and wise_data['w3'] is not None:
                empty_mask = np.zeros_like(wise_data['w3'], dtype=bool)
                return empty_mask, empty_mask, wise_data['w3'], None
            elif 'w4' in wise_data and wise_data['w4'] is not None:
                empty_mask = np.zeros_like(wise_data['w4'], dtype=bool)
                return empty_mask, empty_mask, None, wise_data['w4']
            else:
                # 如果两个波段都没有，尝试加载W3波段作为参考
                w3_image, _, _ = load_wise_fits(source_name, wise_base_path, config_path, band='12')
                empty_mask = np.zeros_like(w3_image, dtype=bool)
                return empty_mask, empty_mask, None, None

        # 获取W3和W4波段数据
        w3_data = wise_data['w3'].copy()
        w4_data = wise_data['w4'].copy()

        # 处理NaN值
        w3_data = np.nan_to_num(w3_data, nan=0.0)
        w4_data = np.nan_to_num(w4_data, nan=0.0)

        # 确保两个波段的尺寸一致（使用W3波段的尺寸作为标准）
        if w3_data.shape != w4_data.shape:
            logger.info(f"调整W4波段数据从{w4_data.shape}到{w3_data.shape}")
            w4_data = resize(w4_data, w3_data.shape, mode='reflect', anti_aliasing=True, order=3)

        # 应用高斯平滑
        w3_smoothed = ndimage.gaussian_filter(w3_data, sigma=smooth_sigma)
        w4_smoothed = ndimage.gaussian_filter(w4_data, sigma=smooth_sigma)

        # 计算W3/W4比值
        # 避免除以零，设置一个小的阈值
        epsilon = np.finfo(float).eps
        ratio_mask = w4_smoothed > epsilon
        ratio = np.zeros_like(w3_smoothed)
        ratio[ratio_mask] = w3_smoothed[ratio_mask] / w4_smoothed[ratio_mask]

        # 限制比值范围，避免极值
        ratio_valid = ratio[ratio_mask]
        if len(ratio_valid) > 0:
            p1, p99 = np.percentile(ratio_valid, [1, 99])
            logger.info(f"W3/W4比值范围: {p1:.2f}-{p99:.2f}，均值: {np.mean(ratio_valid):.2f}")
            # 限制极值
            ratio[ratio < p1] = p1
            ratio[ratio > p99] = p99
        else:
            logger.warning("没有有效的W3/W4比值数据")
            empty_mask = np.zeros_like(w3_data, dtype=bool)
            return empty_mask, empty_mask, w3_smoothed, w4_smoothed

        # 获取中心坐标在图像中的像素位置
        center_x, center_y = wcs.world_to_pixel(center_coord)

        # 计算像素尺度
        test_offset = 1.0 / 3600.0  # 1角秒
        # 根据坐标系统类型创建测试坐标
        if center_coord.frame.name == 'galactic':
            test_coord = SkyCoord(l=center_coord.l + test_offset * u.degree,
                                 b=center_coord.b,
                                 frame='galactic')
        else:
            test_coord = SkyCoord(ra=center_coord.ra + test_offset * u.degree,
                                 dec=center_coord.dec,
                                 frame=center_coord.frame)
        test_x, test_y = wcs.world_to_pixel(test_coord)
        pixel_scale = np.sqrt((test_x - center_x)**2 + (test_y - center_y)**2) * 3600  # 像素/角秒
        logger.info(f"像素尺度: {pixel_scale:.2f} 像素/角秒")

        # 计算PDR搜索半径（默认为3R）
        r_eff_pixels = r_eff * 3600 / pixel_scale  # 转换为像素
        pdr_search_radius_pixels = r_eff * search_radius_factor * 3600 / pixel_scale  # 转换为像素
        # 确保最小半径为10像素
        pdr_search_radius_pixels = max(10, pdr_search_radius_pixels)
        logger.info(f"有效半径: {r_eff_pixels:.1f} 像素")
        logger.info(f"PDR搜索半径 ({search_radius_factor}R): {pdr_search_radius_pixels:.1f} 像素")

        # 创建距离图像
        y, x = np.ogrid[:ratio.shape[0], :ratio.shape[1]]
        distance_from_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)

        # 限制PDR搜索区域在指定半径内
        pdr_search_mask = distance_from_center <= pdr_search_radius_pixels

        # 步骤1：首先识别HII区空腔
        logger.info("步骤1：识别HII区空腔")
        cavity_mask = define_cavity_mask_w4(
            w4_smoothed, ratio, wcs, center_coord, r_eff,
            w4_threshold_factor, ratio_upper_limit,
            min_cavity_size_factor, smooth_sigma,
            cavity_weight, ratio_weight,
            morphology_disk_size
        )

        # 步骤2：在空腔周围寻找PDR区域
        logger.info("步骤2：在空腔周围寻找PDR区域")

        # 准备数据：只考虑搜索区域内的有效比值
        valid_mask = pdr_search_mask & ratio_mask & np.isfinite(ratio) & ~cavity_mask
        valid_ratio = ratio[valid_mask]
        valid_positions = np.array(np.where(valid_mask)).T

        if len(valid_ratio) < 100:
            logger.warning(f"有效比值数据太少 ({len(valid_ratio)} < 100)，使用备用方法")
            # 使用简单阈值作为备用方法
            w3_mean = np.mean(w3_smoothed[w3_smoothed > 0])
            w3_std = np.std(w3_smoothed[w3_smoothed > 0])
            threshold = w3_mean + 0.5 * w3_std
            pdr_mask = (w3_smoothed >= threshold) & pdr_search_mask & ~cavity_mask
            pdr_mask = morphology.binary_closing(pdr_mask, morphology.disk(morphology_disk_size))
            pdr_mask = ndi.binary_fill_holes(pdr_mask)
            logger.info(f"备用方法后PDR掩模覆盖{np.sum(pdr_mask)}个像素")
        else:
            # 准备聚类数据：W3强度、W4强度、W3/W4比值和位置信息
            # 提取有效像素的W3和W4强度
            valid_w3 = w3_smoothed[valid_mask]
            valid_w4 = w4_smoothed[valid_mask]

            # 归一化W3和W4强度
            if np.max(valid_w3) > np.min(valid_w3):
                w3_normalized = (valid_w3 - np.min(valid_w3)) / (np.max(valid_w3) - np.min(valid_w3))
            else:
                w3_normalized = np.zeros_like(valid_w3)

            if np.max(valid_w4) > np.min(valid_w4):
                w4_normalized = (valid_w4 - np.min(valid_w4)) / (np.max(valid_w4) - np.min(valid_w4))
            else:
                w4_normalized = np.zeros_like(valid_w4)

            # 将位置信息归一化，使其与强度和比值具有相似的尺度
            positions_scaled = valid_positions.copy()
            positions_scaled[:, 0] = (positions_scaled[:, 0] - center_y) / pdr_search_radius_pixels
            positions_scaled[:, 1] = (positions_scaled[:, 1] - center_x) / pdr_search_radius_pixels

            # 组合所有特征：W3强度、W4强度、W3/W4比值和位置信息
            X = np.column_stack([w3_normalized, w4_normalized, valid_ratio, positions_scaled])

            logger.info(f"聚类特征包含: W3强度, W4强度, W3/W4比值, 位置信息")

            # 使用DBSCAN聚类
            logger.info("使用DBSCAN聚类算法")

            # 计算min_samples参数（总样本数的2%，但不少于5个样本）
            min_samples = max(5, int(0.02 * len(X)))
            logger.info(f"DBSCAN min_samples参数: {min_samples} (总样本数的2%)")

            # 自适应确定eps参数
            # 使用k-距离图的拐点
            k = min(min_samples, len(X) - 1)  # 确保k不超过样本数
            nbrs = NearestNeighbors(n_neighbors=k).fit(X)
            distances, _ = nbrs.kneighbors(X)
            # 获取每个点到其第k个最近邻居的距离
            k_distances = distances[:, -1]
            # 排序距离
            k_distances_sorted = np.sort(k_distances)

            # 尝试找到拐点（距离突然增大的点）
            # 计算距离的一阶差分
            diffs = np.diff(k_distances_sorted)
            # 找到差分最大的点的索引
            knee_idx = np.argmax(diffs) + 1  # +1是因为diff减少了一个元素
            # 使用拐点作为eps
            eps_knee = k_distances_sorted[knee_idx]

            # 使用拐点eps进行聚类
            dbscan = DBSCAN(eps=eps_knee, min_samples=min_samples).fit(X)
            labels = dbscan.labels_
            n_clusters = len(set(labels)) - (1 if -1 in labels else 0)  # 不计算噪声
            logger.info(f"使用拐点eps={eps_knee:.4f}，找到{n_clusters}个聚类，噪声点比例={np.sum(labels == -1) / len(labels):.2%}")

            # 如果聚类数太少或太多，尝试调整eps
            if n_clusters < 2 or n_clusters > 10:
                # 使用距离分布的百分位数
                percentiles = [10, 15, 20, 25, 30]
                eps_values = [np.percentile(k_distances, p) for p in percentiles]

                # 记录每个eps值
                logger.info(f"DBSCAN eps候选值: 拐点={eps_knee:.4f}, 百分位数={[f'{p}%={eps:.4f}' for p, eps in zip(percentiles, eps_values)]}")

                # 尝试不同的eps值，选择产生合适聚类数的那个
                best_eps = None
                best_n_clusters = 0
                best_labels = None
                target_n_clusters = 3  # 目标聚类数（不包括噪声）

                for p, eps in zip(percentiles, eps_values):
                    dbscan = DBSCAN(eps=eps, min_samples=min_samples).fit(X)
                    labels = dbscan.labels_
                    n_clusters = len(set(labels)) - (1 if -1 in labels else 0)  # 不计算噪声
                    noise_ratio = np.sum(labels == -1) / len(labels)
                    logger.info(f"使用{p}%分位数eps={eps:.4f}，找到{n_clusters}个聚类，噪声点比例={noise_ratio:.2%}")

                    # 如果聚类数在合理范围内，且噪声点比例不太高
                    if 2 <= n_clusters <= 10 and noise_ratio < 0.5:
                        # 如果这个eps值产生的聚类数更接近目标聚类数，或者是第一个合适的eps值
                        if best_eps is None or abs(n_clusters - target_n_clusters) < abs(best_n_clusters - target_n_clusters):
                            best_eps = eps
                            best_n_clusters = n_clusters
                            best_labels = labels

                # 如果找到了更好的eps值，使用它
                if best_eps is not None:
                    logger.info(f"使用最佳eps值: {best_eps:.4f}，找到{best_n_clusters}个聚类")
                    labels = best_labels
                    n_clusters = best_n_clusters

            # 分析各聚类的特征
            unique_labels = np.unique(labels)
            cluster_stats = []

            # 计算每个聚类的特征
            for label in unique_labels:
                # 跳过噪声点（标签为-1）
                if label == -1:
                    noise_mask = labels == -1
                    noise_ratio = np.sum(noise_mask) / len(labels)
                    logger.info(f"噪声点: {np.sum(noise_mask)}个 ({noise_ratio:.2%})")
                    continue

                cluster_mask = labels == label
                cluster_ratio = valid_ratio[cluster_mask]
                cluster_w3 = valid_w3[cluster_mask]
                cluster_w4 = valid_w4[cluster_mask]
                cluster_positions = valid_positions[cluster_mask]

                # 计算聚类的统计特征
                cluster_ratio_mean = np.mean(cluster_ratio)
                cluster_ratio_std = np.std(cluster_ratio)
                cluster_w3_mean = np.mean(cluster_w3)
                cluster_w3_std = np.std(cluster_w3)
                cluster_w4_mean = np.mean(cluster_w4)
                cluster_w4_std = np.std(cluster_w4)
                cluster_size = np.sum(cluster_mask)

                # 计算聚类中心到HII区中心的平均距离
                cluster_distances = np.sqrt(
                    ((cluster_positions[:, 0] - center_y) ** 2) +
                    ((cluster_positions[:, 1] - center_x) ** 2)
                )
                cluster_mean_distance = np.mean(cluster_distances)

                # 计算聚类到空腔边缘的最小距离
                # 首先获取空腔边缘像素
                cavity_edge = morphology.binary_dilation(cavity_mask) & ~cavity_mask
                cavity_edge_positions = np.array(np.where(cavity_edge)).T

                # 如果有空腔边缘像素，计算到边缘的最小距离
                if len(cavity_edge_positions) > 0:
                    min_distances = []
                    for pos in cluster_positions:
                        # 计算到所有边缘像素的距离
                        distances = np.sqrt(np.sum((cavity_edge_positions - pos) ** 2, axis=1))
                        # 取最小距离
                        min_distances.append(np.min(distances))
                    cluster_min_edge_distance = np.mean(min_distances)
                else:
                    # 如果没有边缘像素，使用到中心的距离减去有效半径
                    cluster_min_edge_distance = max(0, cluster_mean_distance - r_eff_pixels)

                # 存储聚类统计信息
                cluster_stats.append({
                    'id': label,
                    'ratio_mean': cluster_ratio_mean,
                    'ratio_std': cluster_ratio_std,
                    'w3_mean': cluster_w3_mean,
                    'w3_std': cluster_w3_std,
                    'w4_mean': cluster_w4_mean,
                    'w4_std': cluster_w4_std,
                    'size': cluster_size,
                    'mean_distance': cluster_mean_distance,
                    'min_edge_distance': cluster_min_edge_distance
                })

                logger.info(f"聚类 {label}: 平均比值={cluster_ratio_mean:.2f}±{cluster_ratio_std:.2f}, "
                           f"W3强度={cluster_w3_mean:.2f}±{cluster_w3_std:.2f}, "
                           f"W4强度={cluster_w4_mean:.2f}±{cluster_w4_std:.2f}, "
                           f"大小={cluster_size}, 平均距离={cluster_mean_distance:.1f}, "
                           f"到空腔边缘最小距离={cluster_min_edge_distance:.1f}")

            # 如果没有找到任何聚类，使用备用方法
            if len(cluster_stats) == 0:
                logger.warning("没有找到任何聚类，使用备用方法")
                # 使用简单阈值作为备用方法
                w3_mean = np.mean(w3_smoothed[w3_smoothed > 0])
                w3_std = np.std(w3_smoothed[w3_smoothed > 0])
                threshold = w3_mean + 0.5 * w3_std
                pdr_mask = (w3_smoothed >= threshold) & pdr_search_mask & ~cavity_mask
                pdr_mask = morphology.binary_closing(pdr_mask, morphology.disk(morphology_disk_size))
                pdr_mask = ndi.binary_fill_holes(pdr_mask)
                logger.info(f"备用方法后PDR掩模覆盖{np.sum(pdr_mask)}个像素")
            else:
                # 计算全局统计量，用于归一化
                all_w3_mean = np.mean(valid_w3)
                all_w3_std = np.std(valid_w3)
                all_ratio_mean = np.mean(valid_ratio)
                all_ratio_std = np.std(valid_ratio)

                # 为每个聚类计算PDR得分
                for cluster in cluster_stats:
                    # 归一化W3强度和比值
                    w3_z = (cluster['w3_mean'] - all_w3_mean) / all_w3_std if all_w3_std > 0 else 0
                    ratio_z = (cluster['ratio_mean'] - all_ratio_mean) / all_ratio_std if all_ratio_std > 0 else 0

                    # PDR得分：高比值、中等W3强度、靠近空腔边缘的聚类得分高
                    # 比值越高越好
                    ratio_score = ratio_z
                    # W3强度应该适中（不太高也不太低）
                    w3_score = 1.0 - abs(w3_z)
                    # 距离应该适中（不太近也不太远）
                    edge_distance_factor = cluster['min_edge_distance'] / (0.5 * r_eff_pixels)
                    edge_distance_score = max(0, 1.0 - edge_distance_factor)  # 越近越好，但不能为负

                    # 综合得分（加权平均）
                    cluster['pdr_score'] = 0.5 * ratio_score + 0.3 * w3_score + 0.2 * edge_distance_score

                    logger.info(f"聚类 {cluster['id']} PDR得分: {cluster['pdr_score']:.2f} "
                               f"(比值得分={ratio_score:.2f}, W3得分={w3_score:.2f}, 边缘距离得分={edge_distance_score:.2f})")

                # 按PDR得分排序
                cluster_stats.sort(key=lambda x: x['pdr_score'], reverse=True)

                # 选择PDR得分最高的几个聚类作为PDR区域
                # 首先选择得分最高的聚类
                pdr_cluster_ids = [cluster_stats[0]['id']]
                logger.info(f"选择聚类 {pdr_cluster_ids[0]} 作为主要PDR区域，PDR得分={cluster_stats[0]['pdr_score']:.2f}")

                # 然后选择其他得分较高且靠近空腔的聚类
                for cluster in cluster_stats[1:]:
                    # 如果得分大于0且距离空腔边缘较近，也选择它
                    if cluster['pdr_score'] > 0 and cluster['min_edge_distance'] < r_eff_pixels:
                        pdr_cluster_ids.append(cluster['id'])
                        logger.info(f"额外选择聚类 {cluster['id']} 作为PDR区域，PDR得分={cluster['pdr_score']:.2f}")

                # 创建初始PDR掩模
                pdr_mask = np.zeros_like(ratio, dtype=bool)
                for cluster_id in pdr_cluster_ids:
                    cluster_mask = labels == cluster_id
                    pdr_mask[valid_mask] = pdr_mask[valid_mask] | cluster_mask

                # 应用形态学操作，连接相近区域并填充孔洞
                pdr_mask = morphology.binary_closing(pdr_mask, morphology.disk(morphology_disk_size))
                pdr_mask = morphology.binary_dilation(pdr_mask, morphology.disk(2))  # 略微扩大PDR区域
                pdr_mask = ndi.binary_fill_holes(pdr_mask)

                # 确保PDR区域不与空腔重叠
                pdr_mask = pdr_mask & ~cavity_mask

                # 检查PDR区域是否围绕空腔
                # 如果PDR区域不连续或不围绕空腔，尝试添加更多聚类
                if len(measure.regionprops(measure.label(pdr_mask))) > 1:
                    logger.info("PDR区域不连续，尝试连接分离的区域")
                    # 使用更大的结构元素进行闭操作
                    pdr_mask = morphology.binary_closing(pdr_mask, morphology.disk(morphology_disk_size * 2))
                    pdr_mask = ndi.binary_fill_holes(pdr_mask)
                    pdr_mask = pdr_mask & ~cavity_mask

                logger.info(f"最终PDR掩模覆盖{np.sum(pdr_mask)}个像素")

        # 如果需要保存比值图像
        if save_ratio_plot and output_dir:
            save_improved_ratio_plot(
                ratio, w3_smoothed, w4_smoothed, pdr_mask, cavity_mask,
                center_x, center_y, r_eff_pixels, pdr_search_radius_pixels,
                source_name, output_dir
            )

        logger.info(f"改进的W3/W4波段比值方法PDR掩模和空腔掩模创建完成")
        return pdr_mask, cavity_mask, w3_smoothed, w4_smoothed

    except Exception as e:
        logger.error(f"使用改进的W3/W4波段比值方法定义PDR掩模失败: {str(e)}")
        # 出错时返回空掩模和空数据
        empty_array = np.zeros((100, 100), dtype=bool)
        return empty_array, empty_array, empty_array, empty_array  # 返回默认大小的空掩模和空数据

def save_improved_ratio_plot(ratio_data, w3_data, w4_data, pdr_mask, cavity_mask,
                         center_x, center_y, r_eff_pixels, pdr_search_radius_pixels,
                         source_name, output_dir):
    """
    保存改进的W3/W4比值图像

    Args:
        ratio_data: W3/W4比值数据
        w3_data: W3波段数据
        w4_data: W4波段数据
        pdr_mask: PDR掩模
        cavity_mask: 空腔掩模
        center_x: 中心X坐标
        center_y: 中心Y坐标
        r_eff_pixels: 有效半径（像素）
        pdr_search_radius_pixels: PDR搜索半径（像素）
        source_name: 源名称
        output_dir: 输出目录
    """
    try:
        # 创建可视化目录
        vis_dir = os.path.join(output_dir, 'visualizations')
        ensure_directory(vis_dir)

        # 设置图像路径
        ratio_plot_path = os.path.join(vis_dir, f"{source_name}_improved_w3w4_ratio.png")

        # 创建图像
        plt.figure(figsize=(15, 12))

        # 绘制W3数据
        plt.subplot(231)
        plt.title("W3 (12μm)")
        if w3_data is not None:
            w3_display = np.log1p(w3_data)
            w3_display = w3_display / np.percentile(w3_display[w3_display > 0], 99.5) if np.any(w3_display > 0) else w3_display
            im = plt.imshow(w3_display, origin='lower', cmap='inferno')
            plt.colorbar(im, label='log(W3+1) [normalized]')
        else:
            plt.text(0.5, 0.5, "W3 data not available", ha='center', va='center', transform=plt.gca().transAxes)
        plt.plot(center_x, center_y, 'c+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False, color='c', linestyle='--')
        plt.gca().add_patch(circle)

        # 绘制W4数据
        plt.subplot(232)
        plt.title("W4 (22μm)")
        if w4_data is not None:
            w4_display = np.log1p(w4_data)
            w4_display = w4_display / np.percentile(w4_display[w4_display > 0], 99.5) if np.any(w4_display > 0) else w4_display
            im = plt.imshow(w4_display, origin='lower', cmap='inferno')
            plt.colorbar(im, label='log(W4+1) [normalized]')
        else:
            plt.text(0.5, 0.5, "W4 data not available", ha='center', va='center', transform=plt.gca().transAxes)
        plt.plot(center_x, center_y, 'c+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False, color='c', linestyle='--')
        plt.gca().add_patch(circle)

        # 绘制比值图像
        plt.subplot(233)
        plt.title("W3/W4 Ratio")
        im = plt.imshow(ratio_data, origin='lower', cmap='viridis')
        plt.colorbar(im, label='W3/W4 Ratio')
        plt.plot(center_x, center_y, 'r+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False, color='r', linestyle='--')
        plt.gca().add_patch(circle)
        circle = plt.Circle((center_x, center_y), pdr_search_radius_pixels, fill=False, color='white')
        plt.gca().add_patch(circle)

        # 绘制空腔掩模
        plt.subplot(234)
        plt.title("Cavity Mask")
        plt.imshow(cavity_mask, origin='lower', cmap='gray')
        plt.plot(center_x, center_y, 'r+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False, color='r', linestyle='--')
        plt.gca().add_patch(circle)

        # 绘制PDR掩模
        plt.subplot(235)
        plt.title("PDR Mask")
        plt.imshow(pdr_mask, origin='lower', cmap='gray')
        plt.plot(center_x, center_y, 'r+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False, color='r', linestyle='--')
        plt.gca().add_patch(circle)

        # 绘制组合图像
        plt.subplot(236)
        plt.title("Combined Regions")
        # 创建RGB图像：红色=空腔，绿色=PDR
        combined = np.zeros((*cavity_mask.shape, 3))
        combined[..., 0] = cavity_mask  # 红色通道 = 空腔
        combined[..., 1] = pdr_mask     # 绿色通道 = PDR
        plt.imshow(combined, origin='lower')
        plt.plot(center_x, center_y, 'w+', markersize=10)
        circle = plt.Circle((center_x, center_y), r_eff_pixels, fill=False, color='w', linestyle='--')
        plt.gca().add_patch(circle)

        # 添加图例
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='red', edgecolor='w', label='Cavity'),
            Patch(facecolor='green', edgecolor='w', label='PDR')
        ]
        plt.legend(handles=legend_elements, loc='upper right')

        # 调整布局并保存图像
        plt.tight_layout()
        plt.savefig(ratio_plot_path, dpi=300)
        plt.close()

        logger.info(f"成功保存改进的W3/W4比值图像到: {ratio_plot_path}")

    except Exception as e:
        logger.error(f"保存改进的W3/W4比值图像失败: {str(e)}")
