"""
测试标准天文流程PDR区域检测方法

该脚本测试使用标准天文数据处理流程的PDR区域检测方法，
包括9个详细步骤的完整实现。
"""

import os
import sys
import argparse
import numpy as np
import matplotlib.pyplot as plt
from astropy.coordinates import SkyCoord
import astropy.units as u
from skimage import measure

from src.utils.logger import setup_logger
from src.utils.helpers import load_config, ensure_directory
from src.region_mapper.base import get_region_masks
from src.region_mapper.standard_astro_method import define_pdr_mask_standard_astro

# 设置日志记录器
logger = setup_logger(name='test_standard_astro_pdr_detection')

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='测试标准天文流程PDR区域检测方法')
    parser.add_argument('--source', type=str, default='G010.964+00.006',
                        help='源名称，默认为G010.964+00.006')
    parser.add_argument('--config', type=str, default='config/default_config.yaml',
                        help='配置文件路径，默认为config/default_config.yaml')
    parser.add_argument('--output-dir', type=str, default='data/output',
                        help='输出目录，默认为data/output')
    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 获取源信息
    source_name = args.source
    logger.info(f"测试源: {source_name}")
    
    # 创建输出目录
    output_dir = args.output_dir
    ensure_directory(output_dir)
    source_output_dir = os.path.join(output_dir, source_name)
    ensure_directory(source_output_dir)
    
    try:
        # 加载源信息
        # 这里简化处理，直接使用源名称和默认有效半径
        r_eff_arcmin = 1.0  # 默认有效半径（角分）
        r_eff_deg = r_eff_arcmin / 60.0  # 转换为度
        
        # 从源名称解析坐标
        # 假设源名称格式为"GLLL.lll+BB.bbb"
        try:
            l_str, b_str = source_name[1:].split('+')
            if '-' in l_str:
                l_str, b_str = source_name[1:].split('-')
                b_str = '-' + b_str
            l = float(l_str)
            b = float(b_str)
            center_coord = SkyCoord(l=l*u.degree, b=b*u.degree, frame='galactic')
            logger.info(f"从源名称解析坐标: l={l}, b={b}")
        except Exception as e:
            logger.error(f"无法从源名称解析坐标: {str(e)}")
            # 使用默认坐标
            center_coord = SkyCoord(l=0.0*u.degree, b=0.0*u.degree, frame='galactic')
            logger.warning(f"使用默认坐标: l=0.0, b=0.0")
        
        # 使用标准天文流程PDR区域检测方法
        logger.info("使用标准天文流程方法定义PDR掩模和HII区空腔")
        
        # 直接调用标准天文流程方法
        pdr_mask, cavity_mask, external_mask = define_pdr_mask_standard_astro(
            source_name, None, center_coord, r_eff_deg,
            config_path=args.config,
            output_dir=source_output_dir, save_plots=True
        )
        
        # 计算PDR、空腔和外部区域的特征
        pdr_pixels = np.sum(pdr_mask)
        cavity_pixels = np.sum(cavity_mask)
        external_pixels = np.sum(external_mask)
        
        # 计算PDR区域的形状特征
        pdr_labels = measure.label(pdr_mask)
        pdr_regions = measure.regionprops(pdr_labels)
        
        # 计算空腔区域的形状特征
        cavity_labels = measure.label(cavity_mask)
        cavity_regions = measure.regionprops(cavity_labels)
        
        # 计算外部区域的形状特征
        external_labels = measure.label(external_mask)
        external_regions = measure.regionprops(external_labels)
        
        # 输出结果
        logger.info(f"PDR区域覆盖{pdr_pixels}个像素，包含{len(pdr_regions)}个连通区域")
        logger.info(f"空腔区域覆盖{cavity_pixels}个像素，包含{len(cavity_regions)}个连通区域")
        logger.info(f"外部区域覆盖{external_pixels}个像素，包含{len(external_regions)}个连通区域")
        
        # 如果有多个PDR区域，输出每个区域的特征
        if len(pdr_regions) > 0:
            for i, region in enumerate(pdr_regions):
                logger.info(f"PDR区域{i+1}: 面积={region.area}像素, "
                           f"周长={region.perimeter:.1f}像素, "
                           f"离心率={region.eccentricity:.2f}")
        
        # 如果有多个空腔区域，输出每个区域的特征
        if len(cavity_regions) > 0:
            for i, region in enumerate(cavity_regions):
                logger.info(f"空腔区域{i+1}: 面积={region.area}像素, "
                           f"周长={region.perimeter:.1f}像素, "
                           f"离心率={region.eccentricity:.2f}")
        
        # 如果有多个外部区域，输出每个区域的特征
        if len(external_regions) > 0:
            for i, region in enumerate(external_regions):
                logger.info(f"外部区域{i+1}: 面积={region.area}像素, "
                           f"周长={region.perimeter:.1f}像素, "
                           f"离心率={region.eccentricity:.2f}")
        
        # 验证掩模互斥性
        overlap_pdr_cavity = np.sum(pdr_mask & cavity_mask)
        overlap_pdr_external = np.sum(pdr_mask & external_mask)
        overlap_cavity_external = np.sum(cavity_mask & external_mask)
        
        logger.info(f"掩模重叠检查:")
        logger.info(f"  PDR-Cavity重叠: {overlap_pdr_cavity} 像素")
        logger.info(f"  PDR-External重叠: {overlap_pdr_external} 像素")
        logger.info(f"  Cavity-External重叠: {overlap_cavity_external} 像素")
        
        if overlap_pdr_cavity == 0 and overlap_pdr_external == 0 and overlap_cavity_external == 0:
            logger.info("掩模互斥性验证通过")
        else:
            logger.warning("掩模存在重叠，需要检查算法")
        
        # 计算覆盖率
        total_pixels = pdr_pixels + cavity_pixels + external_pixels
        image_size = pdr_mask.shape[0] * pdr_mask.shape[1]
        coverage_rate = total_pixels / image_size * 100
        
        logger.info(f"总覆盖像素: {total_pixels}")
        logger.info(f"图像总像素: {image_size}")
        logger.info(f"覆盖率: {coverage_rate:.2f}%")
        
        logger.info("标准天文流程PDR检测测试完成")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
