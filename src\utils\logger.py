"""
日志配置模块

提供项目中使用的日志功能，支持不同级别的日志记录和文件轮转。
"""

import os
import logging
import yaml
from logging.handlers import RotatingFileHandler
from datetime import datetime

def setup_logger(config_path='config/default_config.yaml', name=None):
    """
    设置日志记录器
    
    Args:
        config_path: 配置文件路径
        name: 日志记录器名称，默认为root
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 加载配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    log_config = config['logging']
    log_dir = config['output_paths']['logs']
    
    # 确保日志目录存在
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, log_config['level']))
    
    # 清除现有处理器
    if logger.handlers:
        logger.handlers.clear()
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, log_config['level']))
    console_formatter = logging.Formatter(log_config['format'])
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # 创建文件处理器
    log_file = os.path.join(log_dir, f"{datetime.now().strftime('%Y%m%d')}_{name or 'main'}.log")
    file_handler = RotatingFileHandler(
        log_file, 
        maxBytes=10*1024*1024,  # 10MB
        backupCount=log_config['file_rotation']
    )
    file_handler.setLevel(getattr(logging, log_config['level']))
    file_formatter = logging.Formatter(log_config['format'])
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)
    
    return logger
