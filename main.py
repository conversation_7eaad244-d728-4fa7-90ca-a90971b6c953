"""
HII区分子云距离估计项目主程序

用于估计HII区域宿主分子云的距离，通过分析不同区域（空腔、PDR、外部）
恒星的消光-距离关系。
"""

import os
import sys
import argparse
import yaml
from datetime import datetime
import numpy as np
from astropy.table import Table
from astropy.coordinates import SkyCoord
import astropy.units as u
from astropy.io import fits
import matplotlib.pyplot as plt

# 添加src目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.logger import setup_logger
from src.utils.helpers import load_config, ensure_directory
from src.data_manager import (
    load_hii_region_catalog, load_gaia_data, load_wise_fits,
    load_wise_multiband, cross_match_catalogs, save_processed_data
)
from src.region_mapper import (
    process_wise_image, define_pdr_mask, define_cavity_mask,
    define_external_mask, assign_region_tags, save_region_masks
)
from src.star_selector import (
    apply_quality_cuts, flag_ysos_wise, filter_star_sample
)
from src.extinction_estimator import compute_av_per_star
from src.distance_analyzer import (
    analyze_all_regions, estimate_cloud_distance, save_analysis_results
)
from src.visualizer import (
    plot_wise_with_regions_and_stars,
    plot_extinction_distance,
    plot_wise_rgb
)


# 设置日志记录器
logger = setup_logger(name='main')

def parse_arguments():
    """
    解析命令行参数

    Returns:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='估计HII区域宿主分子云的距离')

    parser.add_argument('--source', type=str, required=True,
                       help='HII区域源名称')

    parser.add_argument('--config', type=str, default='config/default_config.yaml',
                       help='配置文件路径')

    parser.add_argument('--output-dir', type=str,
                       help='输出目录，如果不指定则使用配置文件中的设置')

    parser.add_argument('--ra', type=float,
                       help='赤经（度），如果不指定则从源表中查找')

    parser.add_argument('--dec', type=float,
                       help='赤纬（度），如果不指定则从源表中查找')

    parser.add_argument('--radius', type=float,
                       help='有效半径（度），如果不指定则从源表中查找')

    parser.add_argument('--skip-steps', type=str, nargs='+',
                       choices=['data_loading', 'region_mapping', 'star_selection',
                               'extinction_calculation', 'distance_analysis'],
                       help='跳过指定的处理步骤')

    return parser.parse_args()

def load_source_info(source_name, args, config):
    """
    加载源信息

    Args:
        source_name: 源名称
        args: 命令行参数
        config: 配置字典

    Returns:
        dict: 源信息
    """
    logger.info(f"加载源{source_name}的信息")

    # 如果命令行参数中提供了坐标和半径，直接使用
    if args.ra is not None and args.dec is not None and args.radius is not None:
        source_info = {
            'name': source_name,
            'ra': args.ra,
            'dec': args.dec,
            'r_eff': args.radius
        }
        logger.info(f"使用命令行参数: RA={source_info['ra']:.6f}, "
                   f"Dec={source_info['dec']:.6f}, R_eff={source_info['r_eff']:.6f}度")
        return source_info

    # 否则从源表中查找
    try:
        catalog = load_hii_region_catalog(config['data_paths']['hii_region_catalog'])

        # 查找源
        source_row = None
        for i, row in catalog.iterrows():
            if source_name.strip() == row['source_name'].strip():
                source_row = row
                break

        if source_row is None:
            logger.error(f"在源表中未找到{source_name}")
            raise ValueError(f"在源表中未找到{source_name}")

        # 从源名中解析银道坐标并转换为赤道坐标
        try:
            from src.utils.helpers import parse_galactic_coords_from_name, galactic_to_icrs

            # 解析银道坐标
            l, b = parse_galactic_coords_from_name(source_name)
            logger.info(f"从源名解析银道坐标: l={l:.6f}, b={b:.6f}")

            # 转换为赤道坐标
            ra_from_name, dec_from_name = galactic_to_icrs(l, b)
            logger.info(f"银道坐标转换为赤道坐标: RA={ra_from_name:.6f}, Dec={dec_from_name:.6f} (ICRS)")

            # 使用从源名解析的坐标
            ra = ra_from_name
            dec = dec_from_name

        except Exception as e:
            logger.warning(f"从源名解析坐标失败: {str(e)}，将尝试从FITS文件获取坐标")

            # 从WISE FITS文件获取坐标作为备选方案
            wise_base_path = config['data_paths']['wise_fits_base']
            wise_dir = os.path.join(wise_base_path, source_name)

            if not os.path.exists(wise_dir):
                logger.error(f"找不到WISE数据目录: {wise_dir}")
                raise FileNotFoundError(f"找不到WISE数据目录: {wise_dir}")

            # 列出目录中的所有文件，用于调试
            all_files = os.listdir(wise_dir)
            logger.info(f"目录中的所有文件: {all_files}")

            # 查找WISE 12μm (W3) FITS文件，使用正确的文件命名格式
            fits_files = [f for f in all_files
                         if f.endswith('.fits') and '_wise_12' in f.lower()]
            logger.info(f"第一次匹配结果: {fits_files}")

            # 如果没有找到，尝试更宽松的匹配
            if not fits_files:
                fits_files = [f for f in all_files
                             if f.endswith('.fits') and 'wise_12' in f.lower()]
                logger.info(f"第二次匹配结果: {fits_files}")

            # 如果仍然没有找到，尝试最宽松的匹配
            if not fits_files:
                fits_files = [f for f in all_files
                             if f.endswith('.fits') and '12' in f.lower()]
                logger.info(f"第三次匹配结果: {fits_files}")

            if not fits_files:
                logger.error(f"在{wise_dir}中未找到WISE W3 FITS文件")
                raise FileNotFoundError(f"未找到WISE W3 FITS文件")

            # 使用第一个匹配的文件
            fits_file = os.path.join(wise_dir, fits_files[0])
            logger.info(f"从FITS文件获取坐标: {fits_file}")

            # 读取FITS文件头获取坐标
            with fits.open(fits_file) as hdul:
                header = hdul[0].header

                # 尝试从头信息中获取中心坐标和坐标系统
                if 'CRVAL1' in header and 'CRVAL2' in header:
                    ra = float(header['CRVAL1'])
                    dec = float(header['CRVAL2'])

                    # 检查坐标系统
                    coord_system = 'icrs'  # 默认使用ICRS
                    if 'RADESYS' in header:
                        coord_system = header['RADESYS'].lower()
                        logger.info(f"从FITS头信息获取坐标系统: {coord_system}")

                    # 创建SkyCoord对象并转换为ICRS
                    coord = SkyCoord(ra=ra*u.degree, dec=dec*u.degree, frame=coord_system)
                    icrs_coord = coord.icrs

                    # 更新为ICRS坐标
                    ra = icrs_coord.ra.degree
                    dec = icrs_coord.dec.degree

                    logger.info(f"从FITS头信息获取坐标: RA={ra:.6f}, Dec={dec:.6f} (ICRS)")
                else:
                    logger.error(f"FITS头信息中缺少坐标信息")
                    raise ValueError(f"FITS头信息中缺少坐标信息")

        # 如果同时有从源名解析的坐标和FITS文件中的坐标，比较它们
        if 'ra_from_name' in locals() and 'ra' in locals() and ra != ra_from_name:
            # 计算两组坐标之间的角距离
            from src.utils.helpers import angular_separation
            separation = angular_separation(ra, dec, ra_from_name, dec_from_name)
            logger.info(f"源名坐标和FITS坐标之间的角距离: {separation:.6f}度")

            # 如果差异较大，发出警告
            if separation > 0.1:  # 超过0.1度（6角分）
                logger.warning(f"源名坐标和FITS坐标差异较大: {separation:.6f}度")
                logger.warning(f"源名坐标: RA={ra_from_name:.6f}, Dec={dec_from_name:.6f}")
                logger.warning(f"FITS坐标: RA={ra:.6f}, Dec={dec:.6f}")
                logger.warning("使用源名中解析的坐标作为最终坐标")

        # 使用源表中的有效半径（角秒）转换为度
        r_eff_arcsec = float(source_row['N'])
        r_eff = r_eff_arcsec / 3600.0  # 角秒转度
        logger.info(f"使用有效半径{r_eff_arcsec}角秒 ({r_eff:.6f}度)")

        source_info = {
            'name': source_name,
            'ra': ra,
            'dec': dec,
            'r_eff': r_eff
        }

        logger.info(f"从源表加载信息: RA={source_info['ra']:.6f}, "
                   f"Dec={source_info['dec']:.6f}, R_eff={source_info['r_eff']:.6f}度")
        return source_info

    except Exception as e:
        logger.error(f"加载源信息失败: {str(e)}")
        raise

def process_hii_region(args):
    """
    处理HII区域

    Args:
        args: 命令行参数

    Returns:
        dict: 处理结果
    """
    # 加载配置
    config = load_config(args.config)

    # 设置输出目录
    if args.output_dir:
        output_dir = args.output_dir
    else:
        output_dir = config['output_paths']['results']

    ensure_directory(output_dir)
    logger.info(f"输出目录: {output_dir}")

    # 跳过的步骤
    skip_steps = args.skip_steps or []

    # 加载源信息
    source_info = load_source_info(args.source, args, config)

    # 创建中心坐标对象
    center_coord = SkyCoord(ra=source_info['ra']*u.degree,
                           dec=source_info['dec']*u.degree,
                           frame='icrs')

    # 创建处理目录
    processed_data_dir = os.path.join(output_dir, 'processed')
    ensure_directory(processed_data_dir)

    # 步骤1：加载WISE数据
    if 'data_loading' not in skip_steps:
        logger.info("步骤1：加载WISE数据")

        # 加载WISE FITS图像
        wise_image, wise_header, wise_wcs = load_wise_fits(source_info['name'])

        logger.info("WISE数据加载完成")
    else:
        logger.info("跳过数据加载步骤")

        # 加载WISE FITS图像
        wise_image, wise_header, wise_wcs = load_wise_fits(source_info['name'])

    # 步骤2：区域映射
    if 'region_mapping' not in skip_steps:
        logger.info("步骤2：区域映射")

        # 处理WISE图像
        processed_wise = process_wise_image(wise_image, wise_header)

        # 定义PDR掩模
        # 从配置文件加载PDR参数
        pdr_params = config.get('region_mapping', {}).get('pdr', {})

        # 创建可视化目录
        vis_dir = os.path.join(output_dir, 'visualizations')
        ensure_directory(vis_dir)

        # 确定使用哪种PDR检测方法
        use_contour_method = pdr_params.get('use_contour_method', False)
        use_ratio_method = pdr_params.get('use_ratio_method', True)

        if use_ratio_method:
            logger.info("使用W3/W4波段比值方法定义PDR区域")
            pdr_mask = define_pdr_mask(
                processed_wise, wise_wcs, center_coord, source_info['r_eff'],
                threshold_factor=pdr_params.get('threshold_factor', 0.3),
                max_radius_factor=pdr_params.get('max_radius_factor', 5.0),
                search_radius_factor=pdr_params.get('search_radius_factor', 3.0),
                use_ratio_method=True,
                source_name=source_info['name'],
                output_dir=output_dir,
                save_ratio_plot=pdr_params.get('save_ratio_plot', True),
                config_path=args.config
            )
        elif use_contour_method:
            logger.info("使用等高线方法定义PDR区域")
            pdr_mask = define_pdr_mask(
                processed_wise, wise_wcs, center_coord, source_info['r_eff'],
                threshold_factor=pdr_params.get('threshold_factor', 0.3),
                max_radius_factor=pdr_params.get('max_radius_factor', 5.0),
                search_radius_factor=pdr_params.get('search_radius_factor', 3.0),
                use_contour_method=True,
                n_contours=pdr_params.get('n_contours', 10),
                contour_start_nsigma=pdr_params.get('contour_start_nsigma', 3.0),
                source_name=source_info['name'],
                output_dir=output_dir,
                save_contour_plot=True
            )
        else:
            logger.info("使用阈值方法定义PDR区域")
            pdr_mask = define_pdr_mask(
                processed_wise, wise_wcs, center_coord, source_info['r_eff'],
                threshold_factor=pdr_params.get('threshold_factor', 0.3),
                max_radius_factor=pdr_params.get('max_radius_factor', 5.0),
                search_radius_factor=pdr_params.get('search_radius_factor', 3.0)
            )

        # 定义空腔掩模
        cavity_mask = define_cavity_mask(
            processed_wise.shape, wise_wcs, center_coord, source_info['r_eff'], pdr_mask
        )

        # 定义外部区域掩模
        external_mask = define_external_mask(
            processed_wise.shape, wise_wcs, center_coord, source_info['r_eff'], pdr_mask
        )

        # 保存区域掩模
        masks = {
            'cavity': cavity_mask,
            'pdr': pdr_mask,
            'external': external_mask
        }

        save_region_masks(
            masks, source_info['name'], processed_data_dir
        )

        # 加载Gaia数据
        try:
            gaia_data = load_gaia_data(
                source_info['name'],
                source_info['ra'],
                source_info['dec'],
                source_info['r_eff'] * 5  # 搜索半径为R_eff的5倍
            )

            # 为恒星分配区域标签
            star_coords = SkyCoord(ra=gaia_data['ra']*u.degree,
                                  dec=gaia_data['dec']*u.degree)

            region_tags = assign_region_tags(
                star_coords, wise_wcs, cavity_mask, pdr_mask, external_mask
            )

            # 添加区域标签列
            gaia_data['region'] = region_tags

            # 保存带区域标签的Gaia数据
            gaia_file = save_processed_data(
                gaia_data,
                f"{source_info['name']}_gaia_regions.fits",
                processed_data_dir
            )

            logger.info("区域映射完成")
        except Exception as e:
            logger.error(f"加载Gaia数据失败: {str(e)}")
            logger.warning("将使用空的Gaia数据继续处理")

            # 创建一个空的Gaia数据表
            from astropy.table import Table, Column
            gaia_data = Table()

            # 添加必要的列
            gaia_data['source_id'] = Column([0], dtype='int64')
            gaia_data['ra'] = Column([source_info['ra']], dtype='float64')
            gaia_data['dec'] = Column([source_info['dec']], dtype='float64')
            gaia_data['parallax'] = Column([np.nan], dtype='float64')
            gaia_data['parallax_error'] = Column([np.nan], dtype='float64')
            gaia_data['pmra'] = Column([np.nan], dtype='float64')
            gaia_data['pmde'] = Column([np.nan], dtype='float64')
            gaia_data['ruwe'] = Column([np.nan], dtype='float64')
            gaia_data['phot_g_mean_mag'] = Column([np.nan], dtype='float64')
            gaia_data['bp_rp'] = Column([np.nan], dtype='float64')
            gaia_data['distance'] = Column([np.nan], dtype='float64')
            gaia_data['j_m'] = Column([np.nan], dtype='float64')
            gaia_data['h_m'] = Column([np.nan], dtype='float64')
            gaia_data['k_m'] = Column([np.nan], dtype='float64')
            gaia_data['j_h'] = Column([np.nan], dtype='float64')
            gaia_data['h_k'] = Column([np.nan], dtype='float64')
            gaia_data['region'] = Column(['unknown'], dtype='U10')

            # 删除这一行，以创建一个空表但保留列结构
            gaia_data.remove_row(0)

            logger.warning("区域映射完成，但没有Gaia数据")

    # 步骤3：恒星选择
    if 'star_selection' not in skip_steps:
        logger.info("步骤3：恒星选择")

        # 应用质量筛选
        filtered_data, quality_flags = apply_quality_cuts(gaia_data)

        # 保存质量筛选后的数据
        filtered_data_file = save_processed_data(
            filtered_data,
            f"{source_info['name']}_filtered_data.fits",
            processed_data_dir
        )
        logger.info(f"保存质量筛选后的数据到: {filtered_data_file}")

        # 识别YSO
        yso_flags = flag_ysos_wise(filtered_data)

        # 筛选恒星样本
        star_sample = filter_star_sample(
            filtered_data, quality_flags=None, yso_flags=yso_flags
        )

        # 保存筛选后的恒星样本
        star_sample_file = save_processed_data(
            star_sample,
            f"{source_info['name']}_star_sample.fits",
            processed_data_dir
        )

        logger.info("恒星选择完成")
    else:
        logger.info("跳过恒星选择步骤")

        # 从已处理的文件加载数据
        star_sample_file = os.path.join(processed_data_dir, f"{source_info['name']}_star_sample.fits")

        if os.path.exists(star_sample_file):
            from astropy.table import Table
            star_sample = Table.read(star_sample_file)
        else:
            logger.warning(f"找不到筛选后的恒星样本文件: {star_sample_file}，创建空表")
            from astropy.table import Table
            star_sample = Table()
            star_sample['ra'] = []
            star_sample['dec'] = []

    # 步骤4：消光计算
    if 'extinction_calculation' not in skip_steps:
        logger.info("步骤4：消光计算")

        # 计算消光
        stars_with_av = compute_av_per_star(star_sample)

        # 保存带消光值的恒星样本
        av_file = save_processed_data(
            stars_with_av,
            f"{source_info['name']}_stars_with_av.fits",
            processed_data_dir
        )

        logger.info("消光计算完成")
    else:
        logger.info("跳过消光计算步骤")

        # 从已处理的文件加载数据
        av_file = os.path.join(processed_data_dir, f"{source_info['name']}_stars_with_av.fits")

        if os.path.exists(av_file):
            from astropy.table import Table
            stars_with_av = Table.read(av_file)
        else:
            logger.warning(f"找不到带消光值的恒星样本文件: {av_file}，创建空表")
            from astropy.table import Table
            stars_with_av = Table()
            stars_with_av['ra'] = []
            stars_with_av['dec'] = []
            stars_with_av['a_v'] = []
            stars_with_av['distance'] = []
            stars_with_av['region'] = []

    # 步骤5：距离分析
    if 'distance_analysis' not in skip_steps:
        logger.info("步骤5：距离分析")

        # 分析所有区域
        region_results = analyze_all_regions(stars_with_av, args.config, output_dir)

        # 估计分子云距离
        distance_estimate = estimate_cloud_distance(region_results)

        # 整合结果
        results = {
            'source_name': source_info['name'],
            'source_info': source_info,
            'region_results': region_results,
            'cloud_distance': distance_estimate.get('cloud_distance'),
            'distance_error': distance_estimate.get('distance_error'),
            'confidence': distance_estimate.get('confidence'),
            'evidence': distance_estimate.get('evidence')
        }

        # 保存分析结果
        save_analysis_results(
            results, source_info['name'], output_dir
        )

        logger.info("距离分析完成")
    else:
        logger.info("跳过距离分析步骤")

        # 这里可以加载已有的分析结果
        results = {
            'source_name': source_info['name'],
            'source_info': source_info,
            'message': "跳过了距离分析步骤，没有距离估计结果"
        }

    # 步骤6：可视化和报告生成
    logger.info("步骤6：可视化和报告生成")

    # 创建可视化目录
    vis_dir = os.path.join(output_dir, 'visualizations')
    ensure_directory(vis_dir)

    # 创建报告目录
    reports_dir = os.path.join(output_dir, 'reports')
    ensure_directory(reports_dir)

    # 如果跳过了前面的步骤，需要创建一个空的stars_with_av对象
    if 'stars_with_av' not in locals() or stars_with_av is None:
        logger.info("创建空的stars_with_av对象用于可视化")
        stars_with_av = Table()
        # 添加必要的列
        stars_with_av['ra'] = []
        stars_with_av['dec'] = []
        stars_with_av['a_v'] = []
        stars_with_av['distance'] = []
        stars_with_av['region'] = []

    try:
        # 1. 以12μm数据为背景，画出PDR区的轮廓，以及Gaia星的分布
        wise_vis_path = os.path.join(vis_dir, f"{source_info['name']}_wise_regions_stars.png")

        # 加载WISE图像和区域掩模
        wise_image, wise_header, wise_wcs = load_wise_fits(source_info['name'])

        # 加载区域掩模
        masks_file = os.path.join(processed_data_dir, f"{source_info['name']}_region_masks.fits")
        if os.path.exists(masks_file):
            with fits.open(masks_file) as hdul:
                cavity_mask = hdul['CAVITY'].data.astype(bool)
                pdr_mask = hdul['PDR'].data.astype(bool)
                external_mask = hdul['EXTERNAL'].data.astype(bool)

                masks = {
                    'cavity': cavity_mask,
                    'pdr': pdr_mask,
                    'external': external_mask
                }

                # 创建中心坐标对象
                center_coord = SkyCoord(ra=source_info['ra']*u.degree,
                                      dec=source_info['dec']*u.degree)

                # 绘制WISE图像、区域轮廓和恒星位置
                plot_wise_with_regions_and_stars(
                    wise_image, wise_header, masks, stars_with_av,
                    wise_vis_path,
                    title=f"{source_info['name']} - WISE 12μm with Region Contours and Stars",
                    r_eff=source_info['r_eff'],
                    center_coord=center_coord
                )

                logger.info(f"成功保存WISE图像可视化到: {wise_vis_path}")

                # 1.1 创建WISE三波段RGB图像
                try:
                    # 加载多波段WISE数据
                    wise_rgb_path = os.path.join(vis_dir, f"{source_info['name']}_wise_rgb.png")
                    logger.info(f"加载WISE多波段数据用于RGB图像生成")

                    wise_data, wise_rgb_wcs = load_wise_multiband(
                        source_info['name'], bands=('3.4', '12', '22'))

                    # 检查是否成功加载了所有波段
                    if 'w1' in wise_data and 'w3' in wise_data and 'w4' in wise_data:
                        # 绘制RGB图像
                        plot_wise_rgb(
                            wise_data, wise_rgb_wcs, wise_rgb_path,
                            title=f"{source_info['name']}",  # 只使用源名作为标题
                            r_eff=source_info['r_eff'],
                            center_coord=center_coord,
                            masks=masks,
                            star_table=stars_with_av
                        )
                        logger.info(f"成功保存WISE RGB图像到: {wise_rgb_path}")
                    else:
                        logger.warning("未能加载所有需要的WISE波段数据，跳过RGB图像生成")
                except Exception as e:
                    logger.error(f"生成WISE RGB图像失败: {str(e)}")
        else:
            logger.warning(f"找不到区域掩模文件: {masks_file}")

        # 2. 三个区域（空腔、PDR、外部）的消光-距离散点图
        if len(stars_with_av) > 0 and 'a_v' in stars_with_av.colnames and 'distance' in stars_with_av.colnames:
            ext_dist_path = os.path.join(vis_dir, f"{source_info['name']}_extinction_distance.png")

            # 绘制消光-距离散点图
            plot_extinction_distance(
                stars_with_av, ext_dist_path,
                title=f"{source_info['name']} - 消光-距离关系"
            )

            logger.info(f"成功保存消光-距离散点图到: {ext_dist_path}")
        else:
            logger.warning("没有足够的数据绘制消光-距离散点图")

    except Exception as e:
        logger.error(f"可视化失败: {str(e)}")
        logger.warning("继续处理，但可视化结果可能不完整")

    # 收集处理统计信息
    try:
        # 导入必要的模块
        from astropy.table import Table

        # 获取原始的Gaia数据
        original_gaia_file = os.path.join(processed_data_dir, f"{source_info['name']}_gaia_regions.fits")
        if os.path.exists(original_gaia_file):
            original_gaia_data = Table.read(original_gaia_file)
        else:
            # 如果文件不存在，创建一个空表
            original_gaia_data = Table()
            original_gaia_data['ra'] = []
            original_gaia_data['dec'] = []

        # 获取质量筛选后的数据
        filtered_data_file = os.path.join(processed_data_dir, f"{source_info['name']}_filtered_data.fits")
        if os.path.exists(filtered_data_file):
            filtered_data_from_file = Table.read(filtered_data_file)
            # 使用从文件加载的数据
            filtered_data_for_report = filtered_data_from_file
        else:
            # 如果文件不存在，创建一个空表
            filtered_data_for_report = Table()
            filtered_data_for_report['ra'] = []
            filtered_data_for_report['dec'] = []

        # 创建详细的处理统计信息
        processing_stats = {
            'gaia_stats': {
                'initial_count': len(original_gaia_data) if original_gaia_data is not None else 0,
                'after_quality_cuts': len(filtered_data_for_report) if filtered_data_for_report is not None else 0,
                'final_count': len(star_sample) if star_sample is not None else 0
            },
            'extinction_stats': {
                'valid_av_count': np.sum(~np.isnan(stars_with_av['a_v'])) if stars_with_av is not None and 'a_v' in stars_with_av.colnames else 0
            },
            'distance_stats': {
                'final_distance': results.get('cloud_distance'),
                'distance_error': results.get('distance_error'),
                'confidence': results.get('confidence'),
                'evidence': results.get('evidence', [])
            }
        }

        # 添加区域统计信息
        if stars_with_av is not None and 'region' in stars_with_av.colnames:
            # 计算各区域的恒星数量
            cavity_count = np.sum(stars_with_av['region'] == 'cavity')
            pdr_count = np.sum(stars_with_av['region'] == 'pdr')
            external_count = np.sum(stars_with_av['region'] == 'external')

            # 获取PDR区域的面积（像素数）
            pdr_area = 0
            masks_file = os.path.join(processed_data_dir, f"{source_info['name']}_region_masks.npz")
            if os.path.exists(masks_file):
                try:
                    masks_data = np.load(masks_file)
                    if 'pdr' in masks_data:
                        pdr_area = np.sum(masks_data['pdr'])
                except Exception as e:
                    logger.error(f"加载PDR掩模失败: {str(e)}")

            # 添加到统计信息
            processing_stats['region_stats'] = {
                'cavity_count': cavity_count,
                'pdr_count': pdr_count,
                'external_count': external_count,
                'pdr_area': pdr_area
            }

        # 添加更多的消光统计信息
        if stars_with_av is not None and 'a_v' in stars_with_av.colnames:
            valid_av = stars_with_av['a_v'][~np.isnan(stars_with_av['a_v'])]
            if len(valid_av) > 0:
                processing_stats['extinction_stats'].update({
                    'mean_av': np.mean(valid_av),
                    'median_av': np.median(valid_av),
                    'min_av': np.min(valid_av),
                    'max_av': np.max(valid_av),
                    'std_av': np.std(valid_av)
                })

        # 添加跳跃点信息（如果有）
        if 'region_results' in results:
            jumps = {}
            for region, region_result in results['region_results'].items():
                if 'jumps' in region_result:
                    jumps[region] = region_result['jumps']

            if jumps:
                processing_stats['distance_stats']['jumps'] = jumps

        # 生成详细的文本报告
        report_path = os.path.join(output_dir, 'reports', f"{source_info['name']}_report.txt")
        with open(report_path, 'w', encoding='utf-8-sig') as f:
            f.write(f"{source_info['name']} 处理报告\n")
            f.write("="*50 + "\n\n")

            # 源信息
            f.write("源信息:\n")
            f.write(f"  名称: {source_info['name']}\n")
            f.write(f"  坐标: RA={source_info['ra']:.6f}, Dec={source_info['dec']:.6f} (ICRS)\n")
            f.write(f"  有效半径: {source_info['r_eff']*3600:.1f} 角秒 ({source_info['r_eff']:.6f} 度)\n\n")

            # 处理参数
            f.write("处理参数:\n")
            # 从配置文件加载参数
            config = load_config(args.config)

            # PDR区域定义参数
            pdr_params = config.get('region_mapping', {}).get('pdr', {})
            use_contour_method = pdr_params.get('use_contour_method', False)
            use_ratio_method = pdr_params.get('use_ratio_method', True)

            f.write("  PDR区域定义:\n")
            if use_ratio_method:
                f.write(f"    方法: W3/W4波段比值聚类法\n")
                f.write(f"    搜索半径因子: {pdr_params.get('search_radius_factor', 3.0)} (3R)\n")
                f.write(f"    平滑参数: 1.0\n")
                f.write(f"    聚类方法: KMeans聚类，自动确定最佳聚类数(2-4)\n")
                f.write(f"    聚类特征: 结合W3/W4比值和空间位置信息\n")
                f.write(f"    PDR识别: 基于聚类特征（比值分布、距离中心距离、紧凑性）自动识别PDR区域\n")
                f.write(f"    备用策略: 当聚类方法失效时，使用W3波段亮度阈值法\n\n")
            elif use_contour_method:
                f.write(f"    方法: 等高线分析法\n")
                f.write(f"    搜索半径因子: {pdr_params.get('search_radius_factor', 3.0)} (3R)\n")
                f.write(f"    等高线数量: {pdr_params.get('n_contours', 10)}\n")
                f.write(f"    最低等高线水平: 背景中值 + {pdr_params.get('contour_start_nsigma', 3.0)} * 背景标准差\n")
                f.write(f"    背景估计: 使用sigma-clipping统计方法\n")
                f.write(f"    PDR识别: 基于内外边界等高线对\n\n")
            else:
                f.write(f"    方法: 阈值法\n")
                f.write(f"    阈值因子: {pdr_params.get('threshold_factor', 0.3)}\n")
                f.write(f"    最大半径因子: {pdr_params.get('max_radius_factor', 5.0)}\n")
                f.write(f"    搜索半径因子: {pdr_params.get('search_radius_factor', 3.0)} (3R)\n")
                f.write(f"    形态学操作磁盘大小: {pdr_params.get('morphology_disk_size', 5)}\n")
                f.write(f"    背景估计: 使用低强度区域的第10百分位数\n\n")

            # 质量筛选参数
            quality_params = config.get('star_selection', {}).get('quality_cuts', {})
            f.write("  质量筛选标准:\n")
            f.write(f"    视差信噪比最小值: {quality_params.get('parallax_snr_min', 5)}\n")
            f.write(f"    RUWE最大值: {quality_params.get('ruwe_max', 1.4)}\n")
            f.write(f"    BP-RP误差最大值: {quality_params.get('bp_rp_error_max', 0.1)}\n")
            f.write(f"    G星等最大值: {quality_params.get('g_mag_max', 18)}\n\n")

            # YSO筛选参数
            yso_params = config.get('star_selection', {}).get('yso_criteria', {})
            f.write("  YSO筛选标准:\n")
            f.write(f"    W1-W2最小值: {yso_params.get('w1w2_min', 0.8)}\n")
            f.write(f"    W2-W3最小值: {yso_params.get('w2w3_min', 2.0)}\n\n")

            # 消光计算参数
            ext_params = config.get('extinction', {})
            f.write("  消光计算:\n")
            f.write(f"    方法: {ext_params.get('method', 'nir')}\n")
            f.write(f"    Rv值: {ext_params.get('rv', 3.1)}\n\n")

            # 处理统计
            f.write("处理统计:\n")

            # Gaia数据统计
            gaia_stats = processing_stats['gaia_stats']
            f.write("  Gaia数据:\n")
            f.write(f"    初始恒星数量: {gaia_stats.get('initial_count', 0)}\n")
            f.write(f"    质量筛选后: {gaia_stats.get('after_quality_cuts', 0)}\n")
            f.write(f"    最终恒星样本: {gaia_stats.get('final_count', 0)}\n\n")

            # 区域统计
            if 'region_stats' in processing_stats:
                region_stats = processing_stats.get('region_stats', {})
                f.write("  区域统计:\n")
                f.write(f"    空腔区域恒星数: {region_stats.get('cavity_count', 0)}\n")
                f.write(f"    PDR区域恒星数: {region_stats.get('pdr_count', 0)}\n")
                f.write(f"    外部区域恒星数: {region_stats.get('external_count', 0)}\n")
                f.write(f"    PDR区域面积(像素): {region_stats.get('pdr_area', 0)}\n\n")

            # 消光计算统计
            ext_stats = processing_stats['extinction_stats']
            f.write("  消光计算:\n")
            f.write(f"    有效消光值数量: {ext_stats.get('valid_av_count', 0)}\n")
            f.write(f"    平均A_V: {ext_stats.get('mean_av', 0):.2f}\n")
            f.write(f"    中位数A_V: {ext_stats.get('median_av', 0):.2f}\n")
            f.write(f"    A_V范围: {ext_stats.get('min_av', 0):.2f} - {ext_stats.get('max_av', 0):.2f}\n\n")

            # 距离估计
            dist_stats = processing_stats['distance_stats']
            f.write("  距离估计:\n")
            if dist_stats.get('final_distance') is not None:
                f.write(f"    最终距离: {dist_stats.get('final_distance', 0):.2f} ± {dist_stats.get('distance_error', 0):.2f} kpc\n")
                f.write(f"    置信度: {dist_stats.get('confidence', '未知')}\n\n")

                f.write("    证据:\n")
                for evidence in dist_stats.get('evidence', []):
                    f.write(f"      - {evidence}\n")

                # 添加跳跃点信息
                if 'jumps' in dist_stats:
                    f.write("\n    消光跳变:\n")
                    for region, jumps in dist_stats.get('jumps', {}).items():
                        if jumps:
                            region_name = {"cavity": "空腔", "pdr": "PDR", "external": "外部"}.get(region, region)
                            for i, jump in enumerate(jumps):
                                f.write(f"      {region_name}区域跳变 {i+1}: {jump.get('distance', 0):.2f} kpc, ΔA_V = {jump.get('delta_av', 0):.2f}\n")
            else:
                f.write("    无可用的距离估计\n")

            # 添加处理日志摘要
            f.write("\n处理日志摘要:\n")
            try:
                # 尝试读取最新的日志文件
                log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
                log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
                if log_files:
                    latest_log = max(log_files, key=lambda x: os.path.getmtime(os.path.join(log_dir, x)))
                    log_path = os.path.join(log_dir, latest_log)

                    # 读取日志文件的最后20行
                    with open(log_path, 'r', encoding='utf-8') as log_file:
                        log_lines = log_file.readlines()
                        last_lines = log_lines[-20:]
                        for line in last_lines:
                            if source_info['name'] in line:
                                f.write(f"  {line.strip()}\n")
            except Exception as e:
                f.write(f"  读取日志文件时出错: {str(e)}\n")

            f.write("\n" + "="*50 + "\n")
            f.write(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

        logger.info(f"成功生成处理报告: {report_path}")

    except Exception as e:
        logger.error(f"生成报告失败: {str(e)}")
        logger.warning("继续处理，但报告可能不完整")

    logger.info(f"处理{source_info['name']}完成")
    return results

def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_arguments()

    try:
        # 处理HII区域
        results = process_hii_region(args)

        # 输出结果摘要
        if 'cloud_distance' in results and results['cloud_distance'] is not None:
            print(f"\n结果摘要:")
            print(f"源: {results['source_name']}")
            print(f"分子云距离估计: {results['cloud_distance']:.0f} ± {results['distance_error']:.0f} pc")
            print(f"置信度: {results['confidence']}")
            print("\n证据:")
            for e in results['evidence']:
                print(f"- {e}")
        else:
            print(f"\n未能估计{results['source_name']}的分子云距离")

        return 0

    except Exception as e:
        logger.error(f"处理失败: {str(e)}", exc_info=True)
        print(f"错误: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
