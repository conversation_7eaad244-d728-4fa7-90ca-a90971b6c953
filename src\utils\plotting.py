"""
绘图工具模块

提供项目中使用的绘图函数。
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LogNorm
from astropy.visualization import ZScaleInterval, ImageNormalize
import astropy.units as u

def setup_plotting_style():
    """
    设置绘图样式
    """
    plt.style.use('seaborn-v0_8-whitegrid')
    plt.rcParams['figure.figsize'] = (10, 8)
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.labelsize'] = 14
    plt.rcParams['axes.titlesize'] = 16
    plt.rcParams['xtick.labelsize'] = 12
    plt.rcParams['ytick.labelsize'] = 12
    plt.rcParams['legend.fontsize'] = 12
    plt.rcParams['figure.titlesize'] = 18

def plot_extinction_distance(distances, extinctions, region_name, bin_centers=None, 
                             bin_medians=None, bin_std=None, output_dir=None):
    """
    绘制消光-距离关系图
    
    Args:
        distances: 距离数组（单位：pc）
        extinctions: 消光数组（单位：mag）
        region_name: 区域名称（Cavity, PDR, External）
        bin_centers: bin中心位置
        bin_medians: 每个bin的中位数
        bin_std: 每个bin的标准差
        output_dir: 输出目录
        
    Returns:
        str: 保存的图像路径
    """
    setup_plotting_style()
    
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # 绘制散点图
    scatter = ax.scatter(distances, extinctions, alpha=0.5, s=10, 
                         label=f'{region_name} Stars')
    
    # 如果提供了bin数据，绘制中位数线
    if bin_centers is not None and bin_medians is not None:
        valid = ~np.isnan(bin_medians)
        ax.plot(bin_centers[valid], bin_medians[valid], 'r-', 
                linewidth=2, label=f'{region_name} Median')
        
        # 如果提供了标准差，绘制误差区域
        if bin_std is not None:
            ax.fill_between(bin_centers[valid], 
                           bin_medians[valid] - bin_std[valid],
                           bin_medians[valid] + bin_std[valid],
                           color='r', alpha=0.2)
    
    # 设置坐标轴标签和标题
    ax.set_xlabel('Distance (pc)')
    ax.set_ylabel('Extinction $A_V$ (mag)')
    ax.set_title(f'Extinction vs. Distance for {region_name} Region')
    
    # 添加图例
    ax.legend(loc='upper left')
    
    # 设置网格
    ax.grid(True, linestyle='--', alpha=0.7)
    
    # 保存图像
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, f'extinction_distance_{region_name}.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        return output_path
    else:
        plt.tight_layout()
        plt.show()
        return None

def plot_wise_image(image_data, wcs, center_coord, masks=None, output_dir=None):
    """
    绘制WISE图像和区域掩模
    
    Args:
        image_data: WISE图像数据
        wcs: 世界坐标系
        center_coord: 中心坐标 (SkyCoord对象)
        masks: 字典，包含不同区域的掩模 {'cavity': mask1, 'pdr': mask2, 'external': mask3}
        output_dir: 输出目录
        
    Returns:
        str: 保存的图像路径
    """
    setup_plotting_style()
    
    # 创建图像归一化
    norm = ImageNormalize(image_data, interval=ZScaleInterval())
    
    fig = plt.figure(figsize=(12, 10))
    
    # 主图像
    ax = plt.subplot(projection=wcs)
    im = ax.imshow(image_data, origin='lower', norm=norm, cmap='viridis')
    
    # 添加坐标网格
    ax.grid(color='white', ls='solid', alpha=0.3)
    ax.set_xlabel('RA')
    ax.set_ylabel('Dec')
    
    # 标记中心位置
    ax.scatter(center_coord.ra.degree, center_coord.dec.degree, 
               transform=ax.get_transform('world'), 
               marker='+', color='red', s=200, label='Center')
    
    # 如果提供了掩模，绘制区域边界
    if masks:
        colors = {'cavity': 'blue', 'pdr': 'red', 'external': 'green'}
        for name, mask in masks.items():
            if mask is not None:
                # 这里需要根据实际掩模格式调整绘制方法
                # 示例：绘制掩模边界
                pass
    
    # 添加色标
    cbar = plt.colorbar(im, ax=ax, label='Flux')
    
    # 添加图例
    ax.legend(loc='upper right')
    
    # 设置标题
    ax.set_title(f'WISE 12μm Image with Region Masks')
    
    # 保存图像
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, f'wise_image_regions.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        return output_path
    else:
        plt.tight_layout()
        plt.show()
        return None

def plot_comparison_jumps(region_results, output_dir=None):
    """
    比较不同区域的消光跳变
    
    Args:
        region_results: 字典，包含不同区域的结果
                       {'cavity': {'distances': [], 'medians': [], 'jumps': []},
                        'pdr': {...},
                        'external': {...}}
        output_dir: 输出目录
        
    Returns:
        str: 保存的图像路径
    """
    setup_plotting_style()
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    colors = {'cavity': 'blue', 'pdr': 'red', 'external': 'green'}
    
    # 绘制每个区域的中位数曲线
    for region, data in region_results.items():
        if 'distances' in data and 'medians' in data:
            distances = data['distances']
            medians = data['medians']
            valid = ~np.isnan(medians)
            ax.plot(distances[valid], medians[valid], 
                    color=colors.get(region, 'black'),
                    linewidth=2, label=f'{region.capitalize()} Median')
    
    # 标记跳变位置
    for region, data in region_results.items():
        if 'jumps' in data and data['jumps']:
            for jump in data['jumps']:
                jump_distance = jump['distance']
                jump_height = jump['height']
                ax.axvline(x=jump_distance, color=colors.get(region, 'black'), 
                          linestyle='--', alpha=0.7)
                ax.text(jump_distance, ax.get_ylim()[1]*0.9, 
                       f"{region.capitalize()}\nΔA_V={jump_height:.2f}",
                       color=colors.get(region, 'black'),
                       rotation=90, ha='right', va='top')
    
    # 设置坐标轴标签和标题
    ax.set_xlabel('Distance (pc)')
    ax.set_ylabel('Extinction $A_V$ (mag)')
    ax.set_title('Comparison of Extinction Jumps Across Regions')
    
    # 添加图例
    ax.legend(loc='upper left')
    
    # 设置网格
    ax.grid(True, linestyle='--', alpha=0.7)
    
    # 保存图像
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, f'extinction_jumps_comparison.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        return output_path
    else:
        plt.tight_layout()
        plt.show()
        return None
