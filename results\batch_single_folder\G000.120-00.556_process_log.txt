处理时间: 2025-05-20 09:37:40
耗时: 70.89秒

标准输出:

未能估计G000.120-00.556的分子云距离


标准错误:
2025-05-20 09:36:32,661 - main_single_folder - INFO - 输出目录: results/batch_single_folder
2025-05-20 09:36:32,662 - main_single_folder - INFO - 加载源G000.120-00.556的信息
2025-05-20 09:36:32,662 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
2025-05-20 09:36:32,669 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-05-20 09:36:32,669 - main_single_folder - INFO - 从源名解析银道坐标: l=0.120000, b=-0.556000
2025-05-20 09:36:32,678 - main_single_folder - INFO - 银道坐标转换为赤道坐标: RA=267.019801, Dec=-29.122052 (ICRS)
2025-05-20 09:36:32,678 - main_single_folder - INFO - 从源表加载信息: RA=267.019801, Dec=-29.122052, R_eff=0.100000度
2025-05-20 09:36:32,679 - main_single_folder - INFO - 步骤1：加载WISE数据
2025-05-20 09:36:32,683 - data_manager - INFO - 为源G000.120-00.556加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.120-00.556
2025-05-20 09:36:32,683 - data_manager - INFO - 目录中的所有文件: ['G000.120-00.556_ATLASGAL_870um.fits', 'G000.120-00.556_IRIS_100.fits', 'G000.120-00.556_NVSS.fits', 'G000.120-00.556_WISE_12.fits', 'G000.120-00.556_WISE_22.fits', 'G000.120-00.556_WISE_3.4.fits', 'G000.120-00.556_WISE_4.6.fits']
2025-05-20 09:36:32,683 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:36:32,683 - data_manager - INFO - 第一次匹配结果: ['G000.120-00.556_WISE_12.fits']
2025-05-20 09:36:32,683 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.120-00.556\G000.120-00.556_WISE_12.fits
2025-05-20 09:36:32,695 - data_manager - INFO - FITS数据统计: 最小值=180.14215087890625, 最大值=10206.8095703125, 均值=2248.91357421875, 中位数=1937.759521484375
2025-05-20 09:36:32,695 - data_manager - INFO - 有效数据点数量: 303807/305809 (99.35%)
2025-05-20 09:36:32,702 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (553, 553)
2025-05-20 09:36:32,703 - main_single_folder - INFO - WISE数据加载完成
2025-05-20 09:36:32,703 - main_single_folder - INFO - 步骤2：区域映射
2025-05-20 09:36:32,714 - main_single_folder - INFO - 使用W3/W4波段比值方法定义PDR区域
2025-05-20 09:36:32,714 - region_mapper - INFO - 使用W3/W4波段比值方法定义PDR掩模
2025-05-20 09:36:32,714 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:36:32,714 - region_mapper.ratio - INFO - 使用W3/W4波段比值方法定义PDR掩模，搜索半径因子=3.0
2025-05-20 09:36:32,715 - region_mapper.ratio - INFO - 加载源G000.120-00.556的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:36:32,715 - region_mapper.ratio - INFO - 加载源G000.120-00.556的W3(12μm)和W4(22μm)波段数据
2025-05-20 09:36:32,715 - data_manager - INFO - 为源G000.120-00.556加载多波段WISE数据: ('12', '22')
2025-05-20 09:36:32,720 - data_manager - INFO - 为源G000.120-00.556加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.120-00.556
2025-05-20 09:36:32,720 - data_manager - INFO - 目录中的所有文件: ['G000.120-00.556_ATLASGAL_870um.fits', 'G000.120-00.556_IRIS_100.fits', 'G000.120-00.556_NVSS.fits', 'G000.120-00.556_WISE_12.fits', 'G000.120-00.556_WISE_22.fits', 'G000.120-00.556_WISE_3.4.fits', 'G000.120-00.556_WISE_4.6.fits']
2025-05-20 09:36:32,720 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:36:32,720 - data_manager - INFO - 第一次匹配结果: ['G000.120-00.556_WISE_12.fits']
2025-05-20 09:36:32,721 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.120-00.556\G000.120-00.556_WISE_12.fits
2025-05-20 09:36:32,728 - data_manager - INFO - FITS数据统计: 最小值=180.14215087890625, 最大值=10206.8095703125, 均值=2248.91357421875, 中位数=1937.759521484375
2025-05-20 09:36:32,729 - data_manager - INFO - 有效数据点数量: 303807/305809 (99.35%)
2025-05-20 09:36:32,814 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (553, 553)
2025-05-20 09:36:32,814 - data_manager - INFO - 使用12μm波段的WCS作为参考
2025-05-20 09:36:32,814 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (553, 553)
2025-05-20 09:36:32,819 - data_manager - INFO - 为源G000.120-00.556加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.120-00.556
2025-05-20 09:36:32,819 - data_manager - INFO - 目录中的所有文件: ['G000.120-00.556_ATLASGAL_870um.fits', 'G000.120-00.556_IRIS_100.fits', 'G000.120-00.556_NVSS.fits', 'G000.120-00.556_WISE_12.fits', 'G000.120-00.556_WISE_22.fits', 'G000.120-00.556_WISE_3.4.fits', 'G000.120-00.556_WISE_4.6.fits']
2025-05-20 09:36:32,819 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:36:32,820 - data_manager - INFO - 第一次匹配结果: ['G000.120-00.556_WISE_22.fits']
2025-05-20 09:36:32,820 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.120-00.556\G000.120-00.556_WISE_22.fits
2025-05-20 09:36:32,824 - data_manager - INFO - FITS数据统计: 最小值=330.3290710449219, 最大值=2674.807373046875, 均值=478.0859680175781, 中位数=389.5059814453125
2025-05-20 09:36:32,824 - data_manager - INFO - 有效数据点数量: 89256/90000 (99.17%)
2025-05-20 09:36:32,831 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (300, 300)
2025-05-20 09:36:32,831 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (300, 300)
2025-05-20 09:36:32,831 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w3', 'w4']
2025-05-20 09:36:32,835 - region_mapper.ratio - INFO - 调整W4波段数据从(300, 300)到(553, 553)
2025-05-20 09:36:32,835 - region_mapper.ratio - INFO - 调整W4波段数据从(300, 300)到(553, 553)
2025-05-20 09:36:32,896 - region_mapper.ratio - INFO - W3/W4比值范围: 3.13-7.49，均值: 3804.06
2025-05-20 09:36:32,896 - region_mapper.ratio - INFO - W3/W4比值范围: 3.13-7.49，均值: 3804.06
2025-05-20 09:36:32,903 - region_mapper.ratio - INFO - 像素尺度: 483.09 像素/角秒
2025-05-20 09:36:32,903 - region_mapper.ratio - INFO - 像素尺度: 483.09 像素/角秒
2025-05-20 09:36:32,904 - region_mapper.ratio - INFO - 有效半径: 0.7 像素
2025-05-20 09:36:32,904 - region_mapper.ratio - INFO - 有效半径: 0.7 像素
2025-05-20 09:36:32,904 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:36:32,904 - region_mapper.ratio - INFO - PDR搜索半径 (3.0R): 10.0 像素
2025-05-20 09:36:33,653 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.631
2025-05-20 09:36:33,653 - region_mapper.ratio - INFO - 聚类数=2的轮廓系数: 0.631
2025-05-20 09:36:33,729 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.672
2025-05-20 09:36:33,729 - region_mapper.ratio - INFO - 聚类数=3的轮廓系数: 0.672
2025-05-20 09:36:33,809 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.547
2025-05-20 09:36:33,809 - region_mapper.ratio - INFO - 聚类数=4的轮廓系数: 0.547
2025-05-20 09:36:33,810 - region_mapper.ratio - INFO - 使用最佳聚类数: 3
2025-05-20 09:36:33,810 - region_mapper.ratio - INFO - 使用最佳聚类数: 3
2025-05-20 09:36:33,882 - region_mapper.ratio - INFO - 聚类 0: 平均比值=4.25±0.07, 大小=221, 平均距离=6.7, 紧凑性=6.5
2025-05-20 09:36:33,882 - region_mapper.ratio - INFO - 聚类 0: 平均比值=4.25±0.07, 大小=221, 平均距离=6.7, 紧凑性=6.5
2025-05-20 09:36:33,883 - region_mapper.ratio - INFO - 聚类 1: 平均比值=4.63±0.15, 大小=20, 平均距离=8.0, 紧凑性=1.7
2025-05-20 09:36:33,883 - region_mapper.ratio - INFO - 聚类 1: 平均比值=4.63±0.15, 大小=20, 平均距离=8.0, 紧凑性=1.7
2025-05-20 09:36:33,883 - region_mapper.ratio - INFO - 聚类 2: 平均比值=3.95±0.09, 大小=72, 平均距离=6.3, 紧凑性=3.2
2025-05-20 09:36:33,883 - region_mapper.ratio - INFO - 聚类 2: 平均比值=3.95±0.09, 大小=72, 平均距离=6.3, 紧凑性=3.2
2025-05-20 09:36:33,884 - region_mapper.ratio - INFO - 选择聚类 0 作为PDR区域
2025-05-20 09:36:33,884 - region_mapper.ratio - INFO - 选择聚类 0 作为PDR区域
2025-05-20 09:36:33,921 - region_mapper.ratio - INFO - PDR掩模覆盖237个像素
2025-05-20 09:36:33,921 - region_mapper.ratio - INFO - PDR掩模覆盖237个像素
2025-05-20 09:36:38,518 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G000.120-00.556_w3w4_ratio.png
2025-05-20 09:36:38,518 - region_mapper.ratio - INFO - 成功保存W3/W4比值图像到: results/batch_single_folder\visualizations\G000.120-00.556_w3w4_ratio.png
2025-05-20 09:36:38,519 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖237个像素
2025-05-20 09:36:38,519 - region_mapper.ratio - INFO - W3/W4波段比值方法PDR掩模创建完成，最终覆盖237个像素
2025-05-20 09:36:38,552 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G000.120-00.556_region_masks.fits
2025-05-20 09:36:38,552 - region_mapper.io - INFO - 成功保存区域掩模到: results/batch_single_folder\processed\G000.120-00.556_region_masks.fits
2025-05-20 09:36:38,557 - data_manager - INFO - 为源G000.120-00.556加载Gaia数据，中心坐标: RA=267.019801, Dec=-29.122052, 半径=0.5000度
2025-05-20 09:36:38,559 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances-Data/gaia_data\gaia_G000.120-00.556_30arcmin_5R.csv
H:\Augment\Parallax distances\src\data_manager.py:87: DtypeWarning: Columns (47,52,54) have mixed types. Specify dtype option on import or set low_memory=False.
  df = pd.read_csv(gaia_file)
2025-05-20 09:36:41,330 - data_manager - INFO - 成功加载Gaia数据，共176254条记录
2025-05-20 09:36:41,506 - data_manager - INFO - 成功加载Gaia数据，共176252个源在搜索半径内
2025-05-20 09:36:41,915 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G000.120-00.556_gaia_regions.fits
2025-05-20 09:37:27,802 - data_manager - WARNING - 检测到可能导致保存问题的列: ['region']
2025-05-20 09:37:27,890 - data_manager - INFO - 已移除有问题的列，继续保存
2025-05-20 09:37:31,775 - data_manager - ERROR - 保存数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U11'), dtype('<U9'), dtype('<U1'), dtype('<U10')}
2025-05-20 09:37:31,775 - main_single_folder - ERROR - 加载Gaia数据失败: Column 'TYC2' contains unsupported object types or mixed types: {dtype('<U11'), dtype('<U9'), dtype('<U1'), dtype('<U10')}
2025-05-20 09:37:31,776 - main_single_folder - WARNING - 将使用空的Gaia数据继续处理
2025-05-20 09:37:31,888 - main_single_folder - INFO - 区域映射完成
2025-05-20 09:37:31,888 - main_single_folder - INFO - 步骤3：恒星选择
2025-05-20 09:37:31,893 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-05-20 09:37:31,894 - star_selector - WARNING - 没有有效的视差数据，跳过视差信噪比筛选
2025-05-20 09:37:31,894 - star_selector - WARNING - 没有有效的RUWE数据，跳过RUWE筛选
2025-05-20 09:37:31,894 - star_selector - INFO - 质量筛选详细信息:
2025-05-20 09:37:31,895 - star_selector - INFO -   视差信噪比阈值: 5.0
2025-05-20 09:37:31,895 - star_selector - INFO -   RUWE阈值: 1.4
2025-05-20 09:37:31,895 - star_selector - INFO -   总体通过率: 0.0%
2025-05-20 09:37:31,895 - star_selector - INFO - 质量筛选完成，保留0/0个源
2025-05-20 09:37:31,896 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G000.120-00.556_filtered_data.fits
2025-05-20 09:37:31,908 - data_manager - INFO - 成功保存数据
2025-05-20 09:37:31,909 - main_single_folder - INFO - 保存质量筛选后的数据到: results/batch_single_folder\processed\G000.120-00.556_filtered_data.fits
2025-05-20 09:37:31,914 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-05-20 09:37:31,914 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-05-20 09:37:31,914 - star_selector - INFO - 筛选恒星样本，原始样本大小: 0
2025-05-20 09:37:31,914 - star_selector - INFO - 排除YSO后: 0/0个源
2025-05-20 09:37:31,915 - star_selector - INFO - 区域 cavity: 0个源
2025-05-20 09:37:31,915 - star_selector - INFO - 区域 pdr: 0个源
2025-05-20 09:37:31,915 - star_selector - INFO - 区域 external: 0个源
2025-05-20 09:37:31,916 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 0
2025-05-20 09:37:31,916 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G000.120-00.556_star_sample.fits
2025-05-20 09:37:31,928 - data_manager - INFO - 成功保存数据
2025-05-20 09:37:31,928 - main_single_folder - INFO - 恒星选择完成
2025-05-20 09:37:31,928 - main_single_folder - INFO - 步骤4：消光计算
2025-05-20 09:37:31,933 - extinction_estimator - INFO - 为0个恒星计算A_V，参数: {'rv': 3.1}
2025-05-20 09:37:31,933 - extinction_estimator - WARNING - 星表为空，无法计算A_V
2025-05-20 09:37:31,933 - data_manager - INFO - 保存处理后的数据到: results/batch_single_folder\processed\G000.120-00.556_stars_with_av.fits
2025-05-20 09:37:31,945 - data_manager - INFO - 成功保存数据
2025-05-20 09:37:31,945 - main_single_folder - INFO - 消光计算完成
2025-05-20 09:37:31,946 - main_single_folder - INFO - 步骤5：距离分析
2025-05-20 09:37:31,946 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-05-20 09:37:31,946 - distance_analyzer - INFO - 将分析0个区域: 
2025-05-20 09:37:31,946 - distance_analyzer - INFO - 估计分子云距离
2025-05-20 09:37:31,946 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity, pdr, external
2025-05-20 09:37:31,947 - distance_analyzer - WARNING - 没有检测到任何跳变，无法估计距离
2025-05-20 09:37:31,947 - main_single_folder - INFO - 距离分析完成
2025-05-20 09:37:31,947 - main_single_folder - INFO - 步骤6：可视化和报告生成
2025-05-20 09:37:31,952 - data_manager - INFO - 为源G000.120-00.556加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.120-00.556
2025-05-20 09:37:31,952 - data_manager - INFO - 目录中的所有文件: ['G000.120-00.556_ATLASGAL_870um.fits', 'G000.120-00.556_IRIS_100.fits', 'G000.120-00.556_NVSS.fits', 'G000.120-00.556_WISE_12.fits', 'G000.120-00.556_WISE_22.fits', 'G000.120-00.556_WISE_3.4.fits', 'G000.120-00.556_WISE_4.6.fits']
2025-05-20 09:37:31,952 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:37:31,952 - data_manager - INFO - 第一次匹配结果: ['G000.120-00.556_WISE_12.fits']
2025-05-20 09:37:31,953 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.120-00.556\G000.120-00.556_WISE_12.fits
2025-05-20 09:37:31,961 - data_manager - INFO - FITS数据统计: 最小值=180.14215087890625, 最大值=10206.8095703125, 均值=2248.91357421875, 中位数=1937.759521484375
2025-05-20 09:37:31,961 - data_manager - INFO - 有效数据点数量: 303807/305809 (99.35%)
2025-05-20 09:37:31,969 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (553, 553)
2025-05-20 09:37:31,989 - visualizer - INFO - 绘制WISE图像和区域掩模，输出到: results/batch_single_folder\visualizations\G000.120-00.556_wise_regions_stars.png
2025-05-20 09:37:32,105 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:37:34,179 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:37:34,188 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:37:36,657 - visualizer - INFO - 成功保存图像到: results/batch_single_folder\visualizations\G000.120-00.556_wise_regions_stars.png
2025-05-20 09:37:36,658 - main_single_folder - INFO - 成功保存WISE图像和区域掩模到: results/batch_single_folder\visualizations\G000.120-00.556_wise_regions_stars.png
2025-05-20 09:37:36,658 - main_single_folder - INFO - 加载WISE多波段数据用于RGB图像生成
2025-05-20 09:37:36,658 - data_manager - INFO - 为源G000.120-00.556加载多波段WISE数据: ('3.4', '12', '22')
2025-05-20 09:37:36,663 - data_manager - INFO - 为源G000.120-00.556加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.120-00.556
2025-05-20 09:37:36,663 - data_manager - INFO - 目录中的所有文件: ['G000.120-00.556_ATLASGAL_870um.fits', 'G000.120-00.556_IRIS_100.fits', 'G000.120-00.556_NVSS.fits', 'G000.120-00.556_WISE_12.fits', 'G000.120-00.556_WISE_22.fits', 'G000.120-00.556_WISE_3.4.fits', 'G000.120-00.556_WISE_4.6.fits']
2025-05-20 09:37:36,663 - data_manager - INFO - 查找WISE 3.4μm波段的FITS文件
2025-05-20 09:37:36,663 - data_manager - INFO - 第一次匹配结果: ['G000.120-00.556_WISE_3.4.fits']
2025-05-20 09:37:36,664 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.120-00.556\G000.120-00.556_WISE_3.4.fits
2025-05-20 09:37:36,676 - data_manager - INFO - FITS数据统计: 最小值=-2.8628005981445312, 最大值=4364.04150390625, 均值=534.0675659179688, 中位数=421.4853820800781
2025-05-20 09:37:36,676 - data_manager - INFO - 有效数据点数量: 348091/348100 (100.00%)
2025-05-20 09:37:36,684 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (590, 590)
2025-05-20 09:37:36,684 - data_manager - INFO - 使用3.4μm波段的WCS作为参考
2025-05-20 09:37:36,685 - data_manager - INFO - 成功加载WISE 3.4μm波段数据，尺寸: (590, 590)
2025-05-20 09:37:36,690 - data_manager - INFO - 为源G000.120-00.556加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.120-00.556
2025-05-20 09:37:36,690 - data_manager - INFO - 目录中的所有文件: ['G000.120-00.556_ATLASGAL_870um.fits', 'G000.120-00.556_IRIS_100.fits', 'G000.120-00.556_NVSS.fits', 'G000.120-00.556_WISE_12.fits', 'G000.120-00.556_WISE_22.fits', 'G000.120-00.556_WISE_3.4.fits', 'G000.120-00.556_WISE_4.6.fits']
2025-05-20 09:37:36,690 - data_manager - INFO - 查找WISE 12μm波段的FITS文件
2025-05-20 09:37:36,691 - data_manager - INFO - 第一次匹配结果: ['G000.120-00.556_WISE_12.fits']
2025-05-20 09:37:36,691 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.120-00.556\G000.120-00.556_WISE_12.fits
2025-05-20 09:37:36,699 - data_manager - INFO - FITS数据统计: 最小值=180.14215087890625, 最大值=10206.8095703125, 均值=2248.91357421875, 中位数=1937.759521484375
2025-05-20 09:37:36,699 - data_manager - INFO - 有效数据点数量: 303807/305809 (99.35%)
2025-05-20 09:37:36,707 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (553, 553)
2025-05-20 09:37:36,707 - data_manager - INFO - 成功加载WISE 12μm波段数据，尺寸: (553, 553)
2025-05-20 09:37:36,713 - data_manager - INFO - 为源G000.120-00.556加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G000.120-00.556
2025-05-20 09:37:36,713 - data_manager - INFO - 目录中的所有文件: ['G000.120-00.556_ATLASGAL_870um.fits', 'G000.120-00.556_IRIS_100.fits', 'G000.120-00.556_NVSS.fits', 'G000.120-00.556_WISE_12.fits', 'G000.120-00.556_WISE_22.fits', 'G000.120-00.556_WISE_3.4.fits', 'G000.120-00.556_WISE_4.6.fits']
2025-05-20 09:37:36,713 - data_manager - INFO - 查找WISE 22μm波段的FITS文件
2025-05-20 09:37:36,713 - data_manager - INFO - 第一次匹配结果: ['G000.120-00.556_WISE_22.fits']
2025-05-20 09:37:36,713 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G000.120-00.556\G000.120-00.556_WISE_22.fits
2025-05-20 09:37:36,717 - data_manager - INFO - FITS数据统计: 最小值=330.3290710449219, 最大值=2674.807373046875, 均值=478.0859680175781, 中位数=389.5059814453125
2025-05-20 09:37:36,717 - data_manager - INFO - 有效数据点数量: 89256/90000 (99.17%)
2025-05-20 09:37:36,725 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (300, 300)
2025-05-20 09:37:36,726 - data_manager - INFO - 成功加载WISE 22μm波段数据，尺寸: (300, 300)
2025-05-20 09:37:36,726 - data_manager - INFO - 成功加载多波段WISE数据，波段: ['w1', 'w3', 'w4']
2025-05-20 09:37:36,726 - visualizer - INFO - 创建WISE三波段RGB图像，输出到: results/batch_single_folder\visualizations\G000.120-00.556_wise_rgb.png
2025-05-20 09:37:36,857 - visualizer - INFO - 使用WCS坐标系统: fk5
2025-05-20 09:37:36,857 - visualizer - INFO - 波段 3.4μm 的尺寸: (590, 590)
2025-05-20 09:37:36,858 - visualizer - INFO - 波段 12μm 的尺寸: (553, 553)
2025-05-20 09:37:36,858 - visualizer - INFO - 波段 22μm 的尺寸: (300, 300)
2025-05-20 09:37:36,858 - visualizer - INFO - Using 12μm band size as target: (553, 553)
2025-05-20 09:37:36,858 - visualizer - INFO - Using target image size: (553, 553), original sizes: {'w1': (590, 590), 'w3': (553, 553), 'w4': (300, 300)}
2025-05-20 09:37:36,897 - visualizer - INFO - 调整w4波段数据从(300, 300)到(553, 553)
2025-05-20 09:37:36,898 - visualizer - INFO - 红色通道(22μm)数据范围: 3.0609091936639743e-06-2674.807373046875
2025-05-20 09:37:36,902 - visualizer - INFO - 绿色通道(12μm)数据范围: 180.14215087890625-10206.8095703125
2025-05-20 09:37:36,960 - visualizer - INFO - 调整w1波段数据从(590, 590)到(553, 553)
2025-05-20 09:37:36,961 - visualizer - INFO - 蓝色通道(3.4μm)数据范围: 0.061346251517534256-3973.828125
2025-05-20 09:37:36,972 - visualizer - INFO - 使用Lupton RGB方法，stretch=4004.29, Q=10
2025-05-20 09:37:36,972 - visualizer - INFO - 各波段99.5%百分位数: R=2021.05, G=8008.59, B=2026.47
2025-05-20 09:37:37,028 - visualizer - INFO - RGB数据形状: (553, 553, 3), 类型: float32
2025-05-20 09:37:37,030 - visualizer - INFO - Red通道数据范围：0.0-0.45490196347236633，均值：0.0808
2025-05-20 09:37:37,031 - visualizer - INFO - Green通道数据范围：0.0-0.8941176533699036，均值：0.3844
2025-05-20 09:37:37,032 - visualizer - INFO - Blue通道数据范围：0.0-0.6078431606292725，均值：0.0914
2025-05-20 09:37:38,482 - visualizer - INFO - 将中心坐标从ICRS转换为fk5以绘制HII区域圆
2025-05-20 09:37:38,489 - visualizer - INFO - 成功绘制HII区域圆
2025-05-20 09:37:38,540 - visualizer - INFO - 按照要求不显示Gaia星
2025-05-20 09:37:38,542 - visualizer - INFO - 设置坐标刻度间隔: RA=10.000度, Dec=10.000度
2025-05-20 09:37:39,658 - visualizer - INFO - 成功保存RGB图像到: results/batch_single_folder\visualizations\G000.120-00.556_wise_rgb.png
2025-05-20 09:37:39,660 - main_single_folder - INFO - 成功保存WISE RGB图像到: results/batch_single_folder\visualizations\G000.120-00.556_wise_rgb.png
2025-05-20 09:37:39,660 - main_single_folder - WARNING - 没有足够的数据绘制消光-距离散点图
2025-05-20 09:37:39,676 - main_single_folder - INFO - 成功保存处理报告到: results/batch_single_folder\reports\G000.120-00.556_report.txt
2025-05-20 09:37:39,676 - main_single_folder - INFO - 处理G000.120-00.556完成
