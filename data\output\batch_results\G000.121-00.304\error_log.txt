处理时间: 2025-04-24 04:38:00
耗时: 3.97秒

返回码: 1

标准输出:
错误: 未找到WISE W3 FITS文件


标准错误:
2025-04-24 04:37:59,817 - main - INFO - 输出目录: data/output/batch_results\G000.121-00.304
2025-04-24 04:37:59,817 - main - INFO - 加载源G000.121-00.304的信息
2025-04-24 04:37:59,817 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
H:\Augment\Parallax distances\src\data_manager.py:43: FutureWarning: The 'delim_whitespace' keyword in pd.read_csv is deprecated and will be removed in a future version. Use ``sep='\s+'`` instead
  df = pd.read_csv(catalog_path, delim_whitespace=True, comment='#', header=None, names=column_names)
2025-04-24 04:37:59,823 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-04-24 04:37:59,824 - main - ERROR - 在U:/Data/Bubbles/Wise bubbles\G000.121-00.304中未找到WISE W3 FITS文件
2025-04-24 04:37:59,825 - main - ERROR - 加载源信息失败: 未找到WISE W3 FITS文件
2025-04-24 04:37:59,825 - main - ERROR - 处理失败: 未找到WISE W3 FITS文件
Traceback (most recent call last):
  File "H:\Augment\Parallax distances\main.py", line 423, in main
    results = process_hii_region(args)
              ^^^^^^^^^^^^^^^^^^^^^^^^
  File "H:\Augment\Parallax distances\main.py", line 199, in process_hii_region
    source_info = load_source_info(args.source, args, config)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "H:\Augment\Parallax distances\main.py", line 133, in load_source_info
    raise FileNotFoundError(f"未找到WISE W3 FITS文件")
FileNotFoundError: 未找到WISE W3 FITS文件
