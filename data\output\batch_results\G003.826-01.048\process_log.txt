处理时间: 2025-04-24 05:00:21
耗时: 6.30秒

标准输出:

未能估计G003.826-01.048的分子云距离


标准错误:
2025-04-24 05:00:19,112 - main - INFO - 输出目录: data/output/batch_results\G003.826-01.048
2025-04-24 05:00:19,112 - main - INFO - 加载源G003.826-01.048的信息
2025-04-24 05:00:19,112 - data_manager - INFO - 加载HII区域源表: H:/Augment/Parallax distances/Parallax-based distances.dat
H:\Augment\Parallax distances\src\data_manager.py:43: FutureWarning: The 'delim_whitespace' keyword in pd.read_csv is deprecated and will be removed in a future version. Use ``sep='\s+'`` instead
  df = pd.read_csv(catalog_path, delim_whitespace=True, comment='#', header=None, names=column_names)
2025-04-24 05:00:19,118 - data_manager - INFO - 成功加载HII区域源表，共459条记录
2025-04-24 05:00:19,120 - main - INFO - 目录中的所有文件: ['G003.826-01.048_ATLASGAL_870um.fits', 'G003.826-01.048_IRIS_100.fits', 'G003.826-01.048_NVSS.fits', 'G003.826-01.048_WISE_12.fits', 'G003.826-01.048_WISE_22.fits', 'G003.826-01.048_WISE_3.4.fits', 'G003.826-01.048_WISE_4.6.fits']
2025-04-24 05:00:19,120 - main - INFO - 第一次匹配结果: ['G003.826-01.048_WISE_12.fits']
2025-04-24 05:00:19,120 - main - INFO - 从FITS文件获取坐标: U:/Data/Bubbles/Wise bubbles\G003.826-01.048\G003.826-01.048_WISE_12.fits
2025-04-24 05:00:19,122 - main - INFO - 从FITS头信息获取坐标系统: fk5
2025-04-24 05:00:19,126 - main - INFO - 从FITS头信息获取坐标: RA=269.621992, Dec=-26.178706 (ICRS)
2025-04-24 05:00:19,126 - main - INFO - 使用有效半径161.0角秒 (0.044722度)
2025-04-24 05:00:19,126 - main - INFO - 从源表加载信息: RA=269.621992, Dec=-26.178706, R_eff=0.044722度
2025-04-24 05:00:19,127 - main - INFO - 步骤1：加载WISE数据
2025-04-24 05:00:19,130 - data_manager - INFO - 为源G003.826-01.048加载WISE FITS图像，路径: U:/Data/Bubbles/Wise bubbles\G003.826-01.048
2025-04-24 05:00:19,131 - data_manager - INFO - 目录中的所有文件: ['G003.826-01.048_ATLASGAL_870um.fits', 'G003.826-01.048_IRIS_100.fits', 'G003.826-01.048_NVSS.fits', 'G003.826-01.048_WISE_12.fits', 'G003.826-01.048_WISE_22.fits', 'G003.826-01.048_WISE_3.4.fits', 'G003.826-01.048_WISE_4.6.fits']
2025-04-24 05:00:19,131 - data_manager - INFO - 第一次匹配结果: ['G003.826-01.048_WISE_12.fits']
2025-04-24 05:00:19,131 - data_manager - INFO - 使用WISE FITS文件: U:/Data/Bubbles/Wise bubbles\G003.826-01.048\G003.826-01.048_WISE_12.fits
2025-04-24 05:00:19,140 - data_manager - INFO - 成功加载WISE FITS图像，尺寸: (247, 247)
2025-04-24 05:00:19,140 - main - INFO - WISE数据加载完成
2025-04-24 05:00:19,140 - main - INFO - 步骤2：区域映射
2025-04-24 05:00:19,141 - region_mapper - INFO - 处理WISE图像，平滑sigma=1.0
2025-04-24 05:00:19,141 - region_mapper - INFO - 替换图像中的NaN值
2025-04-24 05:00:19,142 - region_mapper - INFO - 应用高斯平滑
2025-04-24 05:00:19,144 - region_mapper - INFO - 估计并减除背景
2025-04-24 05:00:19,155 - region_mapper - INFO - WISE图像处理完成
2025-04-24 05:00:19,155 - region_mapper - INFO - 定义PDR掩模，阈值因子=0.5，最大半径因子=3.0
2025-04-24 05:00:19,156 - region_mapper - INFO - WCS坐标系统: fk5
2025-04-24 05:00:19,156 - region_mapper - INFO - 将中心坐标从ICRS转换为fk5
2025-04-24 05:00:19,161 - region_mapper - INFO - 中心坐标 (RA=269.621992, Dec=-26.178706) 对应像素坐标 (X=123.0, Y=123.0)
2025-04-24 05:00:19,165 - region_mapper - INFO - 像素尺度: 495.64 像素/角秒
2025-04-24 05:00:19,165 - region_mapper - INFO - 最大搜索半径: 1.0 像素
2025-04-24 05:00:19,166 - region_mapper - INFO - PDR阈值: 0.00 (峰值的50%)
2025-04-24 05:00:19,187 - region_mapper - INFO - PDR掩模创建完成，覆盖1个像素
2025-04-24 05:00:19,188 - region_mapper - INFO - 定义空腔掩模，有效半径=0.04472222222222222度
2025-04-24 05:00:19,195 - region_mapper - INFO - 有效半径: 0.3 像素
2025-04-24 05:00:19,195 - region_mapper - INFO - 空腔掩模创建完成，覆盖0个像素
2025-04-24 05:00:19,196 - region_mapper - INFO - 定义外部区域掩模，最大半径因子=5.0
2025-04-24 05:00:19,202 - region_mapper - INFO - 最大半径: 1.6 像素
2025-04-24 05:00:19,202 - region_mapper - INFO - 外部区域掩模创建完成，覆盖8个像素
2025-04-24 05:00:19,203 - region_mapper - INFO - 保存区域掩模到: data/output/batch_results\G003.826-01.048\processed\G003.826-01.048_region_masks.npz
2025-04-24 05:00:19,205 - region_mapper - INFO - 成功保存区域掩模
2025-04-24 05:00:19,209 - data_manager - INFO - 为源G003.826-01.048加载Gaia数据，中心坐标: RA=269.621992, Dec=-26.178706, 半径=0.2236度
2025-04-24 05:00:19,212 - data_manager - INFO - 找到Gaia数据文件: H:/Cursor/Parallax distances/gaia_data\gaia_G003.826-01.048_13arcmin_5R.csv
2025-04-24 05:00:19,885 - data_manager - INFO - 成功加载Gaia数据，共39199条记录
2025-04-24 05:00:19,941 - data_manager - INFO - 成功加载Gaia数据，共38991个源在搜索半径内
2025-04-24 05:00:19,953 - region_mapper - INFO - 为38991个恒星分配区域标签
2025-04-24 05:00:19,953 - region_mapper - INFO - WCS坐标系统: fk5
2025-04-24 05:00:19,953 - region_mapper - INFO - 将恒星坐标从ICRS转换为fk5
2025-04-24 05:00:20,173 - region_mapper - INFO - 区域标签分配完成: 空腔=0, PDR=0, 外部=1, 区域外=38990
2025-04-24 05:00:20,175 - data_manager - INFO - 保存处理后的数据到: data/output/batch_results\G003.826-01.048\processed\G003.826-01.048_gaia_regions.fits
2025-04-24 05:00:21,102 - data_manager - INFO - 成功保存数据
2025-04-24 05:00:21,102 - main - INFO - 区域映射完成
2025-04-24 05:00:21,102 - main - INFO - 步骤3：恒星选择
2025-04-24 05:00:21,106 - star_selector - INFO - 应用Gaia质量筛选，参数: {'parallax_snr_min': 5.0, 'ruwe_max': 1.4}
2025-04-24 05:00:21,112 - star_selector - INFO - 视差信噪比筛选: 保留2466/38991个源
2025-04-24 05:00:21,114 - star_selector - INFO - RUWE筛选: 保留37744/38991个源
2025-04-24 05:00:21,142 - star_selector - INFO - 质量筛选完成，保留2328/38991个源
2025-04-24 05:00:21,146 - star_selector - INFO - 基于WISE颜色识别YSO，参数: {'w1w2_min': 0.8}
2025-04-24 05:00:21,146 - star_selector - WARNING - 表中缺少WISE颜色数据，无法识别YSO
2025-04-24 05:00:21,146 - star_selector - INFO - 筛选恒星样本，原始样本大小: 2328
2025-04-24 05:00:21,146 - star_selector - INFO - 排除YSO后: 2328/2328个源
2025-04-24 05:00:21,156 - star_selector - INFO - 区域 cavity: 0个源
2025-04-24 05:00:21,157 - star_selector - INFO - 区域 pdr: 0个源
2025-04-24 05:00:21,157 - star_selector - INFO - 区域 external: 0个源
2025-04-24 05:00:21,157 - star_selector - INFO - 恒星样本筛选完成，最终样本大小: 2328
2025-04-24 05:00:21,158 - data_manager - INFO - 保存处理后的数据到: data/output/batch_results\G003.826-01.048\processed\G003.826-01.048_star_sample.fits
2025-04-24 05:00:21,283 - data_manager - INFO - 成功保存数据
2025-04-24 05:00:21,284 - main - INFO - 恒星选择完成
2025-04-24 05:00:21,284 - main - INFO - 步骤4：消光计算
2025-04-24 05:00:21,288 - extinction_estimator - INFO - 为2328个恒星计算A_V，参数: {'rv': 3.1}
2025-04-24 05:00:21,307 - extinction_estimator - INFO - 使用2MASS H-K颜色计算A_V
2025-04-24 05:00:21,307 - extinction_estimator - INFO - 估计本征H-K颜色，方法: select_giants
2025-04-24 05:00:21,307 - extinction_estimator - INFO - 使用红巨星的典型H-K颜色: 0.15
2025-04-24 05:00:21,308 - extinction_estimator - INFO - 基于H-K颜色过量计算A_V，R_V=3.1
2025-04-24 05:00:21,309 - extinction_estimator - INFO - 计算得到1607个A_V值，范围: 0.00-42.26
2025-04-24 05:00:21,309 - extinction_estimator - INFO - 使用Gaia BP-RP颜色计算A_V
2025-04-24 05:00:21,310 - extinction_estimator - INFO - 估计本征BP-RP颜色，方法: fixed_value
2025-04-24 05:00:21,310 - extinction_estimator - INFO - 使用固定的BP-RP颜色: 0.8
2025-04-24 05:00:21,310 - extinction_estimator - INFO - 基于Gaia BP-RP颜色过量计算A_V，R_V=3.1
2025-04-24 05:00:21,311 - extinction_estimator - INFO - 计算得到2292个A_V值，范围: 0.00-11.20
2025-04-24 05:00:21,313 - extinction_estimator - INFO - 方法 2MASS_HK: 1607个源
2025-04-24 05:00:21,313 - extinction_estimator - INFO - 方法 Gaia_BPRP: 692个源
2025-04-24 05:00:21,313 - extinction_estimator - INFO - 方法 unknown: 29个源
2025-04-24 05:00:21,313 - extinction_estimator - INFO - A_V计算完成，范围: 0.00-42.26
2025-04-24 05:00:21,314 - data_manager - INFO - 保存处理后的数据到: data/output/batch_results\G003.826-01.048\processed\G003.826-01.048_stars_with_av.fits
2025-04-24 05:00:21,436 - data_manager - INFO - 成功保存数据
2025-04-24 05:00:21,436 - main - INFO - 消光计算完成
2025-04-24 05:00:21,436 - main - INFO - 步骤5：距离分析
2025-04-24 05:00:21,437 - distance_analyzer - INFO - 分析所有区域的消光-距离关系
2025-04-24 05:00:21,437 - distance_analyzer - INFO - 将分析0个区域: 
2025-04-24 05:00:21,438 - distance_analyzer - INFO - 估计分子云距离
2025-04-24 05:00:21,438 - distance_analyzer - WARNING - 缺少必要的区域数据: cavity, pdr, external
2025-04-24 05:00:21,438 - distance_analyzer - WARNING - 没有检测到任何跳变，无法估计距离
2025-04-24 05:00:21,438 - distance_analyzer - INFO - 保存分析结果到: data/output/batch_results\G003.826-01.048\G003.826-01.048_results.txt
2025-04-24 05:00:21,439 - distance_analyzer - INFO - 成功保存分析结果
2025-04-24 05:00:21,439 - main - INFO - 距离分析完成
2025-04-24 05:00:21,439 - main - INFO - 处理G003.826-01.048完成
