SIMPLE  =                    T / conforms to FITS standard                      BITPIX  =                    8 / array data type                                NAXIS   =                    0 / number of array dimensions                     EXTEND  =                    T                                                  END                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             XTENSION= 'BINTABLE'           / binary table extension                         BITPIX  =                    8 / array data type                                NAXIS   =                    2 / number of array dimensions                     NAXIS1  =                  168 / length of dimension 1                          NAXIS2  =                    0 / length of dimension 2                          PCOUNT  =                    0 / number of group parameters                     GCOUNT  =                    1 / number of groups                               TFIELDS =                   21 / number of table fields                         TTYPE1  = 'ra      '                                                            TFORM1  = 'D       '                                                            TTYPE2  = 'dec     '                                                            TFORM2  = 'D       '                                                            TTYPE3  = 'source_id'                                                           TFORM3  = 'K       '                                                            TTYPE4  = 'parallax'                                                            TFORM4  = 'D       '                                                            TTYPE5  = 'parallax_error'                                                      TFORM5  = 'D       '                                                            TTYPE6  = 'ruwe    '                                                            TFORM6  = 'D       '                                                            TTYPE7  = 'phot_g_mean_mag'                                                     TFORM7  = 'D       '                                                            TTYPE8  = 'BPmag   '                                                            TFORM8  = 'D       '                                                            TTYPE9  = 'RPmag   '                                                            TFORM9  = 'D       '                                                            TTYPE10 = 'bp_rp   '                                                            TFORM10 = 'D       '                                                            TTYPE11 = 'j_m     '                                                            TFORM11 = 'D       '                                                            TTYPE12 = 'h_m     '                                                            TFORM12 = 'D       '                                                            TTYPE13 = 'k_m     '                                                            TFORM13 = 'D       '                                                            TTYPE14 = 'j_ks    '                                                            TFORM14 = 'D       '                                                            TTYPE15 = 'j_h     '                                                            TFORM15 = 'D       '                                                            TTYPE16 = 'h_k     '                                                            TFORM16 = 'D       '                                                            TTYPE17 = 'Dist    '                                                            TFORM17 = 'D       '                                                            TTYPE18 = 'pmra    '                                                            TFORM18 = 'D       '                                                            TTYPE19 = 'pmde    '                                                            TFORM19 = 'D       '                                                            TTYPE20 = 'distance'                                                            TFORM20 = 'D       '                                                            TTYPE21 = 'region  '                                                            TFORM21 = '8A      '                                                            END                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             