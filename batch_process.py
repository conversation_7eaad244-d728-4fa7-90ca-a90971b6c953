"""
批量处理HII区源

处理多个HII区源，生成PDR区域掩模和等高线图像。
"""

import os
import sys
import argparse
import pandas as pd
from tqdm import tqdm
import subprocess
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_process.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('batch_process')

def load_hii_region_catalog(catalog_path):
    """
    加载HII区源表

    Args:
        catalog_path: 源表路径

    Returns:
        pandas.DataFrame: 源表数据
    """
    try:
        # 尝试读取源表
        with open(catalog_path, 'r') as f:
            lines = f.readlines()

        # 手动解析源表
        data = []
        for line in lines:
            if line.strip() and not line.startswith('#'):
                parts = line.strip().split()
                if len(parts) >= 3:
                    source_name = parts[0]  # 源名称
                    quality = parts[1]      # 质量标记 (K/Q)
                    try:
                        N = float(parts[2])  # 有效半径（角秒）
                        distance = float(parts[3])  # 距离（kpc）
                        distance_error_min = float(parts[4]) if len(parts) > 4 else 0.0  # 距离误差下限
                        distance_error_max = float(parts[5]) if len(parts) > 5 else 0.0  # 距离误差上限
                        data.append([source_name, quality, N, distance, distance_error_min, distance_error_max])
                    except ValueError:
                        continue

        # 创建DataFrame
        catalog = pd.DataFrame(data, columns=['source_name', 'quality', 'N', 'distance',
                                             'distance_error_min', 'distance_error_max'])
        logger.info(f"成功加载源表，共{len(catalog)}个源")
        return catalog
    except Exception as e:
        logger.error(f"加载源表失败: {str(e)}")
        raise

def process_source(source_name, output_dir=None, config_path=None):
    """
    处理单个HII区源

    Args:
        source_name: 源名称
        output_dir: 输出目录
        config_path: 配置文件路径

    Returns:
        bool: 处理是否成功
    """
    try:
        # 构建命令
        cmd = ['python', 'main.py', '--source', source_name]

        if output_dir:
            cmd.extend(['--output-dir', output_dir])

        if config_path:
            cmd.extend(['--config', config_path])

        # 执行命令
        logger.info(f"开始处理源 {source_name}")
        logger.info(f"执行命令: {' '.join(cmd)}")

        result = subprocess.run(cmd, capture_output=True, text=True)

        # 检查结果
        if result.returncode == 0:
            logger.info(f"源 {source_name} 处理成功")
            return True
        else:
            logger.error(f"源 {source_name} 处理失败: {result.stderr}")
            return False

    except Exception as e:
        logger.error(f"处理源 {source_name} 时发生错误: {str(e)}")
        return False

def batch_process(catalog_path, num_sources=10, output_base_dir='results', config_path=None):
    """
    批量处理HII区源

    Args:
        catalog_path: 源表路径
        num_sources: 要处理的源数量
        output_base_dir: 输出基目录
        config_path: 配置文件路径

    Returns:
        tuple: (成功数量, 失败数量)
    """
    # 加载源表
    catalog = load_hii_region_catalog(catalog_path)

    # 限制处理的源数量
    sources_to_process = catalog.iloc[:num_sources]

    # 创建输出基目录
    os.makedirs(output_base_dir, exist_ok=True)

    # 处理结果统计
    success_count = 0
    failed_count = 0
    failed_sources = []

    # 批量处理
    logger.info(f"开始批量处理{len(sources_to_process)}个源")

    for i, (_, row) in enumerate(tqdm(sources_to_process.iterrows(), total=len(sources_to_process))):
        source_name = row['source_name'].strip()

        # 创建源专用输出目录
        source_output_dir = os.path.join(output_base_dir, source_name)
        os.makedirs(source_output_dir, exist_ok=True)

        # 处理源
        success = process_source(source_name, source_output_dir, config_path)

        if success:
            success_count += 1
        else:
            failed_count += 1
            failed_sources.append(source_name)

    # 输出处理结果
    logger.info(f"批量处理完成: 成功 {success_count}, 失败 {failed_count}")

    if failed_sources:
        logger.info(f"失败的源: {', '.join(failed_sources)}")

    return success_count, failed_count

def parse_arguments():
    """
    解析命令行参数

    Returns:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='批量处理HII区源')

    parser.add_argument('--catalog', type=str,
                       default='H:/Augment/Parallax distances/Parallax-based distances.dat',
                       help='HII区源表路径')

    parser.add_argument('--num-sources', type=int, default=10,
                       help='要处理的源数量')

    parser.add_argument('--output-dir', type=str, default='results',
                       help='输出基目录')

    parser.add_argument('--config', type=str, default='config/default_config.yaml',
                       help='配置文件路径')

    return parser.parse_args()

if __name__ == '__main__':
    # 解析命令行参数
    args = parse_arguments()

    # 批量处理
    success_count, failed_count = batch_process(
        args.catalog, args.num_sources, args.output_dir, args.config
    )

    # 输出处理结果
    print(f"批量处理完成: 成功 {success_count}, 失败 {failed_count}")

    # 设置退出码
    sys.exit(0 if failed_count == 0 else 1)
